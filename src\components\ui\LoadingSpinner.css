/**
 * LoadingSpinner 组件样式
 * 实现呼吸式加载动画
 */

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--space-md);
  padding: var(--space-lg);
}

.loading-spinner__dots {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.loading-spinner__dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--color-text-tertiary);
  display: block;
}

.loading-spinner__text {
  font-family: var(--font-sans-serif);
  font-size: 14px;
  line-height: 24px;
  color: var(--color-text-tertiary);
  margin: 0;
  text-align: center;
}

/* 尺寸变体 */
.loading-spinner--small .loading-spinner__dot {
  width: 6px;
  height: 6px;
}

.loading-spinner--small .loading-spinner__text {
  font-size: 12px;
  line-height: 20px;
}

.loading-spinner--large .loading-spinner__dot {
  width: 12px;
  height: 12px;
}

.loading-spinner--large .loading-spinner__text {
  font-size: 16px;
  line-height: 28px;
}

/* 居中布局变体 */
.loading-spinner--centered {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

/* 全屏覆盖变体 */
.loading-spinner--overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(249, 247, 243, 0.9);
  backdrop-filter: blur(4px);
  z-index: 1000;
}
