/**
 * Button 组件样式
 * 实现按钮的设计规范
 */

.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-sans-serif);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-default);
  position: relative;
  overflow: hidden;
}

.button__content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-xs);
}

/* 主按钮样式 */
.button--primary {
  background-color: var(--color-accent-romantic);
  color: var(--color-background-primary);
  border: 1px solid var(--color-accent-romantic);
}

.button--primary:hover {
  background-color: #A55A52; /* 背景变暗 5% */
  border-color: #A55A52;
}

.button--primary:active {
  background-color: #954F47; /* 背景变暗 10% */
  border-color: #954F47;
}

/* 次按钮样式 */
.button--secondary {
  background-color: transparent;
  color: var(--color-accent-romantic);
  border: 1px solid var(--color-accent-romantic);
}

.button--secondary:hover {
  background-color: var(--color-alpha-romantic-10);
}

.button--secondary:active {
  background-color: var(--color-alpha-romantic-20);
}

/* 尺寸变体 */
.button--small {
  font-size: 14px;
  line-height: 20px;
  padding: var(--space-xs) var(--space-sm);
  min-height: 32px;
}

.button--medium {
  font-size: 16px;
  line-height: 24px;
  padding: 12px var(--space-md);
  min-height: 40px;
}

.button--large {
  font-size: 18px;
  line-height: 28px;
  padding: var(--space-sm) var(--space-lg);
  min-height: 48px;
}

/* 禁用状态 */
.button--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 焦点状态 */
.button:focus-visible {
  outline: 2px solid var(--color-accent-romantic);
  outline-offset: 2px;
}

/* 加载状态（可选） */
.button--loading {
  pointer-events: none;
}

.button--loading .button__content {
  opacity: 0.7;
}

/* 图标按钮变体 */
.button--icon-only {
  padding: var(--space-xs);
  min-width: 40px;
  min-height: 40px;
}

.button--icon-only.button--small {
  min-width: 32px;
  min-height: 32px;
  padding: 6px;
}

.button--icon-only.button--large {
  min-width: 48px;
  min-height: 48px;
  padding: 12px;
}

/* 全宽按钮 */
.button--full-width {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 767px) {
  .button--medium {
    font-size: 15px;
    padding: 10px var(--space-sm);
    min-height: 36px;
  }
  
  .button--large {
    font-size: 16px;
    padding: 12px var(--space-md);
    min-height: 44px;
  }
}
