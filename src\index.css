/**
 * 《墨韵流光》全局样式
 * 基于设计规范的全局重置和基础样式
 */

/* 导入字体 */
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;700&display=swap');

/* CSS 自定义属性 */
:root {
  /* 色彩系统 */
  --color-background-primary: #F9F7F3;
  --color-background-secondary: #F4F1ED;
  --color-text-primary: #332E2B;
  --color-text-secondary: #45403D;
  --color-text-tertiary: #9A938E;
  --color-accent-romantic: #B5655D;
  --color-border-primary: #EAE6E1;
  --color-overlay-dark: rgba(29, 26, 25, 0.9);
  --color-overlay-mobile-menu: rgba(0, 0, 0, 0.4);
  --color-alpha-romantic-10: rgba(181, 101, 93, 0.1);
  --color-alpha-romantic-20: rgba(181, 101, 93, 0.2);
  --color-alpha-background-85: rgba(249, 247, 243, 0.85);

  /* 间距系统 */
  --space-xs: 8px;
  --space-sm: 16px;
  --space-md: 24px;
  --space-lg: 48px;
  --space-xl: 64px;
  --space-xxl: 96px;
  --content-side-padding: 120px;
  --mobile-content-padding: 24px;
  --header-height: 64px;
  --sidebar-max-width: 280px;
  --article-max-width: 720px;

  /* 字体系统 */
  --font-serif: "Source Han Serif SC", "思源宋体", Georgia, serif;
  --font-sans-serif: "Source Han Sans SC", "思源黑体", "PingFang SC", -apple-system, BlinkMacSystemFont, sans-serif;
  --font-mono: "JetBrains Mono", monospace;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;
  --baseline-grid: 8px;

  /* 动效系统 */
  --easing-default: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --duration-fast: 150ms;
  --duration-medium: 250ms;
  --duration-slow: 300ms;
  --duration-slower: 500ms;

  /* 响应式断点 */
  --breakpoint-mobile: 768px;
  --breakpoint-tablet: 1200px;
  --breakpoint-desktop: 1200px;
}

/* 全局重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-sans-serif);
  font-size: 17px;
  line-height: 32px;
  color: var(--color-text-secondary);
  background-color: var(--color-background-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-serif);
  font-weight: var(--font-weight-regular);
  color: var(--color-text-primary);
  margin: 0;
}

h1 {
  font-size: 48px;
  line-height: 64px;
}

h2 {
  font-size: 32px;
  line-height: 48px;
}

h3 {
  font-size: 24px;
  line-height: 32px;
}

/* 段落和文本 */
p {
  margin: 0;
  line-height: 32px;
}

/* 链接基础样式 */
a {
  color: inherit;
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color var(--duration-fast) var(--easing-default);
}

/* 列表 */
ul, ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

/* 图片 */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* 按钮重置 */
button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
  font-size: inherit;
  color: inherit;
}

/* 输入框重置 */
input, textarea {
  border: none;
  outline: none;
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  background: transparent;
}

/* 代码样式 */
code, pre {
  font-family: var(--font-mono);
  font-size: 15px;
  line-height: 24px;
}

/* 引用样式 */
blockquote {
  font-style: italic;
  font-size: 18px;
  line-height: 32px;
  margin: 0;
}

/* 选择文本样式 */
::selection {
  background-color: var(--color-alpha-romantic-20);
  color: var(--color-text-primary);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-primary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-tertiary);
}
