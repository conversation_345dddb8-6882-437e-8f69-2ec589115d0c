/**
 * Lightbox 组件样式
 * 实现画廊模式的设计规范
 */

.lightbox {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-lg);
}

.lightbox__backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-overlay-dark);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.lightbox__close {
  position: absolute;
  top: var(--space-lg);
  right: var(--space-lg);
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  color: #FFFFFF;
  font-size: 24px;
  line-height: 1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--duration-fast) var(--easing-default);
  z-index: 2001;
}

.lightbox__close:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.lightbox__container {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2001;
}

.lightbox__image {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.lightbox__caption {
  margin-top: var(--space-md);
  padding: var(--space-sm) var(--space-md);
  background: rgba(0, 0, 0, 0.7);
  color: #FFFFFF;
  font-family: var(--font-sans-serif);
  font-size: 14px;
  line-height: 24px;
  border-radius: 4px;
  text-align: center;
  max-width: 500px;
}

/* 导航按钮 */
.lightbox__nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 56px;
  height: 56px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  color: #FFFFFF;
  font-size: 32px;
  line-height: 1;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--duration-fast) var(--easing-default);
  z-index: 2001;
}

.lightbox__nav:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.lightbox__nav--prev {
  left: var(--space-lg);
}

.lightbox__nav--next {
  right: var(--space-lg);
}

/* 图片计数器 */
.lightbox__counter {
  position: absolute;
  bottom: var(--space-lg);
  left: 50%;
  transform: translateX(-50%);
  padding: var(--space-xs) var(--space-sm);
  background: rgba(0, 0, 0, 0.7);
  color: #FFFFFF;
  font-family: var(--font-sans-serif);
  font-size: 12px;
  line-height: 20px;
  border-radius: 12px;
  z-index: 2001;
}

/* 响应式设计 */
@media (max-width: 767px) {
  .lightbox {
    padding: var(--space-md);
  }
  
  .lightbox__close {
    top: var(--space-md);
    right: var(--space-md);
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
  
  .lightbox__nav {
    width: 48px;
    height: 48px;
    font-size: 24px;
  }
  
  .lightbox__nav--prev {
    left: var(--space-md);
  }
  
  .lightbox__nav--next {
    right: var(--space-md);
  }
  
  .lightbox__counter {
    bottom: var(--space-md);
  }
  
  .lightbox__image {
    max-height: 70vh;
  }
  
  .lightbox__caption {
    font-size: 13px;
    line-height: 20px;
    margin-top: var(--space-sm);
    padding: var(--space-xs) var(--space-sm);
  }
}
