/**
 * MarkdownRenderer 组件样式
 * 确保Markdown渲染内容完全符合《墨韵流光》设计规范
 */

/* 导入代码高亮样式 */
@import url('https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css');

.markdown-renderer {
  font-family: var(--font-sans-serif);
  font-size: 17px;
  line-height: 32px;
  color: var(--color-text-secondary);
}

/* 标题样式 - 严格遵循设计规范 */
.markdown-h1 {
  font-family: var(--font-serif);
  font-size: 48px;
  line-height: 64px;
  color: var(--color-text-primary);
  font-weight: var(--font-weight-regular);
  margin: var(--space-xl) 0 var(--space-lg);
}

.markdown-h2 {
  font-family: var(--font-serif);
  font-size: 32px;
  line-height: 48px;
  color: var(--color-text-primary);
  font-weight: var(--font-weight-regular);
  margin: var(--space-xl) 0 var(--space-lg);
}

.markdown-h3 {
  font-family: var(--font-serif);
  font-size: 24px;
  line-height: 32px;
  color: var(--color-text-primary);
  font-weight: var(--font-weight-regular);
  margin: var(--space-lg) 0 var(--space-md);
}

.markdown-h4 {
  font-family: var(--font-serif);
  font-size: 20px;
  line-height: 28px;
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
  margin: var(--space-lg) 0 var(--space-sm);
}

.markdown-h5 {
  font-family: var(--font-serif);
  font-size: 18px;
  line-height: 28px;
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
  margin: var(--space-md) 0 var(--space-sm);
}

.markdown-h6 {
  font-family: var(--font-serif);
  font-size: 16px;
  line-height: 24px;
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
  margin: var(--space-md) 0 var(--space-sm);
}

/* 段落样式 */
.markdown-p {
  margin-bottom: var(--space-lg);
  line-height: 32px;
}

.markdown-p:last-child {
  margin-bottom: 0;
}

/* 链接样式 - 使用现有Link组件样式 */
.markdown-link {
  /* 样式由Link组件提供 */
}

/* 引用样式 - 使用现有Blockquote组件样式 */
.markdown-blockquote {
  /* 样式由Blockquote组件提供 */
}

/* 代码样式 */
.markdown-code-inline {
  background-color: var(--color-background-secondary);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: var(--font-mono);
  font-size: 15px;
  color: var(--color-text-secondary);
}

.markdown-pre {
  background-color: var(--color-background-secondary);
  padding: var(--space-md);
  border-radius: 8px;
  overflow-x: auto;
  margin: var(--space-lg) 0;
  border: 1px solid var(--color-border-primary);
}

.markdown-code-block {
  font-family: var(--font-mono);
  font-size: 15px;
  line-height: 24px;
  color: var(--color-text-secondary);
  background: none;
  padding: 0;
}

/* 覆盖highlight.js的默认样式以符合设计规范 */
.markdown-pre .hljs {
  background: none;
  color: var(--color-text-secondary);
  padding: 0;
}

/* 列表样式 */
.markdown-ul,
.markdown-ol {
  margin: var(--space-lg) 0;
  padding-left: var(--space-lg);
}

.markdown-ul {
  list-style-type: none;
}

.markdown-ol {
  list-style-type: decimal;
  list-style-position: outside;
}

.markdown-li {
  margin-bottom: var(--space-sm);
  line-height: 32px;
  position: relative;
}

.markdown-ul .markdown-li::before {
  content: '·';
  position: absolute;
  left: -var(--space-md);
  color: var(--color-accent-romantic);
  font-weight: var(--font-weight-bold);
}

.markdown-li:last-child {
  margin-bottom: 0;
}

/* 嵌套列表 */
.markdown-ul .markdown-ul,
.markdown-ol .markdown-ol,
.markdown-ul .markdown-ol,
.markdown-ol .markdown-ul {
  margin: var(--space-sm) 0;
}

/* 表格样式 */
.markdown-table-wrapper {
  overflow-x: auto;
  margin: var(--space-lg) 0;
  border-radius: 8px;
  border: 1px solid var(--color-border-primary);
}

.markdown-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 15px;
  line-height: 24px;
}

.markdown-thead {
  background-color: var(--color-background-secondary);
}

.markdown-th {
  padding: var(--space-sm) var(--space-md);
  text-align: left;
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  border-bottom: 1px solid var(--color-border-primary);
}

.markdown-td {
  padding: var(--space-sm) var(--space-md);
  border-bottom: 1px solid var(--color-border-primary);
  color: var(--color-text-secondary);
}

.markdown-tr:last-child .markdown-td {
  border-bottom: none;
}

/* 图片样式 */
.markdown-img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: var(--space-lg) 0;
  display: block;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 分隔线样式 */
.markdown-hr {
  border: none;
  height: 1px;
  background-color: var(--color-border-primary);
  margin: var(--space-xl) 0;
}

/* 强调和加粗样式 */
.markdown-em {
  font-style: italic;
  color: var(--color-text-primary);
}

.markdown-strong {
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

/* 响应式设计 */
@media (max-width: 767px) {
  .markdown-renderer {
    font-size: 16px;
    line-height: 28px;
  }
  
  .markdown-h1 {
    font-size: 32px;
    line-height: 44px;
    margin: var(--space-lg) 0 var(--space-md);
  }
  
  .markdown-h2 {
    font-size: 28px;
    line-height: 40px;
    margin: var(--space-lg) 0 var(--space-md);
  }
  
  .markdown-h3 {
    font-size: 22px;
    line-height: 32px;
    margin: var(--space-md) 0 var(--space-sm);
  }
  
  .markdown-h4 {
    font-size: 18px;
    line-height: 28px;
  }
  
  .markdown-h5 {
    font-size: 16px;
    line-height: 24px;
  }
  
  .markdown-h6 {
    font-size: 15px;
    line-height: 24px;
  }
  
  .markdown-p {
    line-height: 28px;
  }
  
  .markdown-li {
    line-height: 28px;
  }
  
  .markdown-pre {
    padding: var(--space-sm);
    margin: var(--space-md) 0;
  }
  
  .markdown-code-block {
    font-size: 14px;
    line-height: 20px;
  }
  
  .markdown-table {
    font-size: 14px;
    line-height: 20px;
  }
  
  .markdown-th,
  .markdown-td {
    padding: var(--space-xs) var(--space-sm);
  }
}
