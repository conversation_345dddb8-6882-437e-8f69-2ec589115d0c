import React from 'react';
import { motion } from 'framer-motion';
import './LoadingSpinner.css';

const LoadingSpinner = ({ size = 'medium', text = '加载中...' }) => {
  const dotVariants = {
    initial: { opacity: 0.3 },
    animate: { opacity: 1 },
  };

  const containerVariants = {
    initial: {},
    animate: {
      transition: {
        staggerChildren: 0.2,
        repeat: Infinity,
        repeatType: 'reverse',
        duration: 2,
      },
    },
  };

  return (
    <div className={`loading-spinner loading-spinner--${size}`}>
      <motion.div
        className="loading-spinner__dots"
        variants={containerVariants}
        initial="initial"
        animate="animate"
      >
        <motion.span
          className="loading-spinner__dot"
          variants={dotVariants}
          transition={{ duration: 0.6, ease: 'easeInOut' }}
        />
        <motion.span
          className="loading-spinner__dot"
          variants={dotVariants}
          transition={{ duration: 0.6, ease: 'easeInOut' }}
        />
        <motion.span
          className="loading-spinner__dot"
          variants={dotVariants}
          transition={{ duration: 0.6, ease: 'easeInOut' }}
        />
      </motion.div>
      {text && <p className="loading-spinner__text">{text}</p>}
    </div>
  );
};

export default LoadingSpinner;
