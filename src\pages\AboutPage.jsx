import React from 'react';
import Header from '../components/layout/Header';
import Footer from '../components/layout/Footer';
import Blockquote from '../components/ui/Blockquote';
import Poetry from '../components/ui/Poetry';
import Link from '../components/ui/Link';
import './AboutPage.css';

const AboutPage = () => {
  return (
    <div className="about-page">
      <Header />
      
      <main className="about-page__main">
        <div className="about-page__container">
          <header className="about-page__header">
            <h1 className="about-page__title">关于墨韵流光</h1>
            <p className="about-page__subtitle">
              在数字世界中捕捉思想与文字流动的瞬间美感
            </p>
          </header>
          
          <div className="about-page__content">
            <section className="about-page__section">
              <h2>设计理念</h2>
              <p>
                "墨韵流光"致力于在数字世界中捕捉思想与文字流动的瞬间美感。它并非一个静态的内容容器，
                而是一个有生命的、会呼吸的有机体。设计的核心任务是摒弃一切不必要的视觉噪音，
                让内容本身成为绝对主角。
              </p>
              
              <Blockquote author="设计师" source="设计规范">
                内容优先，呼吸感，内隐和谐，情感温度——这是我们追求的设计境界。
              </Blockquote>
            </section>
            
            <section className="about-page__section">
              <h2>技术实现</h2>
              <p>
                本站采用现代化的前端技术栈构建，基于React和Vite开发，
                使用Framer Motion实现流畅的动画效果。整个设计系统严格遵循
                <Link href="#" external>《墨韵流光》设计规范</Link>，
                确保视觉的一致性和用户体验的优雅。
              </p>
              
              <ul className="about-page__tech-list">
                <li>React 18 - 现代化的用户界面库</li>
                <li>Vite - 快速的构建工具</li>
                <li>Framer Motion - 流畅的动画库</li>
                <li>CSS Custom Properties - 设计令牌系统</li>
                <li>响应式设计 - 适配各种设备</li>
              </ul>
            </section>
            
            <section className="about-page__section">
              <h2>设计特色</h2>
              <div className="about-page__features">
                <div className="about-page__feature">
                  <h3>色彩系统</h3>
                  <p>基于传统水墨意境，基调淡雅隽永，仅以一抹"落霞红"画龙点睛。</p>
                </div>
                <div className="about-page__feature">
                  <h3>版式系统</h3>
                  <p>融汇衬线体的古典风骨与无衬线体的现代易读性，构建层次清晰的字体系统。</p>
                </div>
                <div className="about-page__feature">
                  <h3>动效系统</h3>
                  <p>遵循"缓、柔、雅"的原则，所有动效旨在服务内容，强化体验。</p>
                </div>
                <div className="about-page__feature">
                  <h3>间距系统</h3>
                  <p>建立以8px为原子单位的网格体系，通过战略性留白构建从容的视觉节奏。</p>
                </div>
              </div>
            </section>
            
            <section className="about-page__section">
              <Poetry title="网站诗韵" author="墨韵" className="about-page__poetry">
                数字世界觅诗韵，{'\n'}
                文字流光映心境。{'\n'}
                简约设计承古意，{'\n'}
                现代技术展新颜。{'\n'}
                {'\n'}
                留白之间藏深意，{'\n'}
                色彩点缀见匠心。{'\n'}
                愿君驻足细品味，{'\n'}
                共赏墨韵与流光。
              </Poetry>
            </section>
            
            <section className="about-page__section">
              <h2>联系方式</h2>
              <p>
                如果您对本站的设计或技术实现有任何疑问，或者想要交流关于设计和开发的想法，
                欢迎通过以下方式联系我：
              </p>
              
              <div className="about-page__contact">
                <div className="about-page__contact-item">
                  <strong>邮箱：</strong>
                  <Link href="mailto:<EMAIL>" external>
                    <EMAIL>
                  </Link>
                </div>
                <div className="about-page__contact-item">
                  <strong>GitHub：</strong>
                  <Link href="https://github.com" external>
                    @moyunliuguang
                  </Link>
                </div>
                <div className="about-page__contact-item">
                  <strong>微博：</strong>
                  <Link href="https://weibo.com" external>
                    @墨韵流光
                  </Link>
                </div>
              </div>
            </section>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default AboutPage;
