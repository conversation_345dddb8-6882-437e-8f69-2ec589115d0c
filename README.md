# 墨韵流光 - 个人博客

一个基于React的优雅个人博客，严格遵循《墨韵流光》设计规范，在数字世界中捕捉思想与文字流动的瞬间美感。

## ✨ 设计特色

### 设计理念
- **内容优先**: 所有设计元素都服务于内容的呈现与阅读体验
- **呼吸感**: 通过战略性、大面积的留白，构建从容的视觉节奏
- **内隐和谐**: 运用严谨的间距与版式系统，构建可被感知的视觉秩序
- **情感温度**: 在极简基调上，通过精妙的色彩点缀注入恰到好处的情感温度

### 色彩系统
- 基于传统水墨意境，基调淡雅隽永
- 主色调：暖宣白(#F9F7F3)、淡米(#F4F1ED)
- 文字色：墨黑(#332E2B)、深棕灰(#45403D)、浅灰褐(#9A938E)
- 点缀色：落霞红(#B5655D) - 唯一的浪漫色

### 版式系统
- 标题字体：思源宋体 (Source Han Serif SC)
- 正文字体：思源黑体 (Source Han Sans SC)
- 代码字体：JetBrains Mono
- 基线网格：8px 原子单位

### 动效系统
- 遵循"缓、柔、雅"原则
- 全局缓动曲线：cubic-bezier(0.25, 0.46, 0.45, 0.94)
- 交互反馈：150ms-250ms
- 页面转场：300ms-500ms

## 🚀 技术栈

- **React 18** - 现代化的用户界面库
- **Vite** - 快速的构建工具
- **Framer Motion** - 流畅的动画库
- **CSS Custom Properties** - 设计令牌系统
- **响应式设计** - 适配各种设备

## 📱 响应式断点

- **移动端**: < 768px
- **平板端**: 768px - 1199px
- **桌面端**: ≥ 1200px

## 🎨 组件库

### 布局组件
- `Header` - 顶部导航，支持滚动隐藏和移动端菜单
- `Footer` - 页脚，包含版权信息和社交链接

### UI组件
- `ArticleCard` - 文章卡片，支持大小变体和悬停动效
- `Button` - 按钮，支持主次样式和多种尺寸
- `Link` - 链接，带下划线绘制动画
- `Blockquote` - 引用，左侧装饰线设计
- `Poetry` - 诗歌模块，特殊排版和引号装饰
- `LoadingSpinner` - 加载动画，呼吸式三点设计
- `Lightbox` - 画廊模式，全屏图片查看
- `Input` / `Textarea` - 表单元素，统一的焦点样式

### 页面模板
- `HomePage` - 首页"潺潺溪流"布局，砌体网格
- `ArticlePage` - 文章页"静谧之地"布局，单栏阅读
- `AboutPage` - 关于页面，展示设计理念和技术实现
- `NotFoundPage` - 404页面，优雅的错误提示

## 🛠️ 开发指南

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 📁 项目结构

```
src/
├── components/          # 组件库
│   ├── ui/             # UI组件
│   ├── layout/         # 布局组件
│   └── features/       # 功能组件
├── pages/              # 页面组件
├── styles/             # 样式文件
│   ├── tokens/         # 设计令牌
│   └── components/     # 组件样式
├── data/               # 数据文件
├── hooks/              # 自定义Hook
└── utils/              # 工具函数
```

## 🎯 设计规范

本项目严格遵循《墨韵流光》设计规范V2.0，包括：

- 8px基线网格系统
- 严格的色彩和字体规范
- 统一的间距和动效标准
- 无障碍性要求(WCAG 2.1 AA级)
- 响应式设计原则

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

---

*在数字世界中捕捉思想与文字流动的瞬间美感* ✨
