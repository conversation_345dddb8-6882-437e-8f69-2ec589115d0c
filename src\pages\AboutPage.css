/**
 * AboutPage 样式
 * 实现关于页面的设计规范
 */

.about-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.about-page__main {
  flex: 1;
  padding-top: var(--header-height);
  background-color: var(--color-background-primary);
}

.about-page__container {
  max-width: var(--article-max-width);
  margin: 0 auto;
  padding: var(--space-xl) var(--space-md) var(--space-xxl);
}

/* 页面头部 */
.about-page__header {
  text-align: center;
  margin-bottom: var(--space-xxl);
}

.about-page__title {
  font-family: var(--font-serif);
  font-size: 48px;
  line-height: 64px;
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
  font-weight: var(--font-weight-regular);
}

.about-page__subtitle {
  font-family: var(--font-sans-serif);
  font-size: 18px;
  line-height: 32px;
  color: var(--color-text-secondary);
  max-width: 500px;
  margin: 0 auto;
}

/* 内容区域 */
.about-page__content {
  font-family: var(--font-sans-serif);
  font-size: 17px;
  line-height: 32px;
  color: var(--color-text-secondary);
}

.about-page__section {
  margin-bottom: var(--space-xxl);
}

.about-page__section:last-child {
  margin-bottom: 0;
}

.about-page__section h2 {
  font-family: var(--font-serif);
  font-size: 32px;
  line-height: 48px;
  color: var(--color-text-primary);
  margin-bottom: var(--space-lg);
  font-weight: var(--font-weight-regular);
}

.about-page__section h3 {
  font-family: var(--font-serif);
  font-size: 24px;
  line-height: 32px;
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
  font-weight: var(--font-weight-regular);
}

.about-page__section p {
  margin-bottom: var(--space-lg);
}

.about-page__section p:last-child {
  margin-bottom: 0;
}

/* 技术列表 */
.about-page__tech-list {
  list-style: none;
  margin: var(--space-lg) 0;
  padding-left: var(--space-md);
}

.about-page__tech-list li {
  position: relative;
  margin-bottom: var(--space-sm);
  padding-left: var(--space-md);
}

.about-page__tech-list li::before {
  content: '·';
  position: absolute;
  left: 0;
  color: var(--color-accent-romantic);
  font-weight: var(--font-weight-bold);
}

/* 特色功能网格 */
.about-page__features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-lg);
  margin: var(--space-xl) 0;
}

.about-page__feature {
  padding: var(--space-lg);
  background-color: var(--color-background-secondary);
  border-radius: 8px;
  border-left: 3px solid var(--color-accent-romantic);
}

.about-page__feature h3 {
  margin-bottom: var(--space-sm);
  font-size: 20px;
  line-height: 28px;
}

.about-page__feature p {
  margin-bottom: 0;
  font-size: 15px;
  line-height: 24px;
}

/* 诗歌样式 */
.about-page__poetry {
  margin: var(--space-xl) 0;
}

/* 联系方式 */
.about-page__contact {
  margin: var(--space-lg) 0;
}

.about-page__contact-item {
  margin-bottom: var(--space-md);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.about-page__contact-item:last-child {
  margin-bottom: 0;
}

.about-page__contact-item strong {
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
  min-width: 60px;
}

/* 响应式设计 */
@media (max-width: 767px) {
  .about-page__container {
    padding: var(--space-lg) var(--space-md) var(--space-xl);
  }
  
  .about-page__title {
    font-size: 32px;
    line-height: 44px;
  }
  
  .about-page__subtitle {
    font-size: 16px;
    line-height: 28px;
  }
  
  .about-page__content {
    font-size: 16px;
    line-height: 28px;
  }
  
  .about-page__section h2 {
    font-size: 28px;
    line-height: 40px;
    margin-bottom: var(--space-md);
  }
  
  .about-page__section h3 {
    font-size: 20px;
    line-height: 28px;
  }
  
  .about-page__features {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }
  
  .about-page__feature {
    padding: var(--space-md);
  }
  
  .about-page__contact-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-xs);
  }
  
  .about-page__contact-item strong {
    min-width: auto;
  }
}

@media (min-width: 768px) and (max-width: 1199px) {
  .about-page__container {
    padding-left: var(--space-lg);
    padding-right: var(--space-lg);
  }
}

@media (min-width: 1200px) {
  .about-page__container {
    padding-left: var(--content-side-padding);
    padding-right: var(--content-side-padding);
  }
}
