import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import './Lightbox.css';

const Lightbox = ({ images, currentIndex, isOpen, onClose, onNext, onPrev }) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(currentIndex || 0);

  useEffect(() => {
    if (currentIndex !== undefined) {
      setCurrentImageIndex(currentIndex);
    }
  }, [currentIndex]);

  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!isOpen) return;
      
      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowLeft':
          handlePrev();
          break;
        case 'ArrowRight':
          handleNext();
          break;
        default:
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, currentImageIndex]);

  const handleNext = () => {
    const nextIndex = (currentImageIndex + 1) % images.length;
    setCurrentImageIndex(nextIndex);
    onNext && onNext(nextIndex);
  };

  const handlePrev = () => {
    const prevIndex = currentImageIndex === 0 ? images.length - 1 : currentImageIndex - 1;
    setCurrentImageIndex(prevIndex);
    onPrev && onPrev(prevIndex);
  };

  const currentImage = images[currentImageIndex];

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="lightbox"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          onClick={onClose}
        >
          {/* 背景蒙层 */}
          <div className="lightbox__backdrop" />
          
          {/* 关闭按钮 */}
          <button
            className="lightbox__close"
            onClick={onClose}
            aria-label="关闭画廊"
          >
            ×
          </button>
          
          {/* 图片容器 */}
          <div className="lightbox__container" onClick={(e) => e.stopPropagation()}>
            <motion.img
              key={currentImageIndex}
              src={currentImage.src}
              alt={currentImage.alt || ''}
              className="lightbox__image"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3 }}
            />
            
            {/* 图片说明 */}
            {currentImage.caption && (
              <motion.div
                className="lightbox__caption"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
              >
                {currentImage.caption}
              </motion.div>
            )}
          </div>
          
          {/* 导航按钮 */}
          {images.length > 1 && (
            <>
              <button
                className="lightbox__nav lightbox__nav--prev"
                onClick={handlePrev}
                aria-label="上一张图片"
              >
                ‹
              </button>
              <button
                className="lightbox__nav lightbox__nav--next"
                onClick={handleNext}
                aria-label="下一张图片"
              >
                ›
              </button>
            </>
          )}
          
          {/* 图片计数器 */}
          {images.length > 1 && (
            <div className="lightbox__counter">
              {currentImageIndex + 1} / {images.length}
            </div>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Lightbox;
