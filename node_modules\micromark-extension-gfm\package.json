{"name": "micromark-extension-gfm", "version": "3.0.0", "description": "micromark extension to support GFM (GitHub Flavored Markdown)", "license": "MIT", "keywords": ["micromark", "micromark-extension", "table", "strikethrough", "tasklist", "autolink", "footnote", "tagfilter", "github", "gfm", "markdown", "unified"], "repository": "micromark/micromark-extension-gfm", "bugs": "https://github.com/micromark/micromark-extension-gfm/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["index.d.ts", "index.js"], "dependencies": {"micromark-extension-gfm-autolink-literal": "^2.0.0", "micromark-extension-gfm-footnote": "^2.0.0", "micromark-extension-gfm-strikethrough": "^2.0.0", "micromark-extension-gfm-table": "^2.0.0", "micromark-extension-gfm-tagfilter": "^2.0.0", "micromark-extension-gfm-task-list-item": "^2.0.0", "micromark-util-combine-extensions": "^2.0.0", "micromark-util-types": "^2.0.0"}, "devDependencies": {"@types/node": "^20.0.0", "c8": "^8.0.0", "create-gfm-fixtures": "^1.0.0", "hast-util-from-html": "^1.0.0", "hast-util-select": "^5.0.0", "hast-util-to-text": "^3.0.0", "micromark": "^4.0.0", "prettier": "^2.0.0", "rehype": "^12.0.0", "rehype-sort-attributes": "^4.0.0", "remark-cli": "^11.0.0", "remark-preset-wooorm": "^9.0.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "undici": "^5.0.0", "xo": "^0.54.0"}, "scripts": {"prepack": "npm run build && npm run format", "build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark . -qfo && prettier . -w --loglevel warn && xo --fix", "test-api-prod": "node --conditions production test/index.js", "test-api-dev": "node --conditions development test/index.js", "test-api": "npm run test-api-dev && npm run test-api-prod", "test-coverage": "c8 --100 --reporter lcov npm run test-api", "test": "npm run build && npm run format && npm run test-coverage"}, "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": {"prettier": true, "overrides": [{"files": ["{test,script}/**/*.js"], "rules": {"no-await-in-loop": "off"}}]}}