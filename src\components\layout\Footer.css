/**
 * Footer 组件样式
 * 实现页脚的设计规范
 */

.footer {
  margin-top: var(--space-xxl);
  padding: var(--space-xl) 0;
  background-color: var(--color-background-primary);
}

.footer__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-md);
}

.footer__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-lg);
  text-align: center;
}

/* 版权信息 */
.footer__copyright {
  order: 1;
}

.footer__copyright p {
  font-family: var(--font-sans-serif);
  font-size: 14px;
  line-height: 24px;
  color: var(--color-text-tertiary);
  margin: 0;
}

/* 社交媒体链接 */
.footer__social {
  order: 2;
}

.footer__social-list {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
  flex-wrap: wrap;
  justify-content: center;
}

.footer__social-link {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-family: var(--font-sans-serif);
  font-size: 14px;
  line-height: 24px;
  color: var(--color-text-tertiary);
  transition: color var(--duration-fast) var(--easing-default);
}

.footer__social-link:hover {
  color: var(--color-accent-romantic);
}

.footer__social-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 12px;
  font-weight: var(--font-weight-bold);
  background-color: var(--color-border-primary);
  color: var(--color-text-secondary);
  border-radius: 50%;
  transition: all var(--duration-fast) var(--easing-default);
}

.footer__social-link:hover .footer__social-icon {
  background-color: var(--color-accent-romantic);
  color: var(--color-background-primary);
}

.footer__social-name {
  display: none;
}

/* 备案信息 */
.footer__legal {
  order: 3;
}

.footer__legal p {
  font-family: var(--font-sans-serif);
  font-size: 12px;
  line-height: 20px;
  color: var(--color-text-tertiary);
  margin: 0;
}

.footer__legal-link {
  color: inherit;
  font-size: inherit;
  font-weight: var(--font-weight-regular);
}

.footer__legal-link:hover {
  color: var(--color-text-secondary);
}

/* 装饰性分隔符 */
.footer__separator {
  margin-top: var(--space-lg);
  text-align: center;
}

.footer__separator span {
  font-family: var(--font-serif);
  font-size: 16px;
  color: var(--color-border-primary);
  letter-spacing: 8px;
}

/* 响应式设计 */
@media (min-width: 768px) {
  .footer__container {
    padding: 0 var(--space-lg);
  }
  
  .footer__content {
    flex-direction: row;
    justify-content: space-between;
    text-align: left;
  }
  
  .footer__copyright {
    order: 1;
    flex: 1;
  }
  
  .footer__social {
    order: 2;
    flex: 0 0 auto;
  }
  
  .footer__legal {
    order: 3;
    flex: 1;
    text-align: right;
  }
  
  .footer__social-name {
    display: inline;
  }
  
  .footer__social-list {
    gap: var(--space-md);
  }
}

@media (min-width: 1200px) {
  .footer__container {
    padding: 0 var(--content-side-padding);
  }
}
