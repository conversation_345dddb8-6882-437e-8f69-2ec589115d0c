import React from 'react';
import Header from '../components/layout/Header';
import Footer from '../components/layout/Footer';
import Button from '../components/ui/Button';
import './NotFoundPage.css';

const NotFoundPage = () => {
  return (
    <div className="not-found-page">
      <Header />
      
      <main className="not-found-page__main">
        <div className="not-found-page__container">
          <div className="not-found-page__content">
            <h1 className="not-found-page__title">信步至此，已是无径</h1>
            <p className="not-found-page__subtitle">
              或可返回首页，重觅墨韵流光
            </p>
            <div className="not-found-page__actions">
              <Button href="/" variant="primary" size="large">
                返回首页
              </Button>
            </div>
          </div>
          
          {/* 装饰性元素 */}
          <div className="not-found-page__decoration">
            <span className="not-found-page__dots">· · ·</span>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default NotFoundPage;
