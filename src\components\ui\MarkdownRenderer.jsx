import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import rehypeRaw from 'rehype-raw';
import Blockquote from './Blockquote';
import Link from './Link';
import './MarkdownRenderer.css';

const MarkdownRenderer = ({ content, className = '' }) => {
  // 自定义组件映射，确保符合设计规范
  const components = {
    // 标题组件
    h1: ({ children, ...props }) => (
      <h1 className="markdown-h1" {...props}>{children}</h1>
    ),
    h2: ({ children, ...props }) => (
      <h2 className="markdown-h2" {...props}>{children}</h2>
    ),
    h3: ({ children, ...props }) => (
      <h3 className="markdown-h3" {...props}>{children}</h3>
    ),
    h4: ({ children, ...props }) => (
      <h4 className="markdown-h4" {...props}>{children}</h4>
    ),
    h5: ({ children, ...props }) => (
      <h5 className="markdown-h5" {...props}>{children}</h5>
    ),
    h6: ({ children, ...props }) => (
      <h6 className="markdown-h6" {...props}>{children}</h6>
    ),

    // 段落组件
    p: ({ children, ...props }) => (
      <p className="markdown-p" {...props}>{children}</p>
    ),

    // 链接组件 - 使用我们的Link组件
    a: ({ href, children, ...props }) => (
      <Link 
        href={href} 
        external={href?.startsWith('http')} 
        className="markdown-link"
        {...props}
      >
        {children}
      </Link>
    ),

    // 引用组件 - 使用我们的Blockquote组件
    blockquote: ({ children, ...props }) => (
      <Blockquote className="markdown-blockquote" {...props}>
        {children}
      </Blockquote>
    ),

    // 代码组件
    code: ({ inline, className, children, ...props }) => {
      const match = /language-(\w+)/.exec(className || '');
      const language = match ? match[1] : '';
      
      if (inline) {
        return (
          <code className="markdown-code-inline" {...props}>
            {children}
          </code>
        );
      }
      
      return (
        <pre className="markdown-pre" {...props}>
          <code className={`markdown-code-block ${className || ''}`}>
            {children}
          </code>
        </pre>
      );
    },

    // 列表组件
    ul: ({ children, ...props }) => (
      <ul className="markdown-ul" {...props}>{children}</ul>
    ),
    ol: ({ children, ...props }) => (
      <ol className="markdown-ol" {...props}>{children}</ol>
    ),
    li: ({ children, ...props }) => (
      <li className="markdown-li" {...props}>{children}</li>
    ),

    // 表格组件
    table: ({ children, ...props }) => (
      <div className="markdown-table-wrapper">
        <table className="markdown-table" {...props}>{children}</table>
      </div>
    ),
    thead: ({ children, ...props }) => (
      <thead className="markdown-thead" {...props}>{children}</thead>
    ),
    tbody: ({ children, ...props }) => (
      <tbody className="markdown-tbody" {...props}>{children}</tbody>
    ),
    tr: ({ children, ...props }) => (
      <tr className="markdown-tr" {...props}>{children}</tr>
    ),
    th: ({ children, ...props }) => (
      <th className="markdown-th" {...props}>{children}</th>
    ),
    td: ({ children, ...props }) => (
      <td className="markdown-td" {...props}>{children}</td>
    ),

    // 图片组件
    img: ({ src, alt, ...props }) => (
      <img 
        className="markdown-img" 
        src={src} 
        alt={alt} 
        loading="lazy"
        {...props} 
      />
    ),

    // 分隔线
    hr: ({ ...props }) => (
      <hr className="markdown-hr" {...props} />
    ),

    // 强调和加粗
    em: ({ children, ...props }) => (
      <em className="markdown-em" {...props}>{children}</em>
    ),
    strong: ({ children, ...props }) => (
      <strong className="markdown-strong" {...props}>{children}</strong>
    ),
  };

  return (
    <div className={`markdown-renderer ${className}`}>
      <ReactMarkdown
        components={components}
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeHighlight, rehypeRaw]}
        skipHtml={false}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownRenderer;
