# 部署指南

## 静态网站部署

本项目构建后生成的是纯静态文件，可以部署到任何静态网站托管服务。

### 1. Vercel 部署

1. 将代码推送到 GitHub 仓库
2. 在 [Vercel](https://vercel.com) 导入项目
3. 构建命令：`npm run build`
4. 输出目录：`dist`
5. 自动部署完成

### 2. Netlify 部署

1. 将代码推送到 GitHub 仓库
2. 在 [Netlify](https://netlify.com) 导入项目
3. 构建命令：`npm run build`
4. 发布目录：`dist`
5. 自动部署完成

### 3. GitHub Pages 部署

1. 安装 gh-pages 包：
```bash
npm install --save-dev gh-pages
```

2. 在 package.json 添加部署脚本：
```json
{
  "scripts": {
    "deploy": "npm run build && gh-pages -d dist"
  }
}
```

3. 运行部署命令：
```bash
npm run deploy
```

### 4. 服务器部署

1. 构建项目：
```bash
npm run build
```

2. 将 `dist` 目录上传到服务器
3. 配置 Web 服务器（如 Nginx）指向 `dist` 目录

#### Nginx 配置示例：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 环境变量

如果需要配置环境变量，创建 `.env` 文件：

```env
VITE_API_URL=https://api.example.com
VITE_SITE_URL=https://your-domain.com
```

在代码中使用：
```javascript
const apiUrl = import.meta.env.VITE_API_URL;
```

## 性能优化

### 1. 图片优化
- 使用 WebP 格式
- 实现懒加载
- 使用 CDN 加速

### 2. 代码分割
- 路由级别的代码分割
- 组件懒加载

### 3. 缓存策略
- 静态资源长期缓存
- HTML 文件短期缓存

## SEO 优化

### 1. Meta 标签
在 `index.html` 中添加：
```html
<meta name="description" content="墨韵流光 - 个人博客">
<meta name="keywords" content="博客,设计,技术">
<meta property="og:title" content="墨韵流光">
<meta property="og:description" content="在数字世界中捕捉思想与文字流动的瞬间美感">
```

### 2. 结构化数据
添加 JSON-LD 结构化数据以提高搜索引擎理解。

### 3. 站点地图
生成 sitemap.xml 文件提交给搜索引擎。

## 监控和分析

### 1. Google Analytics
添加 GA 代码进行访问统计。

### 2. 性能监控
使用 Web Vitals 监控页面性能。

### 3. 错误监控
集成 Sentry 等错误监控服务。
