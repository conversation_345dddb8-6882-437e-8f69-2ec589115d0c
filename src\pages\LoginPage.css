/**
 * LoginPage 样式
 * 实现管理员登录页面的设计规范
 */

.login-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.login-page__main {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: var(--header-height);
  padding-bottom: var(--space-xxl);
  background-color: var(--color-background-primary);
}

.login-page__container {
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
  padding: 0 var(--space-md);
}

.login-page__form-container {
  background-color: var(--color-background-secondary);
  border-radius: 12px;
  padding: var(--space-xl);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--color-border-primary);
}

/* 页面头部 */
.login-page__header {
  text-align: center;
  margin-bottom: var(--space-xl);
}

.login-page__title {
  font-family: var(--font-serif);
  font-size: 32px;
  line-height: 48px;
  color: var(--color-text-primary);
  margin-bottom: var(--space-sm);
  font-weight: var(--font-weight-regular);
}

.login-page__subtitle {
  font-family: var(--font-sans-serif);
  font-size: 16px;
  line-height: 24px;
  color: var(--color-text-secondary);
  margin: 0;
}

/* 表单样式 */
.login-page__form {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
}

.login-page__field {
  display: flex;
  flex-direction: column;
}

.login-page__error {
  padding: var(--space-sm) var(--space-md);
  background-color: rgba(181, 101, 93, 0.1);
  border: 1px solid var(--color-accent-romantic);
  border-radius: 6px;
  color: var(--color-accent-romantic);
  font-family: var(--font-sans-serif);
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  margin-bottom: var(--space-md);
}

.login-page__actions {
  margin-top: var(--space-md);
}

.login-page__submit-button {
  width: 100%;
}

/* 帮助信息 */
.login-page__help {
  margin-top: var(--space-xl);
  padding-top: var(--space-lg);
  border-top: 1px solid var(--color-border-primary);
  text-align: center;
}

.login-page__help-text {
  font-family: var(--font-sans-serif);
  font-size: 13px;
  line-height: 20px;
  color: var(--color-text-tertiary);
  margin: 0;
  background-color: var(--color-background-primary);
  padding: var(--space-xs) var(--space-sm);
  border-radius: 4px;
  border: 1px solid var(--color-border-primary);
  font-family: var(--font-mono);
}

/* 响应式设计 */
@media (max-width: 767px) {
  .login-page__container {
    max-width: 100%;
    padding: 0 var(--space-sm);
  }
  
  .login-page__form-container {
    padding: var(--space-lg);
    border-radius: 8px;
  }
  
  .login-page__title {
    font-size: 28px;
    line-height: 40px;
  }
  
  .login-page__subtitle {
    font-size: 15px;
    line-height: 22px;
  }
  
  .login-page__form {
    gap: var(--space-md);
  }
  
  .login-page__help-text {
    font-size: 12px;
    line-height: 18px;
  }
}

@media (min-width: 768px) and (max-width: 1199px) {
  .login-page__container {
    padding: 0 var(--space-lg);
  }
}

@media (min-width: 1200px) {
  .login-page__container {
    padding: 0 var(--content-side-padding);
  }
}

/* 焦点状态增强 */
.login-page__form-container:focus-within {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

/* 加载状态 */
.login-page__submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

/* 动画效果 */
.login-page__error {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

/* 成功状态（可选） */
.login-page__success {
  padding: var(--space-sm) var(--space-md);
  background-color: rgba(76, 175, 80, 0.1);
  border: 1px solid #4CAF50;
  border-radius: 6px;
  color: #2E7D32;
  font-family: var(--font-sans-serif);
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  margin-bottom: var(--space-md);
}
