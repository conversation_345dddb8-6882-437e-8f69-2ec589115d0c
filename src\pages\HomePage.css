/**
 * HomePage 样式
 * 实现首页"潺潺溪流"布局
 */

.home-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.home-page__main {
  flex: 1;
  padding-top: var(--header-height);
}

/* 英雄区域 */
.home-page__hero {
  padding: var(--space-xxl) 0;
  background-color: var(--color-background-primary);
  text-align: center;
}

.home-page__hero-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 var(--space-md);
}

.home-page__hero-title {
  font-family: var(--font-serif);
  font-size: 64px;
  line-height: 80px;
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
  font-weight: var(--font-weight-regular);
}

.home-page__hero-subtitle {
  font-family: var(--font-sans-serif);
  font-size: 20px;
  line-height: 32px;
  color: var(--color-text-secondary);
  margin-bottom: var(--space-xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.home-page__hero-actions {
  display: flex;
  gap: var(--space-md);
  justify-content: center;
  flex-wrap: wrap;
}

/* 文章列表区域 */
.home-page__articles {
  padding: var(--space-xxl) 0;
  background-color: var(--color-background-primary);
}

.home-page__articles-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-md);
}

/* 砌体布局网格 */
.home-page__articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

/* 大卡片占据更多空间 */
.home-page__articles-grid .article-card--large {
  grid-column: span 2;
}

.home-page__articles-more {
  text-align: center;
  margin-top: var(--space-xl);
}

/* 响应式设计 */
@media (max-width: 767px) {
  .home-page__hero {
    padding: var(--space-xl) 0;
  }
  
  .home-page__hero-title {
    font-size: 40px;
    line-height: 56px;
  }
  
  .home-page__hero-subtitle {
    font-size: 18px;
    line-height: 28px;
  }
  
  .home-page__hero-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .home-page__articles {
    padding: var(--space-xl) 0;
  }
  
  .home-page__articles-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }
  
  .home-page__articles-grid .article-card--large {
    grid-column: span 1;
  }
}

@media (min-width: 768px) and (max-width: 1199px) {
  .home-page__hero-container {
    padding: 0 var(--space-lg);
  }
  
  .home-page__articles-container {
    padding: 0 var(--space-lg);
  }
  
  .home-page__articles-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .home-page__articles-grid .article-card--large {
    grid-column: span 2;
  }
}

@media (min-width: 1200px) {
  .home-page__hero-container {
    padding: 0 var(--content-side-padding);
  }
  
  .home-page__articles-container {
    padding: 0 var(--content-side-padding);
  }
  
  .home-page__articles-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .home-page__articles-grid .article-card--large {
    grid-column: span 2;
  }
}

/* 特殊布局：第一个大卡片 */
.home-page__articles-grid .article-card:first-child.article-card--large {
  grid-row: span 2;
}

/* 非对称美学：某些卡片的特殊定位 */
@media (min-width: 1200px) {
  .home-page__articles-grid .article-card:nth-child(3) {
    transform: translateY(var(--space-md));
  }
  
  .home-page__articles-grid .article-card:nth-child(5) {
    transform: translateY(-var(--space-md));
  }
}
