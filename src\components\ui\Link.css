/**
 * Link 组件样式
 * 实现链接的设计规范
 */

.link {
  position: relative;
  display: inline-flex;
  align-items: baseline;
  gap: 2px;
  color: #5B4F4A; /* 深棕灰 darken(5%) */
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: color var(--duration-fast) var(--easing-default);
}

.link:hover {
  color: var(--color-accent-romantic);
}

.link:active {
  color: var(--color-accent-romantic);
}

.link:visited {
  color: #5B4F4A; /* 保持与默认状态相同 */
}

.link:visited:hover {
  color: var(--color-accent-romantic);
}

/* 链接文本 */
.link__text {
  position: relative;
  z-index: 1;
}

/* 下划线动画 */
.link__underline {
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 1px;
  background-color: var(--color-accent-romantic);
  transform-origin: left;
  z-index: 0;
}

/* 无下划线变体 */
.link--no-underline .link__underline {
  display: none;
}

/* 外部链接图标 */
.link__external-icon {
  font-size: 0.8em;
  color: var(--color-text-tertiary);
  transition: color var(--duration-fast) var(--easing-default);
  margin-left: 2px;
}

.link--external:hover .link__external-icon {
  color: var(--color-accent-romantic);
}

/* 焦点状态 */
.link:focus-visible {
  outline: 2px solid var(--color-accent-romantic);
  outline-offset: 2px;
  border-radius: 2px;
}

/* 在深色背景上的链接 */
.link--on-dark {
  color: var(--color-background-primary);
}

.link--on-dark:hover {
  color: #FFFFFF;
}

.link--on-dark .link__underline {
  background-color: #FFFFFF;
}

.link--on-dark .link__external-icon {
  color: rgba(255, 255, 255, 0.7);
}

.link--on-dark:hover .link__external-icon {
  color: #FFFFFF;
}

/* 大尺寸链接 */
.link--large {
  font-size: 18px;
  line-height: 28px;
}

.link--large .link__underline {
  height: 2px;
  bottom: -2px;
}

/* 小尺寸链接 */
.link--small {
  font-size: 14px;
  line-height: 20px;
}

/* 块级链接 */
.link--block {
  display: block;
}

/* 响应式设计 */
@media (max-width: 767px) {
  .link {
    font-size: 15px;
  }
  
  .link--large {
    font-size: 16px;
    line-height: 24px;
  }
}
