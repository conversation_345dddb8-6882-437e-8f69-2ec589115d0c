import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import Header from '../components/layout/Header';
import Footer from '../components/layout/Footer';
import Button from '../components/ui/Button';
import { useAuth } from '../contexts/AuthContext';
import './AdminPage.css';

const AdminPage = () => {
  const { isAdmin, user } = useAuth();

  // 如果不是管理员，重定向到首页
  useEffect(() => {
    if (!isAdmin()) {
      window.history.pushState({}, '', '/');
      window.dispatchEvent(new PopStateEvent('popstate'));
    }
  }, [isAdmin]);

  // 如果不是管理员，不渲染内容
  if (!isAdmin()) {
    return null;
  }

  const adminStats = [
    { label: '总文章数', value: '3', icon: '📝' },
    { label: '总访问量', value: '1,234', icon: '👁️' },
    { label: '评论数', value: '56', icon: '💬' },
    { label: '订阅者', value: '89', icon: '👥' },
  ];

  const quickActions = [
    { name: '写新文章', description: '创建一篇新的博客文章', action: () => alert('功能开发中...') },
    { name: '管理文章', description: '编辑或删除现有文章', action: () => alert('功能开发中...') },
    { name: '查看评论', description: '管理用户评论', action: () => alert('功能开发中...') },
    { name: '网站设置', description: '修改网站配置', action: () => alert('功能开发中...') },
  ];

  return (
    <div className="admin-page">
      <Header />
      
      <main className="admin-page__main">
        <div className="admin-page__container">
          <motion.header
            className="admin-page__header"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="admin-page__title">管理后台</h1>
            <p className="admin-page__subtitle">
              欢迎回来，{user?.displayName}！
            </p>
          </motion.header>
          
          {/* 统计数据 */}
          <motion.section
            className="admin-page__stats"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <h2 className="admin-page__section-title">网站概览</h2>
            <div className="admin-page__stats-grid">
              {adminStats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  className="admin-page__stat-card"
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: 0.2 + index * 0.1 }}
                >
                  <div className="admin-page__stat-icon">{stat.icon}</div>
                  <div className="admin-page__stat-content">
                    <div className="admin-page__stat-value">{stat.value}</div>
                    <div className="admin-page__stat-label">{stat.label}</div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.section>
          
          {/* 快速操作 */}
          <motion.section
            className="admin-page__actions"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <h2 className="admin-page__section-title">快速操作</h2>
            <div className="admin-page__actions-grid">
              {quickActions.map((action, index) => (
                <motion.div
                  key={action.name}
                  className="admin-page__action-card"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.4 + index * 0.1 }}
                  whileHover={{ y: -4 }}
                >
                  <h3 className="admin-page__action-title">{action.name}</h3>
                  <p className="admin-page__action-description">{action.description}</p>
                  <Button
                    variant="secondary"
                    size="small"
                    onClick={action.action}
                    className="admin-page__action-button"
                  >
                    执行操作
                  </Button>
                </motion.div>
              ))}
            </div>
          </motion.section>
          
          {/* 最近活动 */}
          <motion.section
            className="admin-page__recent"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <h2 className="admin-page__section-title">最近活动</h2>
            <div className="admin-page__recent-list">
              <div className="admin-page__recent-item">
                <div className="admin-page__recent-time">2小时前</div>
                <div className="admin-page__recent-content">新用户访问了文章《春日里的诗意时光》</div>
              </div>
              <div className="admin-page__recent-item">
                <div className="admin-page__recent-time">5小时前</div>
                <div className="admin-page__recent-content">有用户在《夜深人静时的思考》下留言</div>
              </div>
              <div className="admin-page__recent-item">
                <div className="admin-page__recent-time">1天前</div>
                <div className="admin-page__recent-content">文章《关于写作的一些感悟》获得了新的点赞</div>
              </div>
            </div>
          </motion.section>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default AdminPage;
