import React from 'react';
import clsx from 'clsx';
import './Blockquote.css';

const Blockquote = ({
  children,
  author,
  source,
  className,
  ...props
}) => {
  const blockquoteClasses = clsx('blockquote', className);

  return (
    <blockquote className={blockquoteClasses} {...props}>
      <div className="blockquote__content">
        {children}
      </div>
      {(author || source) && (
        <footer className="blockquote__footer">
          {author && <cite className="blockquote__author">{author}</cite>}
          {source && (
            <>
              {author && <span className="blockquote__separator">，</span>}
              <cite className="blockquote__source">{source}</cite>
            </>
          )}
        </footer>
      )}
    </blockquote>
  );
};

export default Blockquote;
