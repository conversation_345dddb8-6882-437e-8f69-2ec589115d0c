import React from 'react';
import clsx from 'clsx';
import './Poetry.css';

const Poetry = ({
  children,
  title,
  author,
  source,
  className,
  ...props
}) => {
  const poetryClasses = clsx('poetry', className);

  return (
    <div className={poetryClasses} {...props}>
      {title && <h3 className="poetry__title">{title}</h3>}
      
      <div className="poetry__content">
        {children}
      </div>
      
      {(author || source) && (
        <footer className="poetry__footer">
          {author && <cite className="poetry__author">— {author}</cite>}
          {source && (
            <>
              {author && <span className="poetry__separator">，</span>}
              <cite className="poetry__source">《{source}》</cite>
            </>
          )}
        </footer>
      )}
    </div>
  );
};

export default Poetry;
