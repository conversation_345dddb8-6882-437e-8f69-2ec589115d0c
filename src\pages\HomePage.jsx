import React from 'react';
import Header from '../components/layout/Header';
import Footer from '../components/layout/Footer';
import ArticleCard from '../components/ui/ArticleCard';
import Button from '../components/ui/Button';
import { articles } from '../data/articles';
import './HomePage.css';

const HomePage = () => {

  return (
    <div className="home-page">
      <Header />

      <main className="home-page__main">
        {/* 英雄区域 */}
        <section className="home-page__hero">
          <div className="home-page__hero-container">
            <h1 className="home-page__hero-title">墨韵流光</h1>
            <p className="home-page__hero-subtitle">
              在数字世界中捕捉思想与文字流动的瞬间美感
            </p>
            <div className="home-page__hero-actions">
              <Button
                onClick={() => {
                  window.history.pushState({}, '', '/article');
                  window.dispatchEvent(new PopStateEvent('popstate'));
                }}
                variant="primary"
              >
                开始阅读
              </Button>
              <Button
                onClick={() => {
                  window.history.pushState({}, '', '/about');
                  window.dispatchEvent(new PopStateEvent('popstate'));
                }}
                variant="secondary"
              >
                了解更多
              </Button>
            </div>
          </div>
        </section>

        {/* 文章列表 */}
        <section className="home-page__articles">
          <div className="home-page__articles-container">
            <div className="home-page__articles-grid">
              {articles.map((article) => (
                <ArticleCard
                  key={article.id}
                  title={article.title}
                  excerpt={article.excerpt}
                  date={article.date}
                  readTime={article.readTime}
                  image={article.image}
                  href={article.href}
                  size={article.size}
                />
              ))}
            </div>

            <div className="home-page__articles-more">
              <Button href="/articles" variant="secondary" size="large">
                查看更多文章
              </Button>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default HomePage;
