import React from 'react';
import Header from '../components/layout/Header';
import Footer from '../components/layout/Footer';
import ArticleCard from '../components/ui/ArticleCard';
import Button from '../components/ui/Button';
import { articles } from '../data/articles';
import './HomePage.css';

const HomePage = () => {
  // 模拟文章数据
  const articles = [
    {
      id: 1,
      title: '春日里的诗意时光',
      excerpt: '春天来了，万物复苏，心中涌起无限的诗意。在这个美好的季节里，让我们一起感受生活的美好，体验文字的力量。',
      date: '2024-03-15',
      readTime: 5,
      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop',
      href: '/articles/spring-poetry',
      size: 'large'
    },
    {
      id: 2,
      title: '夜深人静时的思考',
      excerpt: '在夜深人静的时候，思绪总是特别清晰。这是一个适合思考人生、回顾过往的时刻。',
      date: '2024-03-12',
      readTime: 3,
      image: 'https://images.unsplash.com/photo-1519681393784-d120c3e90d74?w=800&h=600&fit=crop',
      href: '/articles/night-thoughts'
    },
    {
      id: 3,
      title: '关于写作的一些感悟',
      excerpt: '写作是一种表达，也是一种思考的方式。通过文字，我们可以记录生活，分享感悟。',
      date: '2024-03-10',
      readTime: 7,
      image: 'https://images.unsplash.com/photo-1455390582262-044cdead277a?w=800&h=600&fit=crop',
      href: '/articles/writing-thoughts'
    },
    {
      id: 4,
      title: '简单生活的美学',
      excerpt: '在这个复杂的世界里，简单生活成为了一种奢侈。让我们重新审视生活的本质。',
      date: '2024-03-08',
      readTime: 4,
      href: '/articles/simple-life'
    },
    {
      id: 5,
      title: '阅读的力量',
      excerpt: '书籍是人类进步的阶梯，阅读让我们的精神世界更加丰富。每一本好书都是一次心灵的旅行。',
      date: '2024-03-05',
      readTime: 6,
      image: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=800&h=600&fit=crop',
      href: '/articles/power-of-reading'
    },
    {
      id: 6,
      title: '城市漫步记',
      excerpt: '在城市中漫步，观察街头巷尾的风景，感受都市生活的节奏与温度。',
      date: '2024-03-02',
      readTime: 5,
      image: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800&h=600&fit=crop',
      href: '/articles/city-walk'
    }
  ];

  return (
    <div className="home-page">
      <Header />

      <main className="home-page__main">
        {/* 英雄区域 */}
        <section className="home-page__hero">
          <div className="home-page__hero-container">
            <h1 className="home-page__hero-title">墨韵流光</h1>
            <p className="home-page__hero-subtitle">
              在数字世界中捕捉思想与文字流动的瞬间美感
            </p>
            <div className="home-page__hero-actions">
              <Button
                onClick={() => {
                  window.history.pushState({}, '', '/article');
                  window.dispatchEvent(new PopStateEvent('popstate'));
                }}
                variant="primary"
              >
                开始阅读
              </Button>
              <Button
                onClick={() => {
                  window.history.pushState({}, '', '/about');
                  window.dispatchEvent(new PopStateEvent('popstate'));
                }}
                variant="secondary"
              >
                了解更多
              </Button>
            </div>
          </div>
        </section>

        {/* 文章列表 */}
        <section className="home-page__articles">
          <div className="home-page__articles-container">
            <div className="home-page__articles-grid">
              {articles.map((article) => (
                <ArticleCard
                  key={article.id}
                  title={article.title}
                  excerpt={article.excerpt}
                  date={article.date}
                  readTime={article.readTime}
                  image={article.image}
                  href={article.href}
                  size={article.size}
                />
              ))}
            </div>

            <div className="home-page__articles-more">
              <Button href="/articles" variant="secondary" size="large">
                查看更多文章
              </Button>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default HomePage;
