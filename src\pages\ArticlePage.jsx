import React from 'react';
import Header from '../components/layout/Header';
import Footer from '../components/layout/Footer';
import MarkdownRenderer from '../components/ui/MarkdownRenderer';
import Link from '../components/ui/Link';
import { articles } from '../data/articles';
import './ArticlePage.css';

const ArticlePage = () => {
  // 使用第一篇文章作为示例
  const article = articles[0];

  return (
    <div className="article-page">
      <Header />

      <main className="article-page__main">
        {/* 文章头部 */}
        <header className="article-page__header">
          {article.coverImage && (
            <div className="article-page__cover">
              <img
                src={article.coverImage}
                alt={article.title}
                className="article-page__cover-image"
              />
            </div>
          )}

          <div className="article-page__header-content">
            <h1 className="article-page__title">{article.title}</h1>

            <div className="article-page__meta">
              {article.author && (
                <span className="article-page__author">{article.author}</span>
              )}

              {article.date && (
                <>
                  <span className="article-page__meta-separator">·</span>
                  <time className="article-page__date" dateTime={article.date}>
                    {new Date(article.date).toLocaleDateString('zh-CN', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </time>
                </>
              )}

              {article.readTime && (
                <>
                  <span className="article-page__meta-separator">·</span>
                  <span className="article-page__read-time">
                    {article.readTime} 分钟阅读
                  </span>
                </>
              )}
            </div>
          </div>
        </header>

        {/* 文章内容 */}
        <div className="article-page__container">
          <article className="article-page__content">
            <MarkdownRenderer
              content={article.content}
              className="article-page__body"
            />

            {/* 文章标签 */}
            {article.tags && article.tags.length > 0 && (
              <div className="article-page__tags">
                <span className="article-page__tags-label">标签：</span>
                <ul className="article-page__tags-list">
                  {article.tags.map((tag) => (
                    <li key={tag} className="article-page__tag">
                      <Link href={`/tags/${tag}`} underline={false}>
                        {tag}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </article>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default ArticlePage;
