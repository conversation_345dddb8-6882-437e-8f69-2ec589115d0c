import React from 'react';
import Header from '../components/layout/Header';
import Footer from '../components/layout/Footer';
import Blockquote from '../components/ui/Blockquote';
import Link from '../components/ui/Link';
import './ArticlePage.css';

const ArticlePage = () => {
  // 模拟文章数据
  const article = {
    title: '春日里的诗意时光',
    date: '2024-03-15',
    readTime: 5,
    author: '墨韵',
    content: `
      <p>春天来了，万物复苏，心中涌起无限的诗意。在这个美好的季节里，让我们一起感受生活的美好，体验文字的力量。</p>
      
      <p>阳光明媚的午后，坐在窗前，看着窗外的花草树木，感受着微风拂面的清新，心情也随之舒畅起来。这样的时刻，最适合捧起一本书，或者提笔写下内心的感受。</p>
      
      <h2>春天的色彩</h2>
      
      <p>春天是一个色彩斑斓的季节。嫩绿的新叶，粉红的桃花，金黄的迎春花，还有蓝天白云，构成了一幅美丽的画卷。这些色彩不仅仅是视觉上的享受，更是心灵的洗礼。</p>
      
      <p>在这样的季节里，我喜欢拿起相机，记录下这些美好的瞬间。每一张照片都是一个故事，一段回忆，一份感动。</p>
      
      <blockquote>
        <p>春天是大地的诗篇，每一个细节都值得我们去品味，去感受。</p>
        <footer>
          <cite>— 林清玄</cite>
        </footer>
      </blockquote>
      
      <h2>春日的声音</h2>
      
      <p>春天不仅有色彩，还有声音。鸟儿的鸣叫，溪水的潺潺，风吹过树叶的沙沙声，这些都是春天的声音。闭上眼睛，静静地聆听，你会发现，这些声音是如此的和谐，如此的美妙。</p>
      
      <p>有时候，我会带上一本书，找一个安静的角落，伴随着这些自然的声音，沉浸在阅读的世界里。这样的时光，是最为宝贵的。</p>
      
      <h2>春天的味道</h2>
      
      <p>春天还有独特的味道。新鲜的空气，刚刚萌发的嫩芽，盛开的花朵，这些都有着春天特有的气息。深呼吸，让这些气息充满肺腑，你会感到一种前所未有的清新和活力。</p>
      
      <p>在春天，我喜欢做的一件事就是泡一杯花茶，坐在阳台上，看书或者写作。茶香与春风交织，创造出一种独特的氛围，让人心旷神怡。</p>
      
      <h2>结语</h2>
      
      <p>春天是一个充满诗意的季节，它给我们带来了无限的灵感和创作的欲望。在这个季节里，让我们放慢脚步，感受生活的美好，用文字记录下这些美好的瞬间。</p>
      
      <p>愿每一个人都能在春天里找到属于自己的诗意时光。</p>
    `,
    tags: ['春天', '诗意', '生活感悟'],
    coverImage: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1200&h=600&fit=crop',
  };

  return (
    <div className="article-page">
      <Header />
      
      <main className="article-page__main">
        {/* 文章头部 */}
        <header className="article-page__header">
          {article.coverImage && (
            <div className="article-page__cover">
              <img
                src={article.coverImage}
                alt={article.title}
                className="article-page__cover-image"
              />
            </div>
          )}
          
          <div className="article-page__header-content">
            <h1 className="article-page__title">{article.title}</h1>
            
            <div className="article-page__meta">
              {article.author && (
                <span className="article-page__author">{article.author}</span>
              )}
              
              {article.date && (
                <>
                  <span className="article-page__meta-separator">·</span>
                  <time className="article-page__date" dateTime={article.date}>
                    {new Date(article.date).toLocaleDateString('zh-CN', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </time>
                </>
              )}
              
              {article.readTime && (
                <>
                  <span className="article-page__meta-separator">·</span>
                  <span className="article-page__read-time">
                    {article.readTime} 分钟阅读
                  </span>
                </>
              )}
            </div>
          </div>
        </header>
        
        {/* 文章内容 */}
        <div className="article-page__container">
          <article className="article-page__content">
            <div
              className="article-page__body"
              dangerouslySetInnerHTML={{ __html: article.content }}
            />
            
            {/* 文章标签 */}
            {article.tags && article.tags.length > 0 && (
              <div className="article-page__tags">
                <span className="article-page__tags-label">标签：</span>
                <ul className="article-page__tags-list">
                  {article.tags.map((tag) => (
                    <li key={tag} className="article-page__tag">
                      <Link href={`/tags/${tag}`} underline={false}>
                        {tag}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </article>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default ArticlePage;
