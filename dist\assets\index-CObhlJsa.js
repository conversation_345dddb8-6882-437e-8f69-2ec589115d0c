(function(){const l=document.createElement("link").relList;if(l&&l.supports&&l.supports("modulepreload"))return;for(const c of document.querySelectorAll('link[rel="modulepreload"]'))r(c);new MutationObserver(c=>{for(const d of c)if(d.type==="childList")for(const h of d.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&r(h)}).observe(document,{childList:!0,subtree:!0});function u(c){const d={};return c.integrity&&(d.integrity=c.integrity),c.referrerPolicy&&(d.referrerPolicy=c.referrerPolicy),c.crossOrigin==="use-credentials"?d.credentials="include":c.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function r(c){if(c.ep)return;c.ep=!0;const d=u(c);fetch(c.href,d)}})();var xo={exports:{}},ll={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Lm;function r1(){if(Lm)return ll;Lm=1;var a=Symbol.for("react.transitional.element"),l=Symbol.for("react.fragment");function u(r,c,d){var h=null;if(d!==void 0&&(h=""+d),c.key!==void 0&&(h=""+c.key),"key"in c){d={};for(var g in c)g!=="key"&&(d[g]=c[g])}else d=c;return c=d.ref,{$$typeof:a,type:r,key:h,ref:c!==void 0?c:null,props:d}}return ll.Fragment=l,ll.jsx=u,ll.jsxs=u,ll}var Hm;function o1(){return Hm||(Hm=1,xo.exports=r1()),xo.exports}var x=o1(),Ao={exports:{}},st={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qm;function c1(){if(qm)return st;qm=1;var a=Symbol.for("react.transitional.element"),l=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),h=Symbol.for("react.context"),g=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),b=Symbol.iterator;function E(T){return T===null||typeof T!="object"?null:(T=b&&T[b]||T["@@iterator"],typeof T=="function"?T:null)}var j={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},B=Object.assign,q={};function X(T,U,Q){this.props=T,this.context=U,this.refs=q,this.updater=Q||j}X.prototype.isReactComponent={},X.prototype.setState=function(T,U){if(typeof T!="object"&&typeof T!="function"&&T!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,T,U,"setState")},X.prototype.forceUpdate=function(T){this.updater.enqueueForceUpdate(this,T,"forceUpdate")};function Y(){}Y.prototype=X.prototype;function Z(T,U,Q){this.props=T,this.context=U,this.refs=q,this.updater=Q||j}var H=Z.prototype=new Y;H.constructor=Z,B(H,X.prototype),H.isPureReactComponent=!0;var et=Array.isArray,L={H:null,A:null,T:null,S:null,V:null},W=Object.prototype.hasOwnProperty;function nt(T,U,Q,G,$,dt){return Q=dt.ref,{$$typeof:a,type:T,key:U,ref:Q!==void 0?Q:null,props:dt}}function J(T,U){return nt(T.type,U,void 0,void 0,void 0,T.props)}function yt(T){return typeof T=="object"&&T!==null&&T.$$typeof===a}function Ot(T){var U={"=":"=0",":":"=2"};return"$"+T.replace(/[=:]/g,function(Q){return U[Q]})}var Xt=/\/+/g;function Ht(T,U){return typeof T=="object"&&T!==null&&T.key!=null?Ot(""+T.key):U.toString(36)}function ke(){}function Be(T){switch(T.status){case"fulfilled":return T.value;case"rejected":throw T.reason;default:switch(typeof T.status=="string"?T.then(ke,ke):(T.status="pending",T.then(function(U){T.status==="pending"&&(T.status="fulfilled",T.value=U)},function(U){T.status==="pending"&&(T.status="rejected",T.reason=U)})),T.status){case"fulfilled":return T.value;case"rejected":throw T.reason}}throw T}function qt(T,U,Q,G,$){var dt=typeof T;(dt==="undefined"||dt==="boolean")&&(T=null);var lt=!1;if(T===null)lt=!0;else switch(dt){case"bigint":case"string":case"number":lt=!0;break;case"object":switch(T.$$typeof){case a:case l:lt=!0;break;case v:return lt=T._init,qt(lt(T._payload),U,Q,G,$)}}if(lt)return $=$(T),lt=G===""?"."+Ht(T,0):G,et($)?(Q="",lt!=null&&(Q=lt.replace(Xt,"$&/")+"/"),qt($,U,Q,"",function(hn){return hn})):$!=null&&(yt($)&&($=J($,Q+($.key==null||T&&T.key===$.key?"":(""+$.key).replace(Xt,"$&/")+"/")+lt)),U.push($)),1;lt=0;var ce=G===""?".":G+":";if(et(T))for(var Et=0;Et<T.length;Et++)G=T[Et],dt=ce+Ht(G,Et),lt+=qt(G,U,Q,dt,$);else if(Et=E(T),typeof Et=="function")for(T=Et.call(T),Et=0;!(G=T.next()).done;)G=G.value,dt=ce+Ht(G,Et++),lt+=qt(G,U,Q,dt,$);else if(dt==="object"){if(typeof T.then=="function")return qt(Be(T),U,Q,G,$);throw U=String(T),Error("Objects are not valid as a React child (found: "+(U==="[object Object]"?"object with keys {"+Object.keys(T).join(", ")+"}":U)+"). If you meant to render a collection of children, use an array instead.")}return lt}function N(T,U,Q){if(T==null)return T;var G=[],$=0;return qt(T,G,"","",function(dt){return U.call(Q,dt,$++)}),G}function w(T){if(T._status===-1){var U=T._result;U=U(),U.then(function(Q){(T._status===0||T._status===-1)&&(T._status=1,T._result=Q)},function(Q){(T._status===0||T._status===-1)&&(T._status=2,T._result=Q)}),T._status===-1&&(T._status=0,T._result=U)}if(T._status===1)return T._result.default;throw T._result}var P=typeof reportError=="function"?reportError:function(T){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var U=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof T=="object"&&T!==null&&typeof T.message=="string"?String(T.message):String(T),error:T});if(!window.dispatchEvent(U))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",T);return}console.error(T)};function ft(){}return st.Children={map:N,forEach:function(T,U,Q){N(T,function(){U.apply(this,arguments)},Q)},count:function(T){var U=0;return N(T,function(){U++}),U},toArray:function(T){return N(T,function(U){return U})||[]},only:function(T){if(!yt(T))throw Error("React.Children.only expected to receive a single React element child.");return T}},st.Component=X,st.Fragment=u,st.Profiler=c,st.PureComponent=Z,st.StrictMode=r,st.Suspense=p,st.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=L,st.__COMPILER_RUNTIME={__proto__:null,c:function(T){return L.H.useMemoCache(T)}},st.cache=function(T){return function(){return T.apply(null,arguments)}},st.cloneElement=function(T,U,Q){if(T==null)throw Error("The argument must be a React element, but you passed "+T+".");var G=B({},T.props),$=T.key,dt=void 0;if(U!=null)for(lt in U.ref!==void 0&&(dt=void 0),U.key!==void 0&&($=""+U.key),U)!W.call(U,lt)||lt==="key"||lt==="__self"||lt==="__source"||lt==="ref"&&U.ref===void 0||(G[lt]=U[lt]);var lt=arguments.length-2;if(lt===1)G.children=Q;else if(1<lt){for(var ce=Array(lt),Et=0;Et<lt;Et++)ce[Et]=arguments[Et+2];G.children=ce}return nt(T.type,$,void 0,void 0,dt,G)},st.createContext=function(T){return T={$$typeof:h,_currentValue:T,_currentValue2:T,_threadCount:0,Provider:null,Consumer:null},T.Provider=T,T.Consumer={$$typeof:d,_context:T},T},st.createElement=function(T,U,Q){var G,$={},dt=null;if(U!=null)for(G in U.key!==void 0&&(dt=""+U.key),U)W.call(U,G)&&G!=="key"&&G!=="__self"&&G!=="__source"&&($[G]=U[G]);var lt=arguments.length-2;if(lt===1)$.children=Q;else if(1<lt){for(var ce=Array(lt),Et=0;Et<lt;Et++)ce[Et]=arguments[Et+2];$.children=ce}if(T&&T.defaultProps)for(G in lt=T.defaultProps,lt)$[G]===void 0&&($[G]=lt[G]);return nt(T,dt,void 0,void 0,null,$)},st.createRef=function(){return{current:null}},st.forwardRef=function(T){return{$$typeof:g,render:T}},st.isValidElement=yt,st.lazy=function(T){return{$$typeof:v,_payload:{_status:-1,_result:T},_init:w}},st.memo=function(T,U){return{$$typeof:m,type:T,compare:U===void 0?null:U}},st.startTransition=function(T){var U=L.T,Q={};L.T=Q;try{var G=T(),$=L.S;$!==null&&$(Q,G),typeof G=="object"&&G!==null&&typeof G.then=="function"&&G.then(ft,P)}catch(dt){P(dt)}finally{L.T=U}},st.unstable_useCacheRefresh=function(){return L.H.useCacheRefresh()},st.use=function(T){return L.H.use(T)},st.useActionState=function(T,U,Q){return L.H.useActionState(T,U,Q)},st.useCallback=function(T,U){return L.H.useCallback(T,U)},st.useContext=function(T){return L.H.useContext(T)},st.useDebugValue=function(){},st.useDeferredValue=function(T,U){return L.H.useDeferredValue(T,U)},st.useEffect=function(T,U,Q){var G=L.H;if(typeof Q=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return G.useEffect(T,U)},st.useId=function(){return L.H.useId()},st.useImperativeHandle=function(T,U,Q){return L.H.useImperativeHandle(T,U,Q)},st.useInsertionEffect=function(T,U){return L.H.useInsertionEffect(T,U)},st.useLayoutEffect=function(T,U){return L.H.useLayoutEffect(T,U)},st.useMemo=function(T,U){return L.H.useMemo(T,U)},st.useOptimistic=function(T,U){return L.H.useOptimistic(T,U)},st.useReducer=function(T,U,Q){return L.H.useReducer(T,U,Q)},st.useRef=function(T){return L.H.useRef(T)},st.useState=function(T){return L.H.useState(T)},st.useSyncExternalStore=function(T,U,Q){return L.H.useSyncExternalStore(T,U,Q)},st.useTransition=function(){return L.H.useTransition()},st.version="19.1.1",st}var Ym;function cc(){return Ym||(Ym=1,Ao.exports=c1()),Ao.exports}var K=cc(),Eo={exports:{}},sl={},Mo={exports:{}},Do={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gm;function f1(){return Gm||(Gm=1,function(a){function l(N,w){var P=N.length;N.push(w);t:for(;0<P;){var ft=P-1>>>1,T=N[ft];if(0<c(T,w))N[ft]=w,N[P]=T,P=ft;else break t}}function u(N){return N.length===0?null:N[0]}function r(N){if(N.length===0)return null;var w=N[0],P=N.pop();if(P!==w){N[0]=P;t:for(var ft=0,T=N.length,U=T>>>1;ft<U;){var Q=2*(ft+1)-1,G=N[Q],$=Q+1,dt=N[$];if(0>c(G,P))$<T&&0>c(dt,G)?(N[ft]=dt,N[$]=P,ft=$):(N[ft]=G,N[Q]=P,ft=Q);else if($<T&&0>c(dt,P))N[ft]=dt,N[$]=P,ft=$;else break t}}return w}function c(N,w){var P=N.sortIndex-w.sortIndex;return P!==0?P:N.id-w.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;a.unstable_now=function(){return d.now()}}else{var h=Date,g=h.now();a.unstable_now=function(){return h.now()-g}}var p=[],m=[],v=1,b=null,E=3,j=!1,B=!1,q=!1,X=!1,Y=typeof setTimeout=="function"?setTimeout:null,Z=typeof clearTimeout=="function"?clearTimeout:null,H=typeof setImmediate<"u"?setImmediate:null;function et(N){for(var w=u(m);w!==null;){if(w.callback===null)r(m);else if(w.startTime<=N)r(m),w.sortIndex=w.expirationTime,l(p,w);else break;w=u(m)}}function L(N){if(q=!1,et(N),!B)if(u(p)!==null)B=!0,W||(W=!0,Ht());else{var w=u(m);w!==null&&qt(L,w.startTime-N)}}var W=!1,nt=-1,J=5,yt=-1;function Ot(){return X?!0:!(a.unstable_now()-yt<J)}function Xt(){if(X=!1,W){var N=a.unstable_now();yt=N;var w=!0;try{t:{B=!1,q&&(q=!1,Z(nt),nt=-1),j=!0;var P=E;try{e:{for(et(N),b=u(p);b!==null&&!(b.expirationTime>N&&Ot());){var ft=b.callback;if(typeof ft=="function"){b.callback=null,E=b.priorityLevel;var T=ft(b.expirationTime<=N);if(N=a.unstable_now(),typeof T=="function"){b.callback=T,et(N),w=!0;break e}b===u(p)&&r(p),et(N)}else r(p);b=u(p)}if(b!==null)w=!0;else{var U=u(m);U!==null&&qt(L,U.startTime-N),w=!1}}break t}finally{b=null,E=P,j=!1}w=void 0}}finally{w?Ht():W=!1}}}var Ht;if(typeof H=="function")Ht=function(){H(Xt)};else if(typeof MessageChannel<"u"){var ke=new MessageChannel,Be=ke.port2;ke.port1.onmessage=Xt,Ht=function(){Be.postMessage(null)}}else Ht=function(){Y(Xt,0)};function qt(N,w){nt=Y(function(){N(a.unstable_now())},w)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function(N){N.callback=null},a.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):J=0<N?Math.floor(1e3/N):5},a.unstable_getCurrentPriorityLevel=function(){return E},a.unstable_next=function(N){switch(E){case 1:case 2:case 3:var w=3;break;default:w=E}var P=E;E=w;try{return N()}finally{E=P}},a.unstable_requestPaint=function(){X=!0},a.unstable_runWithPriority=function(N,w){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var P=E;E=N;try{return w()}finally{E=P}},a.unstable_scheduleCallback=function(N,w,P){var ft=a.unstable_now();switch(typeof P=="object"&&P!==null?(P=P.delay,P=typeof P=="number"&&0<P?ft+P:ft):P=ft,N){case 1:var T=-1;break;case 2:T=250;break;case 5:T=1073741823;break;case 4:T=1e4;break;default:T=5e3}return T=P+T,N={id:v++,callback:w,priorityLevel:N,startTime:P,expirationTime:T,sortIndex:-1},P>ft?(N.sortIndex=P,l(m,N),u(p)===null&&N===u(m)&&(q?(Z(nt),nt=-1):q=!0,qt(L,P-ft))):(N.sortIndex=T,l(p,N),B||j||(B=!0,W||(W=!0,Ht()))),N},a.unstable_shouldYield=Ot,a.unstable_wrapCallback=function(N){var w=E;return function(){var P=E;E=w;try{return N.apply(this,arguments)}finally{E=P}}}}(Do)),Do}var Xm;function h1(){return Xm||(Xm=1,Mo.exports=f1()),Mo.exports}var _o={exports:{}},te={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zm;function d1(){if(Zm)return te;Zm=1;var a=cc();function l(p){var m="https://react.dev/errors/"+p;if(1<arguments.length){m+="?args[]="+encodeURIComponent(arguments[1]);for(var v=2;v<arguments.length;v++)m+="&args[]="+encodeURIComponent(arguments[v])}return"Minified React error #"+p+"; visit "+m+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(){}var r={d:{f:u,r:function(){throw Error(l(522))},D:u,C:u,L:u,m:u,X:u,S:u,M:u},p:0,findDOMNode:null},c=Symbol.for("react.portal");function d(p,m,v){var b=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:c,key:b==null?null:""+b,children:p,containerInfo:m,implementation:v}}var h=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function g(p,m){if(p==="font")return"";if(typeof m=="string")return m==="use-credentials"?m:""}return te.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,te.createPortal=function(p,m){var v=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!m||m.nodeType!==1&&m.nodeType!==9&&m.nodeType!==11)throw Error(l(299));return d(p,m,null,v)},te.flushSync=function(p){var m=h.T,v=r.p;try{if(h.T=null,r.p=2,p)return p()}finally{h.T=m,r.p=v,r.d.f()}},te.preconnect=function(p,m){typeof p=="string"&&(m?(m=m.crossOrigin,m=typeof m=="string"?m==="use-credentials"?m:"":void 0):m=null,r.d.C(p,m))},te.prefetchDNS=function(p){typeof p=="string"&&r.d.D(p)},te.preinit=function(p,m){if(typeof p=="string"&&m&&typeof m.as=="string"){var v=m.as,b=g(v,m.crossOrigin),E=typeof m.integrity=="string"?m.integrity:void 0,j=typeof m.fetchPriority=="string"?m.fetchPriority:void 0;v==="style"?r.d.S(p,typeof m.precedence=="string"?m.precedence:void 0,{crossOrigin:b,integrity:E,fetchPriority:j}):v==="script"&&r.d.X(p,{crossOrigin:b,integrity:E,fetchPriority:j,nonce:typeof m.nonce=="string"?m.nonce:void 0})}},te.preinitModule=function(p,m){if(typeof p=="string")if(typeof m=="object"&&m!==null){if(m.as==null||m.as==="script"){var v=g(m.as,m.crossOrigin);r.d.M(p,{crossOrigin:v,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0})}}else m==null&&r.d.M(p)},te.preload=function(p,m){if(typeof p=="string"&&typeof m=="object"&&m!==null&&typeof m.as=="string"){var v=m.as,b=g(v,m.crossOrigin);r.d.L(p,v,{crossOrigin:b,integrity:typeof m.integrity=="string"?m.integrity:void 0,nonce:typeof m.nonce=="string"?m.nonce:void 0,type:typeof m.type=="string"?m.type:void 0,fetchPriority:typeof m.fetchPriority=="string"?m.fetchPriority:void 0,referrerPolicy:typeof m.referrerPolicy=="string"?m.referrerPolicy:void 0,imageSrcSet:typeof m.imageSrcSet=="string"?m.imageSrcSet:void 0,imageSizes:typeof m.imageSizes=="string"?m.imageSizes:void 0,media:typeof m.media=="string"?m.media:void 0})}},te.preloadModule=function(p,m){if(typeof p=="string")if(m){var v=g(m.as,m.crossOrigin);r.d.m(p,{as:typeof m.as=="string"&&m.as!=="script"?m.as:void 0,crossOrigin:v,integrity:typeof m.integrity=="string"?m.integrity:void 0})}else r.d.m(p)},te.requestFormReset=function(p){r.d.r(p)},te.unstable_batchedUpdates=function(p,m){return p(m)},te.useFormState=function(p,m,v){return h.H.useFormState(p,m,v)},te.useFormStatus=function(){return h.H.useHostTransitionStatus()},te.version="19.1.1",te}var Qm;function m1(){if(Qm)return _o.exports;Qm=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(l){console.error(l)}}return a(),_o.exports=d1(),_o.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Km;function p1(){if(Km)return sl;Km=1;var a=h1(),l=cc(),u=m1();function r(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function d(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function h(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function g(t){if(d(t)!==t)throw Error(r(188))}function p(t){var e=t.alternate;if(!e){if(e=d(t),e===null)throw Error(r(188));return e!==t?null:t}for(var n=t,i=e;;){var s=n.return;if(s===null)break;var o=s.alternate;if(o===null){if(i=s.return,i!==null){n=i;continue}break}if(s.child===o.child){for(o=s.child;o;){if(o===n)return g(s),t;if(o===i)return g(s),e;o=o.sibling}throw Error(r(188))}if(n.return!==i.return)n=s,i=o;else{for(var f=!1,y=s.child;y;){if(y===n){f=!0,n=s,i=o;break}if(y===i){f=!0,i=s,n=o;break}y=y.sibling}if(!f){for(y=o.child;y;){if(y===n){f=!0,n=o,i=s;break}if(y===i){f=!0,i=o,n=s;break}y=y.sibling}if(!f)throw Error(r(189))}}if(n.alternate!==i)throw Error(r(190))}if(n.tag!==3)throw Error(r(188));return n.stateNode.current===n?t:e}function m(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=m(t),e!==null)return e;t=t.sibling}return null}var v=Object.assign,b=Symbol.for("react.element"),E=Symbol.for("react.transitional.element"),j=Symbol.for("react.portal"),B=Symbol.for("react.fragment"),q=Symbol.for("react.strict_mode"),X=Symbol.for("react.profiler"),Y=Symbol.for("react.provider"),Z=Symbol.for("react.consumer"),H=Symbol.for("react.context"),et=Symbol.for("react.forward_ref"),L=Symbol.for("react.suspense"),W=Symbol.for("react.suspense_list"),nt=Symbol.for("react.memo"),J=Symbol.for("react.lazy"),yt=Symbol.for("react.activity"),Ot=Symbol.for("react.memo_cache_sentinel"),Xt=Symbol.iterator;function Ht(t){return t===null||typeof t!="object"?null:(t=Xt&&t[Xt]||t["@@iterator"],typeof t=="function"?t:null)}var ke=Symbol.for("react.client.reference");function Be(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===ke?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case B:return"Fragment";case X:return"Profiler";case q:return"StrictMode";case L:return"Suspense";case W:return"SuspenseList";case yt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case j:return"Portal";case H:return(t.displayName||"Context")+".Provider";case Z:return(t._context.displayName||"Context")+".Consumer";case et:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case nt:return e=t.displayName||null,e!==null?e:Be(t.type)||"Memo";case J:e=t._payload,t=t._init;try{return Be(t(e))}catch{}}return null}var qt=Array.isArray,N=l.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,w=u.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,P={pending:!1,data:null,method:null,action:null},ft=[],T=-1;function U(t){return{current:t}}function Q(t){0>T||(t.current=ft[T],ft[T]=null,T--)}function G(t,e){T++,ft[T]=t.current,t.current=e}var $=U(null),dt=U(null),lt=U(null),ce=U(null);function Et(t,e){switch(G(lt,e),G(dt,t),G($,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?hm(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=hm(e),t=dm(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Q($),G($,t)}function hn(){Q($),Q(dt),Q(lt)}function su(t){t.memoizedState!==null&&G(ce,t);var e=$.current,n=dm(e,t.type);e!==n&&(G(dt,t),G($,n))}function El(t){dt.current===t&&(Q($),Q(dt)),ce.current===t&&(Q(ce),tl._currentValue=P)}var uu=Object.prototype.hasOwnProperty,ru=a.unstable_scheduleCallback,ou=a.unstable_cancelCallback,qg=a.unstable_shouldYield,Yg=a.unstable_requestPaint,we=a.unstable_now,Gg=a.unstable_getCurrentPriorityLevel,Qc=a.unstable_ImmediatePriority,Kc=a.unstable_UserBlockingPriority,Ml=a.unstable_NormalPriority,Xg=a.unstable_LowPriority,kc=a.unstable_IdlePriority,Zg=a.log,Qg=a.unstable_setDisableYieldValue,ri=null,fe=null;function dn(t){if(typeof Zg=="function"&&Qg(t),fe&&typeof fe.setStrictMode=="function")try{fe.setStrictMode(ri,t)}catch{}}var he=Math.clz32?Math.clz32:Pg,Kg=Math.log,kg=Math.LN2;function Pg(t){return t>>>=0,t===0?32:31-(Kg(t)/kg|0)|0}var Dl=256,_l=4194304;function Yn(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Rl(t,e,n){var i=t.pendingLanes;if(i===0)return 0;var s=0,o=t.suspendedLanes,f=t.pingedLanes;t=t.warmLanes;var y=i&134217727;return y!==0?(i=y&~o,i!==0?s=Yn(i):(f&=y,f!==0?s=Yn(f):n||(n=y&~t,n!==0&&(s=Yn(n))))):(y=i&~o,y!==0?s=Yn(y):f!==0?s=Yn(f):n||(n=i&~t,n!==0&&(s=Yn(n)))),s===0?0:e!==0&&e!==s&&(e&o)===0&&(o=s&-s,n=e&-e,o>=n||o===32&&(n&4194048)!==0)?e:s}function oi(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function Jg(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Pc(){var t=Dl;return Dl<<=1,(Dl&4194048)===0&&(Dl=256),t}function Jc(){var t=_l;return _l<<=1,(_l&62914560)===0&&(_l=4194304),t}function cu(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function ci(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Fg(t,e,n,i,s,o){var f=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var y=t.entanglements,S=t.expirationTimes,_=t.hiddenUpdates;for(n=f&~n;0<n;){var C=31-he(n),z=1<<C;y[C]=0,S[C]=-1;var R=_[C];if(R!==null)for(_[C]=null,C=0;C<R.length;C++){var O=R[C];O!==null&&(O.lane&=-536870913)}n&=~z}i!==0&&Fc(t,i,0),o!==0&&s===0&&t.tag!==0&&(t.suspendedLanes|=o&~(f&~e))}function Fc(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var i=31-he(e);t.entangledLanes|=e,t.entanglements[i]=t.entanglements[i]|1073741824|n&4194090}function Wc(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var i=31-he(n),s=1<<i;s&e|t[i]&e&&(t[i]|=e),n&=~s}}function fu(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function hu(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function $c(){var t=w.p;return t!==0?t:(t=window.event,t===void 0?32:jm(t.type))}function Wg(t,e){var n=w.p;try{return w.p=t,e()}finally{w.p=n}}var mn=Math.random().toString(36).slice(2),$t="__reactFiber$"+mn,ie="__reactProps$"+mn,ha="__reactContainer$"+mn,du="__reactEvents$"+mn,$g="__reactListeners$"+mn,Ig="__reactHandles$"+mn,Ic="__reactResources$"+mn,fi="__reactMarker$"+mn;function mu(t){delete t[$t],delete t[ie],delete t[du],delete t[$g],delete t[Ig]}function da(t){var e=t[$t];if(e)return e;for(var n=t.parentNode;n;){if(e=n[ha]||n[$t]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=gm(t);t!==null;){if(n=t[$t])return n;t=gm(t)}return e}t=n,n=t.parentNode}return null}function ma(t){if(t=t[$t]||t[ha]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function hi(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(r(33))}function pa(t){var e=t[Ic];return e||(e=t[Ic]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Zt(t){t[fi]=!0}var tf=new Set,ef={};function Gn(t,e){ya(t,e),ya(t+"Capture",e)}function ya(t,e){for(ef[t]=e,t=0;t<e.length;t++)tf.add(e[t])}var t0=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),nf={},af={};function e0(t){return uu.call(af,t)?!0:uu.call(nf,t)?!1:t0.test(t)?af[t]=!0:(nf[t]=!0,!1)}function Ol(t,e,n){if(e0(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var i=e.toLowerCase().slice(0,5);if(i!=="data-"&&i!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function Cl(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function Pe(t,e,n,i){if(i===null)t.removeAttribute(n);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+i)}}var pu,lf;function ga(t){if(pu===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);pu=e&&e[1]||"",lf=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+pu+t+lf}var yu=!1;function gu(t,e){if(!t||yu)return"";yu=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var i={DetermineComponentFrameRoot:function(){try{if(e){var z=function(){throw Error()};if(Object.defineProperty(z.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(z,[])}catch(O){var R=O}Reflect.construct(t,[],z)}else{try{z.call()}catch(O){R=O}t.call(z.prototype)}}else{try{throw Error()}catch(O){R=O}(z=t())&&typeof z.catch=="function"&&z.catch(function(){})}}catch(O){if(O&&R&&typeof O.stack=="string")return[O.stack,R.stack]}return[null,null]}};i.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var s=Object.getOwnPropertyDescriptor(i.DetermineComponentFrameRoot,"name");s&&s.configurable&&Object.defineProperty(i.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var o=i.DetermineComponentFrameRoot(),f=o[0],y=o[1];if(f&&y){var S=f.split(`
`),_=y.split(`
`);for(s=i=0;i<S.length&&!S[i].includes("DetermineComponentFrameRoot");)i++;for(;s<_.length&&!_[s].includes("DetermineComponentFrameRoot");)s++;if(i===S.length||s===_.length)for(i=S.length-1,s=_.length-1;1<=i&&0<=s&&S[i]!==_[s];)s--;for(;1<=i&&0<=s;i--,s--)if(S[i]!==_[s]){if(i!==1||s!==1)do if(i--,s--,0>s||S[i]!==_[s]){var C=`
`+S[i].replace(" at new "," at ");return t.displayName&&C.includes("<anonymous>")&&(C=C.replace("<anonymous>",t.displayName)),C}while(1<=i&&0<=s);break}}}finally{yu=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?ga(n):""}function n0(t){switch(t.tag){case 26:case 27:case 5:return ga(t.type);case 16:return ga("Lazy");case 13:return ga("Suspense");case 19:return ga("SuspenseList");case 0:case 15:return gu(t.type,!1);case 11:return gu(t.type.render,!1);case 1:return gu(t.type,!0);case 31:return ga("Activity");default:return""}}function sf(t){try{var e="";do e+=n0(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function be(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function uf(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function a0(t){var e=uf(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),i=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,o=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return s.call(this)},set:function(f){i=""+f,o.call(this,f)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return i},setValue:function(f){i=""+f},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Nl(t){t._valueTracker||(t._valueTracker=a0(t))}function rf(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),i="";return t&&(i=uf(t)?t.checked?"true":"false":t.value),t=i,t!==n?(e.setValue(t),!0):!1}function jl(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var i0=/[\n"\\]/g;function Te(t){return t.replace(i0,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function vu(t,e,n,i,s,o,f,y){t.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?t.type=f:t.removeAttribute("type"),e!=null?f==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+be(e)):t.value!==""+be(e)&&(t.value=""+be(e)):f!=="submit"&&f!=="reset"||t.removeAttribute("value"),e!=null?Su(t,f,be(e)):n!=null?Su(t,f,be(n)):i!=null&&t.removeAttribute("value"),s==null&&o!=null&&(t.defaultChecked=!!o),s!=null&&(t.checked=s&&typeof s!="function"&&typeof s!="symbol"),y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?t.name=""+be(y):t.removeAttribute("name")}function of(t,e,n,i,s,o,f,y){if(o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"&&(t.type=o),e!=null||n!=null){if(!(o!=="submit"&&o!=="reset"||e!=null))return;n=n!=null?""+be(n):"",e=e!=null?""+be(e):n,y||e===t.value||(t.value=e),t.defaultValue=e}i=i??s,i=typeof i!="function"&&typeof i!="symbol"&&!!i,t.checked=y?t.checked:!!i,t.defaultChecked=!!i,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(t.name=f)}function Su(t,e,n){e==="number"&&jl(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function va(t,e,n,i){if(t=t.options,e){e={};for(var s=0;s<n.length;s++)e["$"+n[s]]=!0;for(n=0;n<t.length;n++)s=e.hasOwnProperty("$"+t[n].value),t[n].selected!==s&&(t[n].selected=s),s&&i&&(t[n].defaultSelected=!0)}else{for(n=""+be(n),e=null,s=0;s<t.length;s++){if(t[s].value===n){t[s].selected=!0,i&&(t[s].defaultSelected=!0);return}e!==null||t[s].disabled||(e=t[s])}e!==null&&(e.selected=!0)}}function cf(t,e,n){if(e!=null&&(e=""+be(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+be(n):""}function ff(t,e,n,i){if(e==null){if(i!=null){if(n!=null)throw Error(r(92));if(qt(i)){if(1<i.length)throw Error(r(93));i=i[0]}n=i}n==null&&(n=""),e=n}n=be(e),t.defaultValue=n,i=t.textContent,i===n&&i!==""&&i!==null&&(t.value=i)}function Sa(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var l0=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function hf(t,e,n){var i=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?i?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":i?t.setProperty(e,n):typeof n!="number"||n===0||l0.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function df(t,e,n){if(e!=null&&typeof e!="object")throw Error(r(62));if(t=t.style,n!=null){for(var i in n)!n.hasOwnProperty(i)||e!=null&&e.hasOwnProperty(i)||(i.indexOf("--")===0?t.setProperty(i,""):i==="float"?t.cssFloat="":t[i]="");for(var s in e)i=e[s],e.hasOwnProperty(s)&&n[s]!==i&&hf(t,s,i)}else for(var o in e)e.hasOwnProperty(o)&&hf(t,o,e[o])}function bu(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var s0=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),u0=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Vl(t){return u0.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var Tu=null;function xu(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var ba=null,Ta=null;function mf(t){var e=ma(t);if(e&&(t=e.stateNode)){var n=t[ie]||null;t:switch(t=e.stateNode,e.type){case"input":if(vu(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Te(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var i=n[e];if(i!==t&&i.form===t.form){var s=i[ie]||null;if(!s)throw Error(r(90));vu(i,s.value,s.defaultValue,s.defaultValue,s.checked,s.defaultChecked,s.type,s.name)}}for(e=0;e<n.length;e++)i=n[e],i.form===t.form&&rf(i)}break t;case"textarea":cf(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&va(t,!!n.multiple,e,!1)}}}var Au=!1;function pf(t,e,n){if(Au)return t(e,n);Au=!0;try{var i=t(e);return i}finally{if(Au=!1,(ba!==null||Ta!==null)&&(vs(),ba&&(e=ba,t=Ta,Ta=ba=null,mf(e),t)))for(e=0;e<t.length;e++)mf(t[e])}}function di(t,e){var n=t.stateNode;if(n===null)return null;var i=n[ie]||null;if(i===null)return null;n=i[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(t=t.type,i=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!i;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(r(231,e,typeof n));return n}var Je=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Eu=!1;if(Je)try{var mi={};Object.defineProperty(mi,"passive",{get:function(){Eu=!0}}),window.addEventListener("test",mi,mi),window.removeEventListener("test",mi,mi)}catch{Eu=!1}var pn=null,Mu=null,zl=null;function yf(){if(zl)return zl;var t,e=Mu,n=e.length,i,s="value"in pn?pn.value:pn.textContent,o=s.length;for(t=0;t<n&&e[t]===s[t];t++);var f=n-t;for(i=1;i<=f&&e[n-i]===s[o-i];i++);return zl=s.slice(t,1<i?1-i:void 0)}function Ul(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Bl(){return!0}function gf(){return!1}function le(t){function e(n,i,s,o,f){this._reactName=n,this._targetInst=s,this.type=i,this.nativeEvent=o,this.target=f,this.currentTarget=null;for(var y in t)t.hasOwnProperty(y)&&(n=t[y],this[y]=n?n(o):o[y]);return this.isDefaultPrevented=(o.defaultPrevented!=null?o.defaultPrevented:o.returnValue===!1)?Bl:gf,this.isPropagationStopped=gf,this}return v(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Bl)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Bl)},persist:function(){},isPersistent:Bl}),e}var Xn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},wl=le(Xn),pi=v({},Xn,{view:0,detail:0}),r0=le(pi),Du,_u,yi,Ll=v({},pi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ou,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==yi&&(yi&&t.type==="mousemove"?(Du=t.screenX-yi.screenX,_u=t.screenY-yi.screenY):_u=Du=0,yi=t),Du)},movementY:function(t){return"movementY"in t?t.movementY:_u}}),vf=le(Ll),o0=v({},Ll,{dataTransfer:0}),c0=le(o0),f0=v({},pi,{relatedTarget:0}),Ru=le(f0),h0=v({},Xn,{animationName:0,elapsedTime:0,pseudoElement:0}),d0=le(h0),m0=v({},Xn,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),p0=le(m0),y0=v({},Xn,{data:0}),Sf=le(y0),g0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},v0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},S0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function b0(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=S0[t])?!!e[t]:!1}function Ou(){return b0}var T0=v({},pi,{key:function(t){if(t.key){var e=g0[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Ul(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?v0[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ou,charCode:function(t){return t.type==="keypress"?Ul(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Ul(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),x0=le(T0),A0=v({},Ll,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),bf=le(A0),E0=v({},pi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ou}),M0=le(E0),D0=v({},Xn,{propertyName:0,elapsedTime:0,pseudoElement:0}),_0=le(D0),R0=v({},Ll,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),O0=le(R0),C0=v({},Xn,{newState:0,oldState:0}),N0=le(C0),j0=[9,13,27,32],Cu=Je&&"CompositionEvent"in window,gi=null;Je&&"documentMode"in document&&(gi=document.documentMode);var V0=Je&&"TextEvent"in window&&!gi,Tf=Je&&(!Cu||gi&&8<gi&&11>=gi),xf=" ",Af=!1;function Ef(t,e){switch(t){case"keyup":return j0.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Mf(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var xa=!1;function z0(t,e){switch(t){case"compositionend":return Mf(e);case"keypress":return e.which!==32?null:(Af=!0,xf);case"textInput":return t=e.data,t===xf&&Af?null:t;default:return null}}function U0(t,e){if(xa)return t==="compositionend"||!Cu&&Ef(t,e)?(t=yf(),zl=Mu=pn=null,xa=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Tf&&e.locale!=="ko"?null:e.data;default:return null}}var B0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Df(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!B0[t.type]:e==="textarea"}function _f(t,e,n,i){ba?Ta?Ta.push(i):Ta=[i]:ba=i,e=Es(e,"onChange"),0<e.length&&(n=new wl("onChange","change",null,n,i),t.push({event:n,listeners:e}))}var vi=null,Si=null;function w0(t){um(t,0)}function Hl(t){var e=hi(t);if(rf(e))return t}function Rf(t,e){if(t==="change")return e}var Of=!1;if(Je){var Nu;if(Je){var ju="oninput"in document;if(!ju){var Cf=document.createElement("div");Cf.setAttribute("oninput","return;"),ju=typeof Cf.oninput=="function"}Nu=ju}else Nu=!1;Of=Nu&&(!document.documentMode||9<document.documentMode)}function Nf(){vi&&(vi.detachEvent("onpropertychange",jf),Si=vi=null)}function jf(t){if(t.propertyName==="value"&&Hl(Si)){var e=[];_f(e,Si,t,xu(t)),pf(w0,e)}}function L0(t,e,n){t==="focusin"?(Nf(),vi=e,Si=n,vi.attachEvent("onpropertychange",jf)):t==="focusout"&&Nf()}function H0(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Hl(Si)}function q0(t,e){if(t==="click")return Hl(e)}function Y0(t,e){if(t==="input"||t==="change")return Hl(e)}function G0(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var de=typeof Object.is=="function"?Object.is:G0;function bi(t,e){if(de(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),i=Object.keys(e);if(n.length!==i.length)return!1;for(i=0;i<n.length;i++){var s=n[i];if(!uu.call(e,s)||!de(t[s],e[s]))return!1}return!0}function Vf(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function zf(t,e){var n=Vf(t);t=0;for(var i;n;){if(n.nodeType===3){if(i=t+n.textContent.length,t<=e&&i>=e)return{node:n,offset:e-t};t=i}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=Vf(n)}}function Uf(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Uf(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Bf(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=jl(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=jl(t.document)}return e}function Vu(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var X0=Je&&"documentMode"in document&&11>=document.documentMode,Aa=null,zu=null,Ti=null,Uu=!1;function wf(t,e,n){var i=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Uu||Aa==null||Aa!==jl(i)||(i=Aa,"selectionStart"in i&&Vu(i)?i={start:i.selectionStart,end:i.selectionEnd}:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection(),i={anchorNode:i.anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset}),Ti&&bi(Ti,i)||(Ti=i,i=Es(zu,"onSelect"),0<i.length&&(e=new wl("onSelect","select",null,e,n),t.push({event:e,listeners:i}),e.target=Aa)))}function Zn(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var Ea={animationend:Zn("Animation","AnimationEnd"),animationiteration:Zn("Animation","AnimationIteration"),animationstart:Zn("Animation","AnimationStart"),transitionrun:Zn("Transition","TransitionRun"),transitionstart:Zn("Transition","TransitionStart"),transitioncancel:Zn("Transition","TransitionCancel"),transitionend:Zn("Transition","TransitionEnd")},Bu={},Lf={};Je&&(Lf=document.createElement("div").style,"AnimationEvent"in window||(delete Ea.animationend.animation,delete Ea.animationiteration.animation,delete Ea.animationstart.animation),"TransitionEvent"in window||delete Ea.transitionend.transition);function Qn(t){if(Bu[t])return Bu[t];if(!Ea[t])return t;var e=Ea[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in Lf)return Bu[t]=e[n];return t}var Hf=Qn("animationend"),qf=Qn("animationiteration"),Yf=Qn("animationstart"),Z0=Qn("transitionrun"),Q0=Qn("transitionstart"),K0=Qn("transitioncancel"),Gf=Qn("transitionend"),Xf=new Map,wu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");wu.push("scrollEnd");function je(t,e){Xf.set(t,e),Gn(e,[t])}var Zf=new WeakMap;function xe(t,e){if(typeof t=="object"&&t!==null){var n=Zf.get(t);return n!==void 0?n:(e={value:t,source:e,stack:sf(e)},Zf.set(t,e),e)}return{value:t,source:e,stack:sf(e)}}var Ae=[],Ma=0,Lu=0;function ql(){for(var t=Ma,e=Lu=Ma=0;e<t;){var n=Ae[e];Ae[e++]=null;var i=Ae[e];Ae[e++]=null;var s=Ae[e];Ae[e++]=null;var o=Ae[e];if(Ae[e++]=null,i!==null&&s!==null){var f=i.pending;f===null?s.next=s:(s.next=f.next,f.next=s),i.pending=s}o!==0&&Qf(n,s,o)}}function Yl(t,e,n,i){Ae[Ma++]=t,Ae[Ma++]=e,Ae[Ma++]=n,Ae[Ma++]=i,Lu|=i,t.lanes|=i,t=t.alternate,t!==null&&(t.lanes|=i)}function Hu(t,e,n,i){return Yl(t,e,n,i),Gl(t)}function Da(t,e){return Yl(t,null,null,e),Gl(t)}function Qf(t,e,n){t.lanes|=n;var i=t.alternate;i!==null&&(i.lanes|=n);for(var s=!1,o=t.return;o!==null;)o.childLanes|=n,i=o.alternate,i!==null&&(i.childLanes|=n),o.tag===22&&(t=o.stateNode,t===null||t._visibility&1||(s=!0)),t=o,o=o.return;return t.tag===3?(o=t.stateNode,s&&e!==null&&(s=31-he(n),t=o.hiddenUpdates,i=t[s],i===null?t[s]=[e]:i.push(e),e.lane=n|536870912),o):null}function Gl(t){if(50<Ki)throw Ki=0,Qr=null,Error(r(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var _a={};function k0(t,e,n,i){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function me(t,e,n,i){return new k0(t,e,n,i)}function qu(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Fe(t,e){var n=t.alternate;return n===null?(n=me(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function Kf(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Xl(t,e,n,i,s,o){var f=0;if(i=t,typeof t=="function")qu(t)&&(f=1);else if(typeof t=="string")f=Jv(t,n,$.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case yt:return t=me(31,n,e,s),t.elementType=yt,t.lanes=o,t;case B:return Kn(n.children,s,o,e);case q:f=8,s|=24;break;case X:return t=me(12,n,e,s|2),t.elementType=X,t.lanes=o,t;case L:return t=me(13,n,e,s),t.elementType=L,t.lanes=o,t;case W:return t=me(19,n,e,s),t.elementType=W,t.lanes=o,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case Y:case H:f=10;break t;case Z:f=9;break t;case et:f=11;break t;case nt:f=14;break t;case J:f=16,i=null;break t}f=29,n=Error(r(130,t===null?"null":typeof t,"")),i=null}return e=me(f,n,e,s),e.elementType=t,e.type=i,e.lanes=o,e}function Kn(t,e,n,i){return t=me(7,t,i,e),t.lanes=n,t}function Yu(t,e,n){return t=me(6,t,null,e),t.lanes=n,t}function Gu(t,e,n){return e=me(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Ra=[],Oa=0,Zl=null,Ql=0,Ee=[],Me=0,kn=null,We=1,$e="";function Pn(t,e){Ra[Oa++]=Ql,Ra[Oa++]=Zl,Zl=t,Ql=e}function kf(t,e,n){Ee[Me++]=We,Ee[Me++]=$e,Ee[Me++]=kn,kn=t;var i=We;t=$e;var s=32-he(i)-1;i&=~(1<<s),n+=1;var o=32-he(e)+s;if(30<o){var f=s-s%5;o=(i&(1<<f)-1).toString(32),i>>=f,s-=f,We=1<<32-he(e)+s|n<<s|i,$e=o+t}else We=1<<o|n<<s|i,$e=t}function Xu(t){t.return!==null&&(Pn(t,1),kf(t,1,0))}function Zu(t){for(;t===Zl;)Zl=Ra[--Oa],Ra[Oa]=null,Ql=Ra[--Oa],Ra[Oa]=null;for(;t===kn;)kn=Ee[--Me],Ee[Me]=null,$e=Ee[--Me],Ee[Me]=null,We=Ee[--Me],Ee[Me]=null}var ne=null,Ct=null,pt=!1,Jn=null,Le=!1,Qu=Error(r(519));function Fn(t){var e=Error(r(418,""));throw Ei(xe(e,t)),Qu}function Pf(t){var e=t.stateNode,n=t.type,i=t.memoizedProps;switch(e[$t]=t,e[ie]=i,n){case"dialog":ct("cancel",e),ct("close",e);break;case"iframe":case"object":case"embed":ct("load",e);break;case"video":case"audio":for(n=0;n<Pi.length;n++)ct(Pi[n],e);break;case"source":ct("error",e);break;case"img":case"image":case"link":ct("error",e),ct("load",e);break;case"details":ct("toggle",e);break;case"input":ct("invalid",e),of(e,i.value,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name,!0),Nl(e);break;case"select":ct("invalid",e);break;case"textarea":ct("invalid",e),ff(e,i.value,i.defaultValue,i.children),Nl(e)}n=i.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||i.suppressHydrationWarning===!0||fm(e.textContent,n)?(i.popover!=null&&(ct("beforetoggle",e),ct("toggle",e)),i.onScroll!=null&&ct("scroll",e),i.onScrollEnd!=null&&ct("scrollend",e),i.onClick!=null&&(e.onclick=Ms),e=!0):e=!1,e||Fn(t)}function Jf(t){for(ne=t.return;ne;)switch(ne.tag){case 5:case 13:Le=!1;return;case 27:case 3:Le=!0;return;default:ne=ne.return}}function xi(t){if(t!==ne)return!1;if(!pt)return Jf(t),pt=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||uo(t.type,t.memoizedProps)),n=!n),n&&Ct&&Fn(t),Jf(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(r(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){Ct=ze(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}Ct=null}}else e===27?(e=Ct,Nn(t.type)?(t=fo,fo=null,Ct=t):Ct=e):Ct=ne?ze(t.stateNode.nextSibling):null;return!0}function Ai(){Ct=ne=null,pt=!1}function Ff(){var t=Jn;return t!==null&&(re===null?re=t:re.push.apply(re,t),Jn=null),t}function Ei(t){Jn===null?Jn=[t]:Jn.push(t)}var Ku=U(null),Wn=null,Ie=null;function yn(t,e,n){G(Ku,e._currentValue),e._currentValue=n}function tn(t){t._currentValue=Ku.current,Q(Ku)}function ku(t,e,n){for(;t!==null;){var i=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,i!==null&&(i.childLanes|=e)):i!==null&&(i.childLanes&e)!==e&&(i.childLanes|=e),t===n)break;t=t.return}}function Pu(t,e,n,i){var s=t.child;for(s!==null&&(s.return=t);s!==null;){var o=s.dependencies;if(o!==null){var f=s.child;o=o.firstContext;t:for(;o!==null;){var y=o;o=s;for(var S=0;S<e.length;S++)if(y.context===e[S]){o.lanes|=n,y=o.alternate,y!==null&&(y.lanes|=n),ku(o.return,n,t),i||(f=null);break t}o=y.next}}else if(s.tag===18){if(f=s.return,f===null)throw Error(r(341));f.lanes|=n,o=f.alternate,o!==null&&(o.lanes|=n),ku(f,n,t),f=null}else f=s.child;if(f!==null)f.return=s;else for(f=s;f!==null;){if(f===t){f=null;break}if(s=f.sibling,s!==null){s.return=f.return,f=s;break}f=f.return}s=f}}function Mi(t,e,n,i){t=null;for(var s=e,o=!1;s!==null;){if(!o){if((s.flags&524288)!==0)o=!0;else if((s.flags&262144)!==0)break}if(s.tag===10){var f=s.alternate;if(f===null)throw Error(r(387));if(f=f.memoizedProps,f!==null){var y=s.type;de(s.pendingProps.value,f.value)||(t!==null?t.push(y):t=[y])}}else if(s===ce.current){if(f=s.alternate,f===null)throw Error(r(387));f.memoizedState.memoizedState!==s.memoizedState.memoizedState&&(t!==null?t.push(tl):t=[tl])}s=s.return}t!==null&&Pu(e,t,n,i),e.flags|=262144}function Kl(t){for(t=t.firstContext;t!==null;){if(!de(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function $n(t){Wn=t,Ie=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function It(t){return Wf(Wn,t)}function kl(t,e){return Wn===null&&$n(t),Wf(t,e)}function Wf(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},Ie===null){if(t===null)throw Error(r(308));Ie=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Ie=Ie.next=e;return n}var P0=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,i){t.push(i)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},J0=a.unstable_scheduleCallback,F0=a.unstable_NormalPriority,Yt={$$typeof:H,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Ju(){return{controller:new P0,data:new Map,refCount:0}}function Di(t){t.refCount--,t.refCount===0&&J0(F0,function(){t.controller.abort()})}var _i=null,Fu=0,Ca=0,Na=null;function W0(t,e){if(_i===null){var n=_i=[];Fu=0,Ca=$r(),Na={status:"pending",value:void 0,then:function(i){n.push(i)}}}return Fu++,e.then($f,$f),e}function $f(){if(--Fu===0&&_i!==null){Na!==null&&(Na.status="fulfilled");var t=_i;_i=null,Ca=0,Na=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function $0(t,e){var n=[],i={status:"pending",value:null,reason:null,then:function(s){n.push(s)}};return t.then(function(){i.status="fulfilled",i.value=e;for(var s=0;s<n.length;s++)(0,n[s])(e)},function(s){for(i.status="rejected",i.reason=s,s=0;s<n.length;s++)(0,n[s])(void 0)}),i}var If=N.S;N.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&W0(t,e),If!==null&&If(t,e)};var In=U(null);function Wu(){var t=In.current;return t!==null?t:At.pooledCache}function Pl(t,e){e===null?G(In,In.current):G(In,e.pool)}function th(){var t=Wu();return t===null?null:{parent:Yt._currentValue,pool:t}}var Ri=Error(r(460)),eh=Error(r(474)),Jl=Error(r(542)),$u={then:function(){}};function nh(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Fl(){}function ah(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(Fl,Fl),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,lh(t),t;default:if(typeof e.status=="string")e.then(Fl,Fl);else{if(t=At,t!==null&&100<t.shellSuspendCounter)throw Error(r(482));t=e,t.status="pending",t.then(function(i){if(e.status==="pending"){var s=e;s.status="fulfilled",s.value=i}},function(i){if(e.status==="pending"){var s=e;s.status="rejected",s.reason=i}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,lh(t),t}throw Oi=e,Ri}}var Oi=null;function ih(){if(Oi===null)throw Error(r(459));var t=Oi;return Oi=null,t}function lh(t){if(t===Ri||t===Jl)throw Error(r(483))}var gn=!1;function Iu(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function tr(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function vn(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Sn(t,e,n){var i=t.updateQueue;if(i===null)return null;if(i=i.shared,(gt&2)!==0){var s=i.pending;return s===null?e.next=e:(e.next=s.next,s.next=e),i.pending=e,e=Gl(t),Qf(t,null,n),e}return Yl(t,i,e,n),Gl(t)}function Ci(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var i=e.lanes;i&=t.pendingLanes,n|=i,e.lanes=n,Wc(t,n)}}function er(t,e){var n=t.updateQueue,i=t.alternate;if(i!==null&&(i=i.updateQueue,n===i)){var s=null,o=null;if(n=n.firstBaseUpdate,n!==null){do{var f={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};o===null?s=o=f:o=o.next=f,n=n.next}while(n!==null);o===null?s=o=e:o=o.next=e}else s=o=e;n={baseState:i.baseState,firstBaseUpdate:s,lastBaseUpdate:o,shared:i.shared,callbacks:i.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var nr=!1;function Ni(){if(nr){var t=Na;if(t!==null)throw t}}function ji(t,e,n,i){nr=!1;var s=t.updateQueue;gn=!1;var o=s.firstBaseUpdate,f=s.lastBaseUpdate,y=s.shared.pending;if(y!==null){s.shared.pending=null;var S=y,_=S.next;S.next=null,f===null?o=_:f.next=_,f=S;var C=t.alternate;C!==null&&(C=C.updateQueue,y=C.lastBaseUpdate,y!==f&&(y===null?C.firstBaseUpdate=_:y.next=_,C.lastBaseUpdate=S))}if(o!==null){var z=s.baseState;f=0,C=_=S=null,y=o;do{var R=y.lane&-536870913,O=R!==y.lane;if(O?(ht&R)===R:(i&R)===R){R!==0&&R===Ca&&(nr=!0),C!==null&&(C=C.next={lane:0,tag:y.tag,payload:y.payload,callback:null,next:null});t:{var at=t,I=y;R=e;var Tt=n;switch(I.tag){case 1:if(at=I.payload,typeof at=="function"){z=at.call(Tt,z,R);break t}z=at;break t;case 3:at.flags=at.flags&-65537|128;case 0:if(at=I.payload,R=typeof at=="function"?at.call(Tt,z,R):at,R==null)break t;z=v({},z,R);break t;case 2:gn=!0}}R=y.callback,R!==null&&(t.flags|=64,O&&(t.flags|=8192),O=s.callbacks,O===null?s.callbacks=[R]:O.push(R))}else O={lane:R,tag:y.tag,payload:y.payload,callback:y.callback,next:null},C===null?(_=C=O,S=z):C=C.next=O,f|=R;if(y=y.next,y===null){if(y=s.shared.pending,y===null)break;O=y,y=O.next,O.next=null,s.lastBaseUpdate=O,s.shared.pending=null}}while(!0);C===null&&(S=z),s.baseState=S,s.firstBaseUpdate=_,s.lastBaseUpdate=C,o===null&&(s.shared.lanes=0),_n|=f,t.lanes=f,t.memoizedState=z}}function sh(t,e){if(typeof t!="function")throw Error(r(191,t));t.call(e)}function uh(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)sh(n[t],e)}var ja=U(null),Wl=U(0);function rh(t,e){t=rn,G(Wl,t),G(ja,e),rn=t|e.baseLanes}function ar(){G(Wl,rn),G(ja,ja.current)}function ir(){rn=Wl.current,Q(ja),Q(Wl)}var bn=0,ut=null,St=null,Bt=null,$l=!1,Va=!1,ta=!1,Il=0,Vi=0,za=null,I0=0;function Vt(){throw Error(r(321))}function lr(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!de(t[n],e[n]))return!1;return!0}function sr(t,e,n,i,s,o){return bn=o,ut=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,N.H=t===null||t.memoizedState===null?Qh:Kh,ta=!1,o=n(i,s),ta=!1,Va&&(o=ch(e,n,i,s)),oh(t),o}function oh(t){N.H=ls;var e=St!==null&&St.next!==null;if(bn=0,Bt=St=ut=null,$l=!1,Vi=0,za=null,e)throw Error(r(300));t===null||Qt||(t=t.dependencies,t!==null&&Kl(t)&&(Qt=!0))}function ch(t,e,n,i){ut=t;var s=0;do{if(Va&&(za=null),Vi=0,Va=!1,25<=s)throw Error(r(301));if(s+=1,Bt=St=null,t.updateQueue!=null){var o=t.updateQueue;o.lastEffect=null,o.events=null,o.stores=null,o.memoCache!=null&&(o.memoCache.index=0)}N.H=sv,o=e(n,i)}while(Va);return o}function tv(){var t=N.H,e=t.useState()[0];return e=typeof e.then=="function"?zi(e):e,t=t.useState()[0],(St!==null?St.memoizedState:null)!==t&&(ut.flags|=1024),e}function ur(){var t=Il!==0;return Il=0,t}function rr(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function or(t){if($l){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}$l=!1}bn=0,Bt=St=ut=null,Va=!1,Vi=Il=0,za=null}function se(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Bt===null?ut.memoizedState=Bt=t:Bt=Bt.next=t,Bt}function wt(){if(St===null){var t=ut.alternate;t=t!==null?t.memoizedState:null}else t=St.next;var e=Bt===null?ut.memoizedState:Bt.next;if(e!==null)Bt=e,St=t;else{if(t===null)throw ut.alternate===null?Error(r(467)):Error(r(310));St=t,t={memoizedState:St.memoizedState,baseState:St.baseState,baseQueue:St.baseQueue,queue:St.queue,next:null},Bt===null?ut.memoizedState=Bt=t:Bt=Bt.next=t}return Bt}function cr(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function zi(t){var e=Vi;return Vi+=1,za===null&&(za=[]),t=ah(za,t,e),e=ut,(Bt===null?e.memoizedState:Bt.next)===null&&(e=e.alternate,N.H=e===null||e.memoizedState===null?Qh:Kh),t}function ts(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return zi(t);if(t.$$typeof===H)return It(t)}throw Error(r(438,String(t)))}function fr(t){var e=null,n=ut.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var i=ut.alternate;i!==null&&(i=i.updateQueue,i!==null&&(i=i.memoCache,i!=null&&(e={data:i.data.map(function(s){return s.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=cr(),ut.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),i=0;i<t;i++)n[i]=Ot;return e.index++,n}function en(t,e){return typeof e=="function"?e(t):e}function es(t){var e=wt();return hr(e,St,t)}function hr(t,e,n){var i=t.queue;if(i===null)throw Error(r(311));i.lastRenderedReducer=n;var s=t.baseQueue,o=i.pending;if(o!==null){if(s!==null){var f=s.next;s.next=o.next,o.next=f}e.baseQueue=s=o,i.pending=null}if(o=t.baseState,s===null)t.memoizedState=o;else{e=s.next;var y=f=null,S=null,_=e,C=!1;do{var z=_.lane&-536870913;if(z!==_.lane?(ht&z)===z:(bn&z)===z){var R=_.revertLane;if(R===0)S!==null&&(S=S.next={lane:0,revertLane:0,action:_.action,hasEagerState:_.hasEagerState,eagerState:_.eagerState,next:null}),z===Ca&&(C=!0);else if((bn&R)===R){_=_.next,R===Ca&&(C=!0);continue}else z={lane:0,revertLane:_.revertLane,action:_.action,hasEagerState:_.hasEagerState,eagerState:_.eagerState,next:null},S===null?(y=S=z,f=o):S=S.next=z,ut.lanes|=R,_n|=R;z=_.action,ta&&n(o,z),o=_.hasEagerState?_.eagerState:n(o,z)}else R={lane:z,revertLane:_.revertLane,action:_.action,hasEagerState:_.hasEagerState,eagerState:_.eagerState,next:null},S===null?(y=S=R,f=o):S=S.next=R,ut.lanes|=z,_n|=z;_=_.next}while(_!==null&&_!==e);if(S===null?f=o:S.next=y,!de(o,t.memoizedState)&&(Qt=!0,C&&(n=Na,n!==null)))throw n;t.memoizedState=o,t.baseState=f,t.baseQueue=S,i.lastRenderedState=o}return s===null&&(i.lanes=0),[t.memoizedState,i.dispatch]}function dr(t){var e=wt(),n=e.queue;if(n===null)throw Error(r(311));n.lastRenderedReducer=t;var i=n.dispatch,s=n.pending,o=e.memoizedState;if(s!==null){n.pending=null;var f=s=s.next;do o=t(o,f.action),f=f.next;while(f!==s);de(o,e.memoizedState)||(Qt=!0),e.memoizedState=o,e.baseQueue===null&&(e.baseState=o),n.lastRenderedState=o}return[o,i]}function fh(t,e,n){var i=ut,s=wt(),o=pt;if(o){if(n===void 0)throw Error(r(407));n=n()}else n=e();var f=!de((St||s).memoizedState,n);f&&(s.memoizedState=n,Qt=!0),s=s.queue;var y=mh.bind(null,i,s,t);if(Ui(2048,8,y,[t]),s.getSnapshot!==e||f||Bt!==null&&Bt.memoizedState.tag&1){if(i.flags|=2048,Ua(9,ns(),dh.bind(null,i,s,n,e),null),At===null)throw Error(r(349));o||(bn&124)!==0||hh(i,e,n)}return n}function hh(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=ut.updateQueue,e===null?(e=cr(),ut.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function dh(t,e,n,i){e.value=n,e.getSnapshot=i,ph(e)&&yh(t)}function mh(t,e,n){return n(function(){ph(e)&&yh(t)})}function ph(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!de(t,n)}catch{return!0}}function yh(t){var e=Da(t,2);e!==null&&Se(e,t,2)}function mr(t){var e=se();if(typeof t=="function"){var n=t;if(t=n(),ta){dn(!0);try{n()}finally{dn(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:en,lastRenderedState:t},e}function gh(t,e,n,i){return t.baseState=n,hr(t,St,typeof i=="function"?i:en)}function ev(t,e,n,i,s){if(is(t))throw Error(r(485));if(t=e.action,t!==null){var o={payload:s,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){o.listeners.push(f)}};N.T!==null?n(!0):o.isTransition=!1,i(o),n=e.pending,n===null?(o.next=e.pending=o,vh(e,o)):(o.next=n.next,e.pending=n.next=o)}}function vh(t,e){var n=e.action,i=e.payload,s=t.state;if(e.isTransition){var o=N.T,f={};N.T=f;try{var y=n(s,i),S=N.S;S!==null&&S(f,y),Sh(t,e,y)}catch(_){pr(t,e,_)}finally{N.T=o}}else try{o=n(s,i),Sh(t,e,o)}catch(_){pr(t,e,_)}}function Sh(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(i){bh(t,e,i)},function(i){return pr(t,e,i)}):bh(t,e,n)}function bh(t,e,n){e.status="fulfilled",e.value=n,Th(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,vh(t,n)))}function pr(t,e,n){var i=t.pending;if(t.pending=null,i!==null){i=i.next;do e.status="rejected",e.reason=n,Th(e),e=e.next;while(e!==i)}t.action=null}function Th(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function xh(t,e){return e}function Ah(t,e){if(pt){var n=At.formState;if(n!==null){t:{var i=ut;if(pt){if(Ct){e:{for(var s=Ct,o=Le;s.nodeType!==8;){if(!o){s=null;break e}if(s=ze(s.nextSibling),s===null){s=null;break e}}o=s.data,s=o==="F!"||o==="F"?s:null}if(s){Ct=ze(s.nextSibling),i=s.data==="F!";break t}}Fn(i)}i=!1}i&&(e=n[0])}}return n=se(),n.memoizedState=n.baseState=e,i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:xh,lastRenderedState:e},n.queue=i,n=Gh.bind(null,ut,i),i.dispatch=n,i=mr(!1),o=br.bind(null,ut,!1,i.queue),i=se(),s={state:e,dispatch:null,action:t,pending:null},i.queue=s,n=ev.bind(null,ut,s,o,n),s.dispatch=n,i.memoizedState=t,[e,n,!1]}function Eh(t){var e=wt();return Mh(e,St,t)}function Mh(t,e,n){if(e=hr(t,e,xh)[0],t=es(en)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var i=zi(e)}catch(f){throw f===Ri?Jl:f}else i=e;e=wt();var s=e.queue,o=s.dispatch;return n!==e.memoizedState&&(ut.flags|=2048,Ua(9,ns(),nv.bind(null,s,n),null)),[i,o,t]}function nv(t,e){t.action=e}function Dh(t){var e=wt(),n=St;if(n!==null)return Mh(e,n,t);wt(),e=e.memoizedState,n=wt();var i=n.queue.dispatch;return n.memoizedState=t,[e,i,!1]}function Ua(t,e,n,i){return t={tag:t,create:n,deps:i,inst:e,next:null},e=ut.updateQueue,e===null&&(e=cr(),ut.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(i=n.next,n.next=t,t.next=i,e.lastEffect=t),t}function ns(){return{destroy:void 0,resource:void 0}}function _h(){return wt().memoizedState}function as(t,e,n,i){var s=se();i=i===void 0?null:i,ut.flags|=t,s.memoizedState=Ua(1|e,ns(),n,i)}function Ui(t,e,n,i){var s=wt();i=i===void 0?null:i;var o=s.memoizedState.inst;St!==null&&i!==null&&lr(i,St.memoizedState.deps)?s.memoizedState=Ua(e,o,n,i):(ut.flags|=t,s.memoizedState=Ua(1|e,o,n,i))}function Rh(t,e){as(8390656,8,t,e)}function Oh(t,e){Ui(2048,8,t,e)}function Ch(t,e){return Ui(4,2,t,e)}function Nh(t,e){return Ui(4,4,t,e)}function jh(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Vh(t,e,n){n=n!=null?n.concat([t]):null,Ui(4,4,jh.bind(null,e,t),n)}function yr(){}function zh(t,e){var n=wt();e=e===void 0?null:e;var i=n.memoizedState;return e!==null&&lr(e,i[1])?i[0]:(n.memoizedState=[t,e],t)}function Uh(t,e){var n=wt();e=e===void 0?null:e;var i=n.memoizedState;if(e!==null&&lr(e,i[1]))return i[0];if(i=t(),ta){dn(!0);try{t()}finally{dn(!1)}}return n.memoizedState=[i,e],i}function gr(t,e,n){return n===void 0||(bn&1073741824)!==0?t.memoizedState=e:(t.memoizedState=n,t=Ld(),ut.lanes|=t,_n|=t,n)}function Bh(t,e,n,i){return de(n,e)?n:ja.current!==null?(t=gr(t,n,i),de(t,e)||(Qt=!0),t):(bn&42)===0?(Qt=!0,t.memoizedState=n):(t=Ld(),ut.lanes|=t,_n|=t,e)}function wh(t,e,n,i,s){var o=w.p;w.p=o!==0&&8>o?o:8;var f=N.T,y={};N.T=y,br(t,!1,e,n);try{var S=s(),_=N.S;if(_!==null&&_(y,S),S!==null&&typeof S=="object"&&typeof S.then=="function"){var C=$0(S,i);Bi(t,e,C,ve(t))}else Bi(t,e,i,ve(t))}catch(z){Bi(t,e,{then:function(){},status:"rejected",reason:z},ve())}finally{w.p=o,N.T=f}}function av(){}function vr(t,e,n,i){if(t.tag!==5)throw Error(r(476));var s=Lh(t).queue;wh(t,s,e,P,n===null?av:function(){return Hh(t),n(i)})}function Lh(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:P,baseState:P,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:en,lastRenderedState:P},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:en,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Hh(t){var e=Lh(t).next.queue;Bi(t,e,{},ve())}function Sr(){return It(tl)}function qh(){return wt().memoizedState}function Yh(){return wt().memoizedState}function iv(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=ve();t=vn(n);var i=Sn(e,t,n);i!==null&&(Se(i,e,n),Ci(i,e,n)),e={cache:Ju()},t.payload=e;return}e=e.return}}function lv(t,e,n){var i=ve();n={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},is(t)?Xh(e,n):(n=Hu(t,e,n,i),n!==null&&(Se(n,t,i),Zh(n,e,i)))}function Gh(t,e,n){var i=ve();Bi(t,e,n,i)}function Bi(t,e,n,i){var s={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(is(t))Xh(e,s);else{var o=t.alternate;if(t.lanes===0&&(o===null||o.lanes===0)&&(o=e.lastRenderedReducer,o!==null))try{var f=e.lastRenderedState,y=o(f,n);if(s.hasEagerState=!0,s.eagerState=y,de(y,f))return Yl(t,e,s,0),At===null&&ql(),!1}catch{}finally{}if(n=Hu(t,e,s,i),n!==null)return Se(n,t,i),Zh(n,e,i),!0}return!1}function br(t,e,n,i){if(i={lane:2,revertLane:$r(),action:i,hasEagerState:!1,eagerState:null,next:null},is(t)){if(e)throw Error(r(479))}else e=Hu(t,n,i,2),e!==null&&Se(e,t,2)}function is(t){var e=t.alternate;return t===ut||e!==null&&e===ut}function Xh(t,e){Va=$l=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function Zh(t,e,n){if((n&4194048)!==0){var i=e.lanes;i&=t.pendingLanes,n|=i,e.lanes=n,Wc(t,n)}}var ls={readContext:It,use:ts,useCallback:Vt,useContext:Vt,useEffect:Vt,useImperativeHandle:Vt,useLayoutEffect:Vt,useInsertionEffect:Vt,useMemo:Vt,useReducer:Vt,useRef:Vt,useState:Vt,useDebugValue:Vt,useDeferredValue:Vt,useTransition:Vt,useSyncExternalStore:Vt,useId:Vt,useHostTransitionStatus:Vt,useFormState:Vt,useActionState:Vt,useOptimistic:Vt,useMemoCache:Vt,useCacheRefresh:Vt},Qh={readContext:It,use:ts,useCallback:function(t,e){return se().memoizedState=[t,e===void 0?null:e],t},useContext:It,useEffect:Rh,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,as(4194308,4,jh.bind(null,e,t),n)},useLayoutEffect:function(t,e){return as(4194308,4,t,e)},useInsertionEffect:function(t,e){as(4,2,t,e)},useMemo:function(t,e){var n=se();e=e===void 0?null:e;var i=t();if(ta){dn(!0);try{t()}finally{dn(!1)}}return n.memoizedState=[i,e],i},useReducer:function(t,e,n){var i=se();if(n!==void 0){var s=n(e);if(ta){dn(!0);try{n(e)}finally{dn(!1)}}}else s=e;return i.memoizedState=i.baseState=s,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:s},i.queue=t,t=t.dispatch=lv.bind(null,ut,t),[i.memoizedState,t]},useRef:function(t){var e=se();return t={current:t},e.memoizedState=t},useState:function(t){t=mr(t);var e=t.queue,n=Gh.bind(null,ut,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:yr,useDeferredValue:function(t,e){var n=se();return gr(n,t,e)},useTransition:function(){var t=mr(!1);return t=wh.bind(null,ut,t.queue,!0,!1),se().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var i=ut,s=se();if(pt){if(n===void 0)throw Error(r(407));n=n()}else{if(n=e(),At===null)throw Error(r(349));(ht&124)!==0||hh(i,e,n)}s.memoizedState=n;var o={value:n,getSnapshot:e};return s.queue=o,Rh(mh.bind(null,i,o,t),[t]),i.flags|=2048,Ua(9,ns(),dh.bind(null,i,o,n,e),null),n},useId:function(){var t=se(),e=At.identifierPrefix;if(pt){var n=$e,i=We;n=(i&~(1<<32-he(i)-1)).toString(32)+n,e="«"+e+"R"+n,n=Il++,0<n&&(e+="H"+n.toString(32)),e+="»"}else n=I0++,e="«"+e+"r"+n.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:Sr,useFormState:Ah,useActionState:Ah,useOptimistic:function(t){var e=se();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=br.bind(null,ut,!0,n),n.dispatch=e,[t,e]},useMemoCache:fr,useCacheRefresh:function(){return se().memoizedState=iv.bind(null,ut)}},Kh={readContext:It,use:ts,useCallback:zh,useContext:It,useEffect:Oh,useImperativeHandle:Vh,useInsertionEffect:Ch,useLayoutEffect:Nh,useMemo:Uh,useReducer:es,useRef:_h,useState:function(){return es(en)},useDebugValue:yr,useDeferredValue:function(t,e){var n=wt();return Bh(n,St.memoizedState,t,e)},useTransition:function(){var t=es(en)[0],e=wt().memoizedState;return[typeof t=="boolean"?t:zi(t),e]},useSyncExternalStore:fh,useId:qh,useHostTransitionStatus:Sr,useFormState:Eh,useActionState:Eh,useOptimistic:function(t,e){var n=wt();return gh(n,St,t,e)},useMemoCache:fr,useCacheRefresh:Yh},sv={readContext:It,use:ts,useCallback:zh,useContext:It,useEffect:Oh,useImperativeHandle:Vh,useInsertionEffect:Ch,useLayoutEffect:Nh,useMemo:Uh,useReducer:dr,useRef:_h,useState:function(){return dr(en)},useDebugValue:yr,useDeferredValue:function(t,e){var n=wt();return St===null?gr(n,t,e):Bh(n,St.memoizedState,t,e)},useTransition:function(){var t=dr(en)[0],e=wt().memoizedState;return[typeof t=="boolean"?t:zi(t),e]},useSyncExternalStore:fh,useId:qh,useHostTransitionStatus:Sr,useFormState:Dh,useActionState:Dh,useOptimistic:function(t,e){var n=wt();return St!==null?gh(n,St,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:fr,useCacheRefresh:Yh},Ba=null,wi=0;function ss(t){var e=wi;return wi+=1,Ba===null&&(Ba=[]),ah(Ba,t,e)}function Li(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function us(t,e){throw e.$$typeof===b?Error(r(525)):(t=Object.prototype.toString.call(e),Error(r(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function kh(t){var e=t._init;return e(t._payload)}function Ph(t){function e(M,A){if(t){var D=M.deletions;D===null?(M.deletions=[A],M.flags|=16):D.push(A)}}function n(M,A){if(!t)return null;for(;A!==null;)e(M,A),A=A.sibling;return null}function i(M){for(var A=new Map;M!==null;)M.key!==null?A.set(M.key,M):A.set(M.index,M),M=M.sibling;return A}function s(M,A){return M=Fe(M,A),M.index=0,M.sibling=null,M}function o(M,A,D){return M.index=D,t?(D=M.alternate,D!==null?(D=D.index,D<A?(M.flags|=67108866,A):D):(M.flags|=67108866,A)):(M.flags|=1048576,A)}function f(M){return t&&M.alternate===null&&(M.flags|=67108866),M}function y(M,A,D,V){return A===null||A.tag!==6?(A=Yu(D,M.mode,V),A.return=M,A):(A=s(A,D),A.return=M,A)}function S(M,A,D,V){var k=D.type;return k===B?C(M,A,D.props.children,V,D.key):A!==null&&(A.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===J&&kh(k)===A.type)?(A=s(A,D.props),Li(A,D),A.return=M,A):(A=Xl(D.type,D.key,D.props,null,M.mode,V),Li(A,D),A.return=M,A)}function _(M,A,D,V){return A===null||A.tag!==4||A.stateNode.containerInfo!==D.containerInfo||A.stateNode.implementation!==D.implementation?(A=Gu(D,M.mode,V),A.return=M,A):(A=s(A,D.children||[]),A.return=M,A)}function C(M,A,D,V,k){return A===null||A.tag!==7?(A=Kn(D,M.mode,V,k),A.return=M,A):(A=s(A,D),A.return=M,A)}function z(M,A,D){if(typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint")return A=Yu(""+A,M.mode,D),A.return=M,A;if(typeof A=="object"&&A!==null){switch(A.$$typeof){case E:return D=Xl(A.type,A.key,A.props,null,M.mode,D),Li(D,A),D.return=M,D;case j:return A=Gu(A,M.mode,D),A.return=M,A;case J:var V=A._init;return A=V(A._payload),z(M,A,D)}if(qt(A)||Ht(A))return A=Kn(A,M.mode,D,null),A.return=M,A;if(typeof A.then=="function")return z(M,ss(A),D);if(A.$$typeof===H)return z(M,kl(M,A),D);us(M,A)}return null}function R(M,A,D,V){var k=A!==null?A.key:null;if(typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint")return k!==null?null:y(M,A,""+D,V);if(typeof D=="object"&&D!==null){switch(D.$$typeof){case E:return D.key===k?S(M,A,D,V):null;case j:return D.key===k?_(M,A,D,V):null;case J:return k=D._init,D=k(D._payload),R(M,A,D,V)}if(qt(D)||Ht(D))return k!==null?null:C(M,A,D,V,null);if(typeof D.then=="function")return R(M,A,ss(D),V);if(D.$$typeof===H)return R(M,A,kl(M,D),V);us(M,D)}return null}function O(M,A,D,V,k){if(typeof V=="string"&&V!==""||typeof V=="number"||typeof V=="bigint")return M=M.get(D)||null,y(A,M,""+V,k);if(typeof V=="object"&&V!==null){switch(V.$$typeof){case E:return M=M.get(V.key===null?D:V.key)||null,S(A,M,V,k);case j:return M=M.get(V.key===null?D:V.key)||null,_(A,M,V,k);case J:var rt=V._init;return V=rt(V._payload),O(M,A,D,V,k)}if(qt(V)||Ht(V))return M=M.get(D)||null,C(A,M,V,k,null);if(typeof V.then=="function")return O(M,A,D,ss(V),k);if(V.$$typeof===H)return O(M,A,D,kl(A,V),k);us(A,V)}return null}function at(M,A,D,V){for(var k=null,rt=null,F=A,tt=A=0,kt=null;F!==null&&tt<D.length;tt++){F.index>tt?(kt=F,F=null):kt=F.sibling;var mt=R(M,F,D[tt],V);if(mt===null){F===null&&(F=kt);break}t&&F&&mt.alternate===null&&e(M,F),A=o(mt,A,tt),rt===null?k=mt:rt.sibling=mt,rt=mt,F=kt}if(tt===D.length)return n(M,F),pt&&Pn(M,tt),k;if(F===null){for(;tt<D.length;tt++)F=z(M,D[tt],V),F!==null&&(A=o(F,A,tt),rt===null?k=F:rt.sibling=F,rt=F);return pt&&Pn(M,tt),k}for(F=i(F);tt<D.length;tt++)kt=O(F,M,tt,D[tt],V),kt!==null&&(t&&kt.alternate!==null&&F.delete(kt.key===null?tt:kt.key),A=o(kt,A,tt),rt===null?k=kt:rt.sibling=kt,rt=kt);return t&&F.forEach(function(Bn){return e(M,Bn)}),pt&&Pn(M,tt),k}function I(M,A,D,V){if(D==null)throw Error(r(151));for(var k=null,rt=null,F=A,tt=A=0,kt=null,mt=D.next();F!==null&&!mt.done;tt++,mt=D.next()){F.index>tt?(kt=F,F=null):kt=F.sibling;var Bn=R(M,F,mt.value,V);if(Bn===null){F===null&&(F=kt);break}t&&F&&Bn.alternate===null&&e(M,F),A=o(Bn,A,tt),rt===null?k=Bn:rt.sibling=Bn,rt=Bn,F=kt}if(mt.done)return n(M,F),pt&&Pn(M,tt),k;if(F===null){for(;!mt.done;tt++,mt=D.next())mt=z(M,mt.value,V),mt!==null&&(A=o(mt,A,tt),rt===null?k=mt:rt.sibling=mt,rt=mt);return pt&&Pn(M,tt),k}for(F=i(F);!mt.done;tt++,mt=D.next())mt=O(F,M,tt,mt.value,V),mt!==null&&(t&&mt.alternate!==null&&F.delete(mt.key===null?tt:mt.key),A=o(mt,A,tt),rt===null?k=mt:rt.sibling=mt,rt=mt);return t&&F.forEach(function(u1){return e(M,u1)}),pt&&Pn(M,tt),k}function Tt(M,A,D,V){if(typeof D=="object"&&D!==null&&D.type===B&&D.key===null&&(D=D.props.children),typeof D=="object"&&D!==null){switch(D.$$typeof){case E:t:{for(var k=D.key;A!==null;){if(A.key===k){if(k=D.type,k===B){if(A.tag===7){n(M,A.sibling),V=s(A,D.props.children),V.return=M,M=V;break t}}else if(A.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===J&&kh(k)===A.type){n(M,A.sibling),V=s(A,D.props),Li(V,D),V.return=M,M=V;break t}n(M,A);break}else e(M,A);A=A.sibling}D.type===B?(V=Kn(D.props.children,M.mode,V,D.key),V.return=M,M=V):(V=Xl(D.type,D.key,D.props,null,M.mode,V),Li(V,D),V.return=M,M=V)}return f(M);case j:t:{for(k=D.key;A!==null;){if(A.key===k)if(A.tag===4&&A.stateNode.containerInfo===D.containerInfo&&A.stateNode.implementation===D.implementation){n(M,A.sibling),V=s(A,D.children||[]),V.return=M,M=V;break t}else{n(M,A);break}else e(M,A);A=A.sibling}V=Gu(D,M.mode,V),V.return=M,M=V}return f(M);case J:return k=D._init,D=k(D._payload),Tt(M,A,D,V)}if(qt(D))return at(M,A,D,V);if(Ht(D)){if(k=Ht(D),typeof k!="function")throw Error(r(150));return D=k.call(D),I(M,A,D,V)}if(typeof D.then=="function")return Tt(M,A,ss(D),V);if(D.$$typeof===H)return Tt(M,A,kl(M,D),V);us(M,D)}return typeof D=="string"&&D!==""||typeof D=="number"||typeof D=="bigint"?(D=""+D,A!==null&&A.tag===6?(n(M,A.sibling),V=s(A,D),V.return=M,M=V):(n(M,A),V=Yu(D,M.mode,V),V.return=M,M=V),f(M)):n(M,A)}return function(M,A,D,V){try{wi=0;var k=Tt(M,A,D,V);return Ba=null,k}catch(F){if(F===Ri||F===Jl)throw F;var rt=me(29,F,null,M.mode);return rt.lanes=V,rt.return=M,rt}finally{}}}var wa=Ph(!0),Jh=Ph(!1),De=U(null),He=null;function Tn(t){var e=t.alternate;G(Gt,Gt.current&1),G(De,t),He===null&&(e===null||ja.current!==null||e.memoizedState!==null)&&(He=t)}function Fh(t){if(t.tag===22){if(G(Gt,Gt.current),G(De,t),He===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(He=t)}}else xn()}function xn(){G(Gt,Gt.current),G(De,De.current)}function nn(t){Q(De),He===t&&(He=null),Q(Gt)}var Gt=U(0);function rs(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||co(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Tr(t,e,n,i){e=t.memoizedState,n=n(i,e),n=n==null?e:v({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var xr={enqueueSetState:function(t,e,n){t=t._reactInternals;var i=ve(),s=vn(i);s.payload=e,n!=null&&(s.callback=n),e=Sn(t,s,i),e!==null&&(Se(e,t,i),Ci(e,t,i))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var i=ve(),s=vn(i);s.tag=1,s.payload=e,n!=null&&(s.callback=n),e=Sn(t,s,i),e!==null&&(Se(e,t,i),Ci(e,t,i))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=ve(),i=vn(n);i.tag=2,e!=null&&(i.callback=e),e=Sn(t,i,n),e!==null&&(Se(e,t,n),Ci(e,t,n))}};function Wh(t,e,n,i,s,o,f){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(i,o,f):e.prototype&&e.prototype.isPureReactComponent?!bi(n,i)||!bi(s,o):!0}function $h(t,e,n,i){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,i),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,i),e.state!==t&&xr.enqueueReplaceState(e,e.state,null)}function ea(t,e){var n=e;if("ref"in e){n={};for(var i in e)i!=="ref"&&(n[i]=e[i])}if(t=t.defaultProps){n===e&&(n=v({},n));for(var s in t)n[s]===void 0&&(n[s]=t[s])}return n}var os=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Ih(t){os(t)}function td(t){console.error(t)}function ed(t){os(t)}function cs(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(i){setTimeout(function(){throw i})}}function nd(t,e,n){try{var i=t.onCaughtError;i(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(s){setTimeout(function(){throw s})}}function Ar(t,e,n){return n=vn(n),n.tag=3,n.payload={element:null},n.callback=function(){cs(t,e)},n}function ad(t){return t=vn(t),t.tag=3,t}function id(t,e,n,i){var s=n.type.getDerivedStateFromError;if(typeof s=="function"){var o=i.value;t.payload=function(){return s(o)},t.callback=function(){nd(e,n,i)}}var f=n.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(t.callback=function(){nd(e,n,i),typeof s!="function"&&(Rn===null?Rn=new Set([this]):Rn.add(this));var y=i.stack;this.componentDidCatch(i.value,{componentStack:y!==null?y:""})})}function uv(t,e,n,i,s){if(n.flags|=32768,i!==null&&typeof i=="object"&&typeof i.then=="function"){if(e=n.alternate,e!==null&&Mi(e,n,s,!0),n=De.current,n!==null){switch(n.tag){case 13:return He===null?kr():n.alternate===null&&Nt===0&&(Nt=3),n.flags&=-257,n.flags|=65536,n.lanes=s,i===$u?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([i]):e.add(i),Jr(t,i,s)),!1;case 22:return n.flags|=65536,i===$u?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([i])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([i]):n.add(i)),Jr(t,i,s)),!1}throw Error(r(435,n.tag))}return Jr(t,i,s),kr(),!1}if(pt)return e=De.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=s,i!==Qu&&(t=Error(r(422),{cause:i}),Ei(xe(t,n)))):(i!==Qu&&(e=Error(r(423),{cause:i}),Ei(xe(e,n))),t=t.current.alternate,t.flags|=65536,s&=-s,t.lanes|=s,i=xe(i,n),s=Ar(t.stateNode,i,s),er(t,s),Nt!==4&&(Nt=2)),!1;var o=Error(r(520),{cause:i});if(o=xe(o,n),Qi===null?Qi=[o]:Qi.push(o),Nt!==4&&(Nt=2),e===null)return!0;i=xe(i,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=s&-s,n.lanes|=t,t=Ar(n.stateNode,i,t),er(n,t),!1;case 1:if(e=n.type,o=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||o!==null&&typeof o.componentDidCatch=="function"&&(Rn===null||!Rn.has(o))))return n.flags|=65536,s&=-s,n.lanes|=s,s=ad(s),id(s,t,n,i),er(n,s),!1}n=n.return}while(n!==null);return!1}var ld=Error(r(461)),Qt=!1;function Pt(t,e,n,i){e.child=t===null?Jh(e,null,n,i):wa(e,t.child,n,i)}function sd(t,e,n,i,s){n=n.render;var o=e.ref;if("ref"in i){var f={};for(var y in i)y!=="ref"&&(f[y]=i[y])}else f=i;return $n(e),i=sr(t,e,n,f,o,s),y=ur(),t!==null&&!Qt?(rr(t,e,s),an(t,e,s)):(pt&&y&&Xu(e),e.flags|=1,Pt(t,e,i,s),e.child)}function ud(t,e,n,i,s){if(t===null){var o=n.type;return typeof o=="function"&&!qu(o)&&o.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=o,rd(t,e,o,i,s)):(t=Xl(n.type,null,i,e,e.mode,s),t.ref=e.ref,t.return=e,e.child=t)}if(o=t.child,!Nr(t,s)){var f=o.memoizedProps;if(n=n.compare,n=n!==null?n:bi,n(f,i)&&t.ref===e.ref)return an(t,e,s)}return e.flags|=1,t=Fe(o,i),t.ref=e.ref,t.return=e,e.child=t}function rd(t,e,n,i,s){if(t!==null){var o=t.memoizedProps;if(bi(o,i)&&t.ref===e.ref)if(Qt=!1,e.pendingProps=i=o,Nr(t,s))(t.flags&131072)!==0&&(Qt=!0);else return e.lanes=t.lanes,an(t,e,s)}return Er(t,e,n,i,s)}function od(t,e,n){var i=e.pendingProps,s=i.children,o=t!==null?t.memoizedState:null;if(i.mode==="hidden"){if((e.flags&128)!==0){if(i=o!==null?o.baseLanes|n:n,t!==null){for(s=e.child=t.child,o=0;s!==null;)o=o|s.lanes|s.childLanes,s=s.sibling;e.childLanes=o&~i}else e.childLanes=0,e.child=null;return cd(t,e,i,n)}if((n&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Pl(e,o!==null?o.cachePool:null),o!==null?rh(e,o):ar(),Fh(e);else return e.lanes=e.childLanes=536870912,cd(t,e,o!==null?o.baseLanes|n:n,n)}else o!==null?(Pl(e,o.cachePool),rh(e,o),xn(),e.memoizedState=null):(t!==null&&Pl(e,null),ar(),xn());return Pt(t,e,s,n),e.child}function cd(t,e,n,i){var s=Wu();return s=s===null?null:{parent:Yt._currentValue,pool:s},e.memoizedState={baseLanes:n,cachePool:s},t!==null&&Pl(e,null),ar(),Fh(e),t!==null&&Mi(t,e,i,!0),null}function fs(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(r(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function Er(t,e,n,i,s){return $n(e),n=sr(t,e,n,i,void 0,s),i=ur(),t!==null&&!Qt?(rr(t,e,s),an(t,e,s)):(pt&&i&&Xu(e),e.flags|=1,Pt(t,e,n,s),e.child)}function fd(t,e,n,i,s,o){return $n(e),e.updateQueue=null,n=ch(e,i,n,s),oh(t),i=ur(),t!==null&&!Qt?(rr(t,e,o),an(t,e,o)):(pt&&i&&Xu(e),e.flags|=1,Pt(t,e,n,o),e.child)}function hd(t,e,n,i,s){if($n(e),e.stateNode===null){var o=_a,f=n.contextType;typeof f=="object"&&f!==null&&(o=It(f)),o=new n(i,o),e.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,o.updater=xr,e.stateNode=o,o._reactInternals=e,o=e.stateNode,o.props=i,o.state=e.memoizedState,o.refs={},Iu(e),f=n.contextType,o.context=typeof f=="object"&&f!==null?It(f):_a,o.state=e.memoizedState,f=n.getDerivedStateFromProps,typeof f=="function"&&(Tr(e,n,f,i),o.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(f=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),f!==o.state&&xr.enqueueReplaceState(o,o.state,null),ji(e,i,o,s),Ni(),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308),i=!0}else if(t===null){o=e.stateNode;var y=e.memoizedProps,S=ea(n,y);o.props=S;var _=o.context,C=n.contextType;f=_a,typeof C=="object"&&C!==null&&(f=It(C));var z=n.getDerivedStateFromProps;C=typeof z=="function"||typeof o.getSnapshotBeforeUpdate=="function",y=e.pendingProps!==y,C||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(y||_!==f)&&$h(e,o,i,f),gn=!1;var R=e.memoizedState;o.state=R,ji(e,i,o,s),Ni(),_=e.memoizedState,y||R!==_||gn?(typeof z=="function"&&(Tr(e,n,z,i),_=e.memoizedState),(S=gn||Wh(e,n,S,i,R,_,f))?(C||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(e.flags|=4194308)):(typeof o.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=i,e.memoizedState=_),o.props=i,o.state=_,o.context=f,i=S):(typeof o.componentDidMount=="function"&&(e.flags|=4194308),i=!1)}else{o=e.stateNode,tr(t,e),f=e.memoizedProps,C=ea(n,f),o.props=C,z=e.pendingProps,R=o.context,_=n.contextType,S=_a,typeof _=="object"&&_!==null&&(S=It(_)),y=n.getDerivedStateFromProps,(_=typeof y=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(f!==z||R!==S)&&$h(e,o,i,S),gn=!1,R=e.memoizedState,o.state=R,ji(e,i,o,s),Ni();var O=e.memoizedState;f!==z||R!==O||gn||t!==null&&t.dependencies!==null&&Kl(t.dependencies)?(typeof y=="function"&&(Tr(e,n,y,i),O=e.memoizedState),(C=gn||Wh(e,n,C,i,R,O,S)||t!==null&&t.dependencies!==null&&Kl(t.dependencies))?(_||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(i,O,S),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(i,O,S)),typeof o.componentDidUpdate=="function"&&(e.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof o.componentDidUpdate!="function"||f===t.memoizedProps&&R===t.memoizedState||(e.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&R===t.memoizedState||(e.flags|=1024),e.memoizedProps=i,e.memoizedState=O),o.props=i,o.state=O,o.context=S,i=C):(typeof o.componentDidUpdate!="function"||f===t.memoizedProps&&R===t.memoizedState||(e.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&R===t.memoizedState||(e.flags|=1024),i=!1)}return o=i,fs(t,e),i=(e.flags&128)!==0,o||i?(o=e.stateNode,n=i&&typeof n.getDerivedStateFromError!="function"?null:o.render(),e.flags|=1,t!==null&&i?(e.child=wa(e,t.child,null,s),e.child=wa(e,null,n,s)):Pt(t,e,n,s),e.memoizedState=o.state,t=e.child):t=an(t,e,s),t}function dd(t,e,n,i){return Ai(),e.flags|=256,Pt(t,e,n,i),e.child}var Mr={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Dr(t){return{baseLanes:t,cachePool:th()}}function _r(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=_e),t}function md(t,e,n){var i=e.pendingProps,s=!1,o=(e.flags&128)!==0,f;if((f=o)||(f=t!==null&&t.memoizedState===null?!1:(Gt.current&2)!==0),f&&(s=!0,e.flags&=-129),f=(e.flags&32)!==0,e.flags&=-33,t===null){if(pt){if(s?Tn(e):xn(),pt){var y=Ct,S;if(S=y){t:{for(S=y,y=Le;S.nodeType!==8;){if(!y){y=null;break t}if(S=ze(S.nextSibling),S===null){y=null;break t}}y=S}y!==null?(e.memoizedState={dehydrated:y,treeContext:kn!==null?{id:We,overflow:$e}:null,retryLane:536870912,hydrationErrors:null},S=me(18,null,null,0),S.stateNode=y,S.return=e,e.child=S,ne=e,Ct=null,S=!0):S=!1}S||Fn(e)}if(y=e.memoizedState,y!==null&&(y=y.dehydrated,y!==null))return co(y)?e.lanes=32:e.lanes=536870912,null;nn(e)}return y=i.children,i=i.fallback,s?(xn(),s=e.mode,y=hs({mode:"hidden",children:y},s),i=Kn(i,s,n,null),y.return=e,i.return=e,y.sibling=i,e.child=y,s=e.child,s.memoizedState=Dr(n),s.childLanes=_r(t,f,n),e.memoizedState=Mr,i):(Tn(e),Rr(e,y))}if(S=t.memoizedState,S!==null&&(y=S.dehydrated,y!==null)){if(o)e.flags&256?(Tn(e),e.flags&=-257,e=Or(t,e,n)):e.memoizedState!==null?(xn(),e.child=t.child,e.flags|=128,e=null):(xn(),s=i.fallback,y=e.mode,i=hs({mode:"visible",children:i.children},y),s=Kn(s,y,n,null),s.flags|=2,i.return=e,s.return=e,i.sibling=s,e.child=i,wa(e,t.child,null,n),i=e.child,i.memoizedState=Dr(n),i.childLanes=_r(t,f,n),e.memoizedState=Mr,e=s);else if(Tn(e),co(y)){if(f=y.nextSibling&&y.nextSibling.dataset,f)var _=f.dgst;f=_,i=Error(r(419)),i.stack="",i.digest=f,Ei({value:i,source:null,stack:null}),e=Or(t,e,n)}else if(Qt||Mi(t,e,n,!1),f=(n&t.childLanes)!==0,Qt||f){if(f=At,f!==null&&(i=n&-n,i=(i&42)!==0?1:fu(i),i=(i&(f.suspendedLanes|n))!==0?0:i,i!==0&&i!==S.retryLane))throw S.retryLane=i,Da(t,i),Se(f,t,i),ld;y.data==="$?"||kr(),e=Or(t,e,n)}else y.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=S.treeContext,Ct=ze(y.nextSibling),ne=e,pt=!0,Jn=null,Le=!1,t!==null&&(Ee[Me++]=We,Ee[Me++]=$e,Ee[Me++]=kn,We=t.id,$e=t.overflow,kn=e),e=Rr(e,i.children),e.flags|=4096);return e}return s?(xn(),s=i.fallback,y=e.mode,S=t.child,_=S.sibling,i=Fe(S,{mode:"hidden",children:i.children}),i.subtreeFlags=S.subtreeFlags&65011712,_!==null?s=Fe(_,s):(s=Kn(s,y,n,null),s.flags|=2),s.return=e,i.return=e,i.sibling=s,e.child=i,i=s,s=e.child,y=t.child.memoizedState,y===null?y=Dr(n):(S=y.cachePool,S!==null?(_=Yt._currentValue,S=S.parent!==_?{parent:_,pool:_}:S):S=th(),y={baseLanes:y.baseLanes|n,cachePool:S}),s.memoizedState=y,s.childLanes=_r(t,f,n),e.memoizedState=Mr,i):(Tn(e),n=t.child,t=n.sibling,n=Fe(n,{mode:"visible",children:i.children}),n.return=e,n.sibling=null,t!==null&&(f=e.deletions,f===null?(e.deletions=[t],e.flags|=16):f.push(t)),e.child=n,e.memoizedState=null,n)}function Rr(t,e){return e=hs({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function hs(t,e){return t=me(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Or(t,e,n){return wa(e,t.child,null,n),t=Rr(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function pd(t,e,n){t.lanes|=e;var i=t.alternate;i!==null&&(i.lanes|=e),ku(t.return,e,n)}function Cr(t,e,n,i,s){var o=t.memoizedState;o===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:i,tail:n,tailMode:s}:(o.isBackwards=e,o.rendering=null,o.renderingStartTime=0,o.last=i,o.tail=n,o.tailMode=s)}function yd(t,e,n){var i=e.pendingProps,s=i.revealOrder,o=i.tail;if(Pt(t,e,i.children,n),i=Gt.current,(i&2)!==0)i=i&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&pd(t,n,e);else if(t.tag===19)pd(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}i&=1}switch(G(Gt,i),s){case"forwards":for(n=e.child,s=null;n!==null;)t=n.alternate,t!==null&&rs(t)===null&&(s=n),n=n.sibling;n=s,n===null?(s=e.child,e.child=null):(s=n.sibling,n.sibling=null),Cr(e,!1,s,n,o);break;case"backwards":for(n=null,s=e.child,e.child=null;s!==null;){if(t=s.alternate,t!==null&&rs(t)===null){e.child=s;break}t=s.sibling,s.sibling=n,n=s,s=t}Cr(e,!0,n,null,o);break;case"together":Cr(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function an(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),_n|=e.lanes,(n&e.childLanes)===0)if(t!==null){if(Mi(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(r(153));if(e.child!==null){for(t=e.child,n=Fe(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=Fe(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function Nr(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Kl(t)))}function rv(t,e,n){switch(e.tag){case 3:Et(e,e.stateNode.containerInfo),yn(e,Yt,t.memoizedState.cache),Ai();break;case 27:case 5:su(e);break;case 4:Et(e,e.stateNode.containerInfo);break;case 10:yn(e,e.type,e.memoizedProps.value);break;case 13:var i=e.memoizedState;if(i!==null)return i.dehydrated!==null?(Tn(e),e.flags|=128,null):(n&e.child.childLanes)!==0?md(t,e,n):(Tn(e),t=an(t,e,n),t!==null?t.sibling:null);Tn(e);break;case 19:var s=(t.flags&128)!==0;if(i=(n&e.childLanes)!==0,i||(Mi(t,e,n,!1),i=(n&e.childLanes)!==0),s){if(i)return yd(t,e,n);e.flags|=128}if(s=e.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),G(Gt,Gt.current),i)break;return null;case 22:case 23:return e.lanes=0,od(t,e,n);case 24:yn(e,Yt,t.memoizedState.cache)}return an(t,e,n)}function gd(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)Qt=!0;else{if(!Nr(t,n)&&(e.flags&128)===0)return Qt=!1,rv(t,e,n);Qt=(t.flags&131072)!==0}else Qt=!1,pt&&(e.flags&1048576)!==0&&kf(e,Ql,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var i=e.elementType,s=i._init;if(i=s(i._payload),e.type=i,typeof i=="function")qu(i)?(t=ea(i,t),e.tag=1,e=hd(null,e,i,t,n)):(e.tag=0,e=Er(null,e,i,t,n));else{if(i!=null){if(s=i.$$typeof,s===et){e.tag=11,e=sd(null,e,i,t,n);break t}else if(s===nt){e.tag=14,e=ud(null,e,i,t,n);break t}}throw e=Be(i)||i,Error(r(306,e,""))}}return e;case 0:return Er(t,e,e.type,e.pendingProps,n);case 1:return i=e.type,s=ea(i,e.pendingProps),hd(t,e,i,s,n);case 3:t:{if(Et(e,e.stateNode.containerInfo),t===null)throw Error(r(387));i=e.pendingProps;var o=e.memoizedState;s=o.element,tr(t,e),ji(e,i,null,n);var f=e.memoizedState;if(i=f.cache,yn(e,Yt,i),i!==o.cache&&Pu(e,[Yt],n,!0),Ni(),i=f.element,o.isDehydrated)if(o={element:i,isDehydrated:!1,cache:f.cache},e.updateQueue.baseState=o,e.memoizedState=o,e.flags&256){e=dd(t,e,i,n);break t}else if(i!==s){s=xe(Error(r(424)),e),Ei(s),e=dd(t,e,i,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Ct=ze(t.firstChild),ne=e,pt=!0,Jn=null,Le=!0,n=Jh(e,null,i,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Ai(),i===s){e=an(t,e,n);break t}Pt(t,e,i,n)}e=e.child}return e;case 26:return fs(t,e),t===null?(n=Tm(e.type,null,e.pendingProps,null))?e.memoizedState=n:pt||(n=e.type,t=e.pendingProps,i=Ds(lt.current).createElement(n),i[$t]=e,i[ie]=t,Ft(i,n,t),Zt(i),e.stateNode=i):e.memoizedState=Tm(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return su(e),t===null&&pt&&(i=e.stateNode=vm(e.type,e.pendingProps,lt.current),ne=e,Le=!0,s=Ct,Nn(e.type)?(fo=s,Ct=ze(i.firstChild)):Ct=s),Pt(t,e,e.pendingProps.children,n),fs(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&pt&&((s=i=Ct)&&(i=Bv(i,e.type,e.pendingProps,Le),i!==null?(e.stateNode=i,ne=e,Ct=ze(i.firstChild),Le=!1,s=!0):s=!1),s||Fn(e)),su(e),s=e.type,o=e.pendingProps,f=t!==null?t.memoizedProps:null,i=o.children,uo(s,o)?i=null:f!==null&&uo(s,f)&&(e.flags|=32),e.memoizedState!==null&&(s=sr(t,e,tv,null,null,n),tl._currentValue=s),fs(t,e),Pt(t,e,i,n),e.child;case 6:return t===null&&pt&&((t=n=Ct)&&(n=wv(n,e.pendingProps,Le),n!==null?(e.stateNode=n,ne=e,Ct=null,t=!0):t=!1),t||Fn(e)),null;case 13:return md(t,e,n);case 4:return Et(e,e.stateNode.containerInfo),i=e.pendingProps,t===null?e.child=wa(e,null,i,n):Pt(t,e,i,n),e.child;case 11:return sd(t,e,e.type,e.pendingProps,n);case 7:return Pt(t,e,e.pendingProps,n),e.child;case 8:return Pt(t,e,e.pendingProps.children,n),e.child;case 12:return Pt(t,e,e.pendingProps.children,n),e.child;case 10:return i=e.pendingProps,yn(e,e.type,i.value),Pt(t,e,i.children,n),e.child;case 9:return s=e.type._context,i=e.pendingProps.children,$n(e),s=It(s),i=i(s),e.flags|=1,Pt(t,e,i,n),e.child;case 14:return ud(t,e,e.type,e.pendingProps,n);case 15:return rd(t,e,e.type,e.pendingProps,n);case 19:return yd(t,e,n);case 31:return i=e.pendingProps,n=e.mode,i={mode:i.mode,children:i.children},t===null?(n=hs(i,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=Fe(t.child,i),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return od(t,e,n);case 24:return $n(e),i=It(Yt),t===null?(s=Wu(),s===null&&(s=At,o=Ju(),s.pooledCache=o,o.refCount++,o!==null&&(s.pooledCacheLanes|=n),s=o),e.memoizedState={parent:i,cache:s},Iu(e),yn(e,Yt,s)):((t.lanes&n)!==0&&(tr(t,e),ji(e,null,null,n),Ni()),s=t.memoizedState,o=e.memoizedState,s.parent!==i?(s={parent:i,cache:i},e.memoizedState=s,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=s),yn(e,Yt,i)):(i=o.cache,yn(e,Yt,i),i!==s.cache&&Pu(e,[Yt],n,!0))),Pt(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(r(156,e.tag))}function ln(t){t.flags|=4}function vd(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Dm(e)){if(e=De.current,e!==null&&((ht&4194048)===ht?He!==null:(ht&62914560)!==ht&&(ht&536870912)===0||e!==He))throw Oi=$u,eh;t.flags|=8192}}function ds(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Jc():536870912,t.lanes|=e,Ya|=e)}function Hi(t,e){if(!pt)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var i=null;n!==null;)n.alternate!==null&&(i=n),n=n.sibling;i===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:i.sibling=null}}function _t(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,i=0;if(e)for(var s=t.child;s!==null;)n|=s.lanes|s.childLanes,i|=s.subtreeFlags&65011712,i|=s.flags&65011712,s.return=t,s=s.sibling;else for(s=t.child;s!==null;)n|=s.lanes|s.childLanes,i|=s.subtreeFlags,i|=s.flags,s.return=t,s=s.sibling;return t.subtreeFlags|=i,t.childLanes=n,e}function ov(t,e,n){var i=e.pendingProps;switch(Zu(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return _t(e),null;case 1:return _t(e),null;case 3:return n=e.stateNode,i=null,t!==null&&(i=t.memoizedState.cache),e.memoizedState.cache!==i&&(e.flags|=2048),tn(Yt),hn(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(xi(e)?ln(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Ff())),_t(e),null;case 26:return n=e.memoizedState,t===null?(ln(e),n!==null?(_t(e),vd(e,n)):(_t(e),e.flags&=-16777217)):n?n!==t.memoizedState?(ln(e),_t(e),vd(e,n)):(_t(e),e.flags&=-16777217):(t.memoizedProps!==i&&ln(e),_t(e),e.flags&=-16777217),null;case 27:El(e),n=lt.current;var s=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==i&&ln(e);else{if(!i){if(e.stateNode===null)throw Error(r(166));return _t(e),null}t=$.current,xi(e)?Pf(e):(t=vm(s,i,n),e.stateNode=t,ln(e))}return _t(e),null;case 5:if(El(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==i&&ln(e);else{if(!i){if(e.stateNode===null)throw Error(r(166));return _t(e),null}if(t=$.current,xi(e))Pf(e);else{switch(s=Ds(lt.current),t){case 1:t=s.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=s.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=s.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=s.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=s.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof i.is=="string"?s.createElement("select",{is:i.is}):s.createElement("select"),i.multiple?t.multiple=!0:i.size&&(t.size=i.size);break;default:t=typeof i.is=="string"?s.createElement(n,{is:i.is}):s.createElement(n)}}t[$t]=e,t[ie]=i;t:for(s=e.child;s!==null;){if(s.tag===5||s.tag===6)t.appendChild(s.stateNode);else if(s.tag!==4&&s.tag!==27&&s.child!==null){s.child.return=s,s=s.child;continue}if(s===e)break t;for(;s.sibling===null;){if(s.return===null||s.return===e)break t;s=s.return}s.sibling.return=s.return,s=s.sibling}e.stateNode=t;t:switch(Ft(t,n,i),n){case"button":case"input":case"select":case"textarea":t=!!i.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&ln(e)}}return _t(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==i&&ln(e);else{if(typeof i!="string"&&e.stateNode===null)throw Error(r(166));if(t=lt.current,xi(e)){if(t=e.stateNode,n=e.memoizedProps,i=null,s=ne,s!==null)switch(s.tag){case 27:case 5:i=s.memoizedProps}t[$t]=e,t=!!(t.nodeValue===n||i!==null&&i.suppressHydrationWarning===!0||fm(t.nodeValue,n)),t||Fn(e)}else t=Ds(t).createTextNode(i),t[$t]=e,e.stateNode=t}return _t(e),null;case 13:if(i=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(s=xi(e),i!==null&&i.dehydrated!==null){if(t===null){if(!s)throw Error(r(318));if(s=e.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(r(317));s[$t]=e}else Ai(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;_t(e),s=!1}else s=Ff(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=s),s=!0;if(!s)return e.flags&256?(nn(e),e):(nn(e),null)}if(nn(e),(e.flags&128)!==0)return e.lanes=n,e;if(n=i!==null,t=t!==null&&t.memoizedState!==null,n){i=e.child,s=null,i.alternate!==null&&i.alternate.memoizedState!==null&&i.alternate.memoizedState.cachePool!==null&&(s=i.alternate.memoizedState.cachePool.pool);var o=null;i.memoizedState!==null&&i.memoizedState.cachePool!==null&&(o=i.memoizedState.cachePool.pool),o!==s&&(i.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),ds(e,e.updateQueue),_t(e),null;case 4:return hn(),t===null&&no(e.stateNode.containerInfo),_t(e),null;case 10:return tn(e.type),_t(e),null;case 19:if(Q(Gt),s=e.memoizedState,s===null)return _t(e),null;if(i=(e.flags&128)!==0,o=s.rendering,o===null)if(i)Hi(s,!1);else{if(Nt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(o=rs(t),o!==null){for(e.flags|=128,Hi(s,!1),t=o.updateQueue,e.updateQueue=t,ds(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)Kf(n,t),n=n.sibling;return G(Gt,Gt.current&1|2),e.child}t=t.sibling}s.tail!==null&&we()>ys&&(e.flags|=128,i=!0,Hi(s,!1),e.lanes=4194304)}else{if(!i)if(t=rs(o),t!==null){if(e.flags|=128,i=!0,t=t.updateQueue,e.updateQueue=t,ds(e,t),Hi(s,!0),s.tail===null&&s.tailMode==="hidden"&&!o.alternate&&!pt)return _t(e),null}else 2*we()-s.renderingStartTime>ys&&n!==536870912&&(e.flags|=128,i=!0,Hi(s,!1),e.lanes=4194304);s.isBackwards?(o.sibling=e.child,e.child=o):(t=s.last,t!==null?t.sibling=o:e.child=o,s.last=o)}return s.tail!==null?(e=s.tail,s.rendering=e,s.tail=e.sibling,s.renderingStartTime=we(),e.sibling=null,t=Gt.current,G(Gt,i?t&1|2:t&1),e):(_t(e),null);case 22:case 23:return nn(e),ir(),i=e.memoizedState!==null,t!==null?t.memoizedState!==null!==i&&(e.flags|=8192):i&&(e.flags|=8192),i?(n&536870912)!==0&&(e.flags&128)===0&&(_t(e),e.subtreeFlags&6&&(e.flags|=8192)):_t(e),n=e.updateQueue,n!==null&&ds(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),i=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(i=e.memoizedState.cachePool.pool),i!==n&&(e.flags|=2048),t!==null&&Q(In),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),tn(Yt),_t(e),null;case 25:return null;case 30:return null}throw Error(r(156,e.tag))}function cv(t,e){switch(Zu(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return tn(Yt),hn(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return El(e),null;case 13:if(nn(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(r(340));Ai()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Q(Gt),null;case 4:return hn(),null;case 10:return tn(e.type),null;case 22:case 23:return nn(e),ir(),t!==null&&Q(In),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return tn(Yt),null;case 25:return null;default:return null}}function Sd(t,e){switch(Zu(e),e.tag){case 3:tn(Yt),hn();break;case 26:case 27:case 5:El(e);break;case 4:hn();break;case 13:nn(e);break;case 19:Q(Gt);break;case 10:tn(e.type);break;case 22:case 23:nn(e),ir(),t!==null&&Q(In);break;case 24:tn(Yt)}}function qi(t,e){try{var n=e.updateQueue,i=n!==null?n.lastEffect:null;if(i!==null){var s=i.next;n=s;do{if((n.tag&t)===t){i=void 0;var o=n.create,f=n.inst;i=o(),f.destroy=i}n=n.next}while(n!==s)}}catch(y){xt(e,e.return,y)}}function An(t,e,n){try{var i=e.updateQueue,s=i!==null?i.lastEffect:null;if(s!==null){var o=s.next;i=o;do{if((i.tag&t)===t){var f=i.inst,y=f.destroy;if(y!==void 0){f.destroy=void 0,s=e;var S=n,_=y;try{_()}catch(C){xt(s,S,C)}}}i=i.next}while(i!==o)}}catch(C){xt(e,e.return,C)}}function bd(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{uh(e,n)}catch(i){xt(t,t.return,i)}}}function Td(t,e,n){n.props=ea(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(i){xt(t,e,i)}}function Yi(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var i=t.stateNode;break;case 30:i=t.stateNode;break;default:i=t.stateNode}typeof n=="function"?t.refCleanup=n(i):n.current=i}}catch(s){xt(t,e,s)}}function qe(t,e){var n=t.ref,i=t.refCleanup;if(n!==null)if(typeof i=="function")try{i()}catch(s){xt(t,e,s)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(s){xt(t,e,s)}else n.current=null}function xd(t){var e=t.type,n=t.memoizedProps,i=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&i.focus();break t;case"img":n.src?i.src=n.src:n.srcSet&&(i.srcset=n.srcSet)}}catch(s){xt(t,t.return,s)}}function jr(t,e,n){try{var i=t.stateNode;Nv(i,t.type,n,e),i[ie]=e}catch(s){xt(t,t.return,s)}}function Ad(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Nn(t.type)||t.tag===4}function Vr(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Ad(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Nn(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function zr(t,e,n){var i=t.tag;if(i===5||i===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=Ms));else if(i!==4&&(i===27&&Nn(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(zr(t,e,n),t=t.sibling;t!==null;)zr(t,e,n),t=t.sibling}function ms(t,e,n){var i=t.tag;if(i===5||i===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(i!==4&&(i===27&&Nn(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(ms(t,e,n),t=t.sibling;t!==null;)ms(t,e,n),t=t.sibling}function Ed(t){var e=t.stateNode,n=t.memoizedProps;try{for(var i=t.type,s=e.attributes;s.length;)e.removeAttributeNode(s[0]);Ft(e,i,n),e[$t]=t,e[ie]=n}catch(o){xt(t,t.return,o)}}var sn=!1,zt=!1,Ur=!1,Md=typeof WeakSet=="function"?WeakSet:Set,Kt=null;function fv(t,e){if(t=t.containerInfo,lo=js,t=Bf(t),Vu(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var i=n.getSelection&&n.getSelection();if(i&&i.rangeCount!==0){n=i.anchorNode;var s=i.anchorOffset,o=i.focusNode;i=i.focusOffset;try{n.nodeType,o.nodeType}catch{n=null;break t}var f=0,y=-1,S=-1,_=0,C=0,z=t,R=null;e:for(;;){for(var O;z!==n||s!==0&&z.nodeType!==3||(y=f+s),z!==o||i!==0&&z.nodeType!==3||(S=f+i),z.nodeType===3&&(f+=z.nodeValue.length),(O=z.firstChild)!==null;)R=z,z=O;for(;;){if(z===t)break e;if(R===n&&++_===s&&(y=f),R===o&&++C===i&&(S=f),(O=z.nextSibling)!==null)break;z=R,R=z.parentNode}z=O}n=y===-1||S===-1?null:{start:y,end:S}}else n=null}n=n||{start:0,end:0}}else n=null;for(so={focusedElem:t,selectionRange:n},js=!1,Kt=e;Kt!==null;)if(e=Kt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Kt=t;else for(;Kt!==null;){switch(e=Kt,o=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&o!==null){t=void 0,n=e,s=o.memoizedProps,o=o.memoizedState,i=n.stateNode;try{var at=ea(n.type,s,n.elementType===n.type);t=i.getSnapshotBeforeUpdate(at,o),i.__reactInternalSnapshotBeforeUpdate=t}catch(I){xt(n,n.return,I)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)oo(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":oo(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(r(163))}if(t=e.sibling,t!==null){t.return=e.return,Kt=t;break}Kt=e.return}}function Dd(t,e,n){var i=n.flags;switch(n.tag){case 0:case 11:case 15:En(t,n),i&4&&qi(5,n);break;case 1:if(En(t,n),i&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(f){xt(n,n.return,f)}else{var s=ea(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(s,e,t.__reactInternalSnapshotBeforeUpdate)}catch(f){xt(n,n.return,f)}}i&64&&bd(n),i&512&&Yi(n,n.return);break;case 3:if(En(t,n),i&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{uh(t,e)}catch(f){xt(n,n.return,f)}}break;case 27:e===null&&i&4&&Ed(n);case 26:case 5:En(t,n),e===null&&i&4&&xd(n),i&512&&Yi(n,n.return);break;case 12:En(t,n);break;case 13:En(t,n),i&4&&Od(t,n),i&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=bv.bind(null,n),Lv(t,n))));break;case 22:if(i=n.memoizedState!==null||sn,!i){e=e!==null&&e.memoizedState!==null||zt,s=sn;var o=zt;sn=i,(zt=e)&&!o?Mn(t,n,(n.subtreeFlags&8772)!==0):En(t,n),sn=s,zt=o}break;case 30:break;default:En(t,n)}}function _d(t){var e=t.alternate;e!==null&&(t.alternate=null,_d(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&mu(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var Mt=null,ue=!1;function un(t,e,n){for(n=n.child;n!==null;)Rd(t,e,n),n=n.sibling}function Rd(t,e,n){if(fe&&typeof fe.onCommitFiberUnmount=="function")try{fe.onCommitFiberUnmount(ri,n)}catch{}switch(n.tag){case 26:zt||qe(n,e),un(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:zt||qe(n,e);var i=Mt,s=ue;Nn(n.type)&&(Mt=n.stateNode,ue=!1),un(t,e,n),Fi(n.stateNode),Mt=i,ue=s;break;case 5:zt||qe(n,e);case 6:if(i=Mt,s=ue,Mt=null,un(t,e,n),Mt=i,ue=s,Mt!==null)if(ue)try{(Mt.nodeType===9?Mt.body:Mt.nodeName==="HTML"?Mt.ownerDocument.body:Mt).removeChild(n.stateNode)}catch(o){xt(n,e,o)}else try{Mt.removeChild(n.stateNode)}catch(o){xt(n,e,o)}break;case 18:Mt!==null&&(ue?(t=Mt,ym(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),il(t)):ym(Mt,n.stateNode));break;case 4:i=Mt,s=ue,Mt=n.stateNode.containerInfo,ue=!0,un(t,e,n),Mt=i,ue=s;break;case 0:case 11:case 14:case 15:zt||An(2,n,e),zt||An(4,n,e),un(t,e,n);break;case 1:zt||(qe(n,e),i=n.stateNode,typeof i.componentWillUnmount=="function"&&Td(n,e,i)),un(t,e,n);break;case 21:un(t,e,n);break;case 22:zt=(i=zt)||n.memoizedState!==null,un(t,e,n),zt=i;break;default:un(t,e,n)}}function Od(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{il(t)}catch(n){xt(e,e.return,n)}}function hv(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Md),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Md),e;default:throw Error(r(435,t.tag))}}function Br(t,e){var n=hv(t);e.forEach(function(i){var s=Tv.bind(null,t,i);n.has(i)||(n.add(i),i.then(s,s))})}function pe(t,e){var n=e.deletions;if(n!==null)for(var i=0;i<n.length;i++){var s=n[i],o=t,f=e,y=f;t:for(;y!==null;){switch(y.tag){case 27:if(Nn(y.type)){Mt=y.stateNode,ue=!1;break t}break;case 5:Mt=y.stateNode,ue=!1;break t;case 3:case 4:Mt=y.stateNode.containerInfo,ue=!0;break t}y=y.return}if(Mt===null)throw Error(r(160));Rd(o,f,s),Mt=null,ue=!1,o=s.alternate,o!==null&&(o.return=null),s.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Cd(e,t),e=e.sibling}var Ve=null;function Cd(t,e){var n=t.alternate,i=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:pe(e,t),ye(t),i&4&&(An(3,t,t.return),qi(3,t),An(5,t,t.return));break;case 1:pe(e,t),ye(t),i&512&&(zt||n===null||qe(n,n.return)),i&64&&sn&&(t=t.updateQueue,t!==null&&(i=t.callbacks,i!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?i:n.concat(i))));break;case 26:var s=Ve;if(pe(e,t),ye(t),i&512&&(zt||n===null||qe(n,n.return)),i&4){var o=n!==null?n.memoizedState:null;if(i=t.memoizedState,n===null)if(i===null)if(t.stateNode===null){t:{i=t.type,n=t.memoizedProps,s=s.ownerDocument||s;e:switch(i){case"title":o=s.getElementsByTagName("title")[0],(!o||o[fi]||o[$t]||o.namespaceURI==="http://www.w3.org/2000/svg"||o.hasAttribute("itemprop"))&&(o=s.createElement(i),s.head.insertBefore(o,s.querySelector("head > title"))),Ft(o,i,n),o[$t]=t,Zt(o),i=o;break t;case"link":var f=Em("link","href",s).get(i+(n.href||""));if(f){for(var y=0;y<f.length;y++)if(o=f[y],o.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&o.getAttribute("rel")===(n.rel==null?null:n.rel)&&o.getAttribute("title")===(n.title==null?null:n.title)&&o.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){f.splice(y,1);break e}}o=s.createElement(i),Ft(o,i,n),s.head.appendChild(o);break;case"meta":if(f=Em("meta","content",s).get(i+(n.content||""))){for(y=0;y<f.length;y++)if(o=f[y],o.getAttribute("content")===(n.content==null?null:""+n.content)&&o.getAttribute("name")===(n.name==null?null:n.name)&&o.getAttribute("property")===(n.property==null?null:n.property)&&o.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&o.getAttribute("charset")===(n.charSet==null?null:n.charSet)){f.splice(y,1);break e}}o=s.createElement(i),Ft(o,i,n),s.head.appendChild(o);break;default:throw Error(r(468,i))}o[$t]=t,Zt(o),i=o}t.stateNode=i}else Mm(s,t.type,t.stateNode);else t.stateNode=Am(s,i,t.memoizedProps);else o!==i?(o===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):o.count--,i===null?Mm(s,t.type,t.stateNode):Am(s,i,t.memoizedProps)):i===null&&t.stateNode!==null&&jr(t,t.memoizedProps,n.memoizedProps)}break;case 27:pe(e,t),ye(t),i&512&&(zt||n===null||qe(n,n.return)),n!==null&&i&4&&jr(t,t.memoizedProps,n.memoizedProps);break;case 5:if(pe(e,t),ye(t),i&512&&(zt||n===null||qe(n,n.return)),t.flags&32){s=t.stateNode;try{Sa(s,"")}catch(O){xt(t,t.return,O)}}i&4&&t.stateNode!=null&&(s=t.memoizedProps,jr(t,s,n!==null?n.memoizedProps:s)),i&1024&&(Ur=!0);break;case 6:if(pe(e,t),ye(t),i&4){if(t.stateNode===null)throw Error(r(162));i=t.memoizedProps,n=t.stateNode;try{n.nodeValue=i}catch(O){xt(t,t.return,O)}}break;case 3:if(Os=null,s=Ve,Ve=_s(e.containerInfo),pe(e,t),Ve=s,ye(t),i&4&&n!==null&&n.memoizedState.isDehydrated)try{il(e.containerInfo)}catch(O){xt(t,t.return,O)}Ur&&(Ur=!1,Nd(t));break;case 4:i=Ve,Ve=_s(t.stateNode.containerInfo),pe(e,t),ye(t),Ve=i;break;case 12:pe(e,t),ye(t);break;case 13:pe(e,t),ye(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Gr=we()),i&4&&(i=t.updateQueue,i!==null&&(t.updateQueue=null,Br(t,i)));break;case 22:s=t.memoizedState!==null;var S=n!==null&&n.memoizedState!==null,_=sn,C=zt;if(sn=_||s,zt=C||S,pe(e,t),zt=C,sn=_,ye(t),i&8192)t:for(e=t.stateNode,e._visibility=s?e._visibility&-2:e._visibility|1,s&&(n===null||S||sn||zt||na(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){S=n=e;try{if(o=S.stateNode,s)f=o.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{y=S.stateNode;var z=S.memoizedProps.style,R=z!=null&&z.hasOwnProperty("display")?z.display:null;y.style.display=R==null||typeof R=="boolean"?"":(""+R).trim()}}catch(O){xt(S,S.return,O)}}}else if(e.tag===6){if(n===null){S=e;try{S.stateNode.nodeValue=s?"":S.memoizedProps}catch(O){xt(S,S.return,O)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}i&4&&(i=t.updateQueue,i!==null&&(n=i.retryQueue,n!==null&&(i.retryQueue=null,Br(t,n))));break;case 19:pe(e,t),ye(t),i&4&&(i=t.updateQueue,i!==null&&(t.updateQueue=null,Br(t,i)));break;case 30:break;case 21:break;default:pe(e,t),ye(t)}}function ye(t){var e=t.flags;if(e&2){try{for(var n,i=t.return;i!==null;){if(Ad(i)){n=i;break}i=i.return}if(n==null)throw Error(r(160));switch(n.tag){case 27:var s=n.stateNode,o=Vr(t);ms(t,o,s);break;case 5:var f=n.stateNode;n.flags&32&&(Sa(f,""),n.flags&=-33);var y=Vr(t);ms(t,y,f);break;case 3:case 4:var S=n.stateNode.containerInfo,_=Vr(t);zr(t,_,S);break;default:throw Error(r(161))}}catch(C){xt(t,t.return,C)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Nd(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Nd(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function En(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Dd(t,e.alternate,e),e=e.sibling}function na(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:An(4,e,e.return),na(e);break;case 1:qe(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&Td(e,e.return,n),na(e);break;case 27:Fi(e.stateNode);case 26:case 5:qe(e,e.return),na(e);break;case 22:e.memoizedState===null&&na(e);break;case 30:na(e);break;default:na(e)}t=t.sibling}}function Mn(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var i=e.alternate,s=t,o=e,f=o.flags;switch(o.tag){case 0:case 11:case 15:Mn(s,o,n),qi(4,o);break;case 1:if(Mn(s,o,n),i=o,s=i.stateNode,typeof s.componentDidMount=="function")try{s.componentDidMount()}catch(_){xt(i,i.return,_)}if(i=o,s=i.updateQueue,s!==null){var y=i.stateNode;try{var S=s.shared.hiddenCallbacks;if(S!==null)for(s.shared.hiddenCallbacks=null,s=0;s<S.length;s++)sh(S[s],y)}catch(_){xt(i,i.return,_)}}n&&f&64&&bd(o),Yi(o,o.return);break;case 27:Ed(o);case 26:case 5:Mn(s,o,n),n&&i===null&&f&4&&xd(o),Yi(o,o.return);break;case 12:Mn(s,o,n);break;case 13:Mn(s,o,n),n&&f&4&&Od(s,o);break;case 22:o.memoizedState===null&&Mn(s,o,n),Yi(o,o.return);break;case 30:break;default:Mn(s,o,n)}e=e.sibling}}function wr(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&Di(n))}function Lr(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Di(t))}function Ye(t,e,n,i){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)jd(t,e,n,i),e=e.sibling}function jd(t,e,n,i){var s=e.flags;switch(e.tag){case 0:case 11:case 15:Ye(t,e,n,i),s&2048&&qi(9,e);break;case 1:Ye(t,e,n,i);break;case 3:Ye(t,e,n,i),s&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Di(t)));break;case 12:if(s&2048){Ye(t,e,n,i),t=e.stateNode;try{var o=e.memoizedProps,f=o.id,y=o.onPostCommit;typeof y=="function"&&y(f,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(S){xt(e,e.return,S)}}else Ye(t,e,n,i);break;case 13:Ye(t,e,n,i);break;case 23:break;case 22:o=e.stateNode,f=e.alternate,e.memoizedState!==null?o._visibility&2?Ye(t,e,n,i):Gi(t,e):o._visibility&2?Ye(t,e,n,i):(o._visibility|=2,La(t,e,n,i,(e.subtreeFlags&10256)!==0)),s&2048&&wr(f,e);break;case 24:Ye(t,e,n,i),s&2048&&Lr(e.alternate,e);break;default:Ye(t,e,n,i)}}function La(t,e,n,i,s){for(s=s&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var o=t,f=e,y=n,S=i,_=f.flags;switch(f.tag){case 0:case 11:case 15:La(o,f,y,S,s),qi(8,f);break;case 23:break;case 22:var C=f.stateNode;f.memoizedState!==null?C._visibility&2?La(o,f,y,S,s):Gi(o,f):(C._visibility|=2,La(o,f,y,S,s)),s&&_&2048&&wr(f.alternate,f);break;case 24:La(o,f,y,S,s),s&&_&2048&&Lr(f.alternate,f);break;default:La(o,f,y,S,s)}e=e.sibling}}function Gi(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,i=e,s=i.flags;switch(i.tag){case 22:Gi(n,i),s&2048&&wr(i.alternate,i);break;case 24:Gi(n,i),s&2048&&Lr(i.alternate,i);break;default:Gi(n,i)}e=e.sibling}}var Xi=8192;function Ha(t){if(t.subtreeFlags&Xi)for(t=t.child;t!==null;)Vd(t),t=t.sibling}function Vd(t){switch(t.tag){case 26:Ha(t),t.flags&Xi&&t.memoizedState!==null&&Wv(Ve,t.memoizedState,t.memoizedProps);break;case 5:Ha(t);break;case 3:case 4:var e=Ve;Ve=_s(t.stateNode.containerInfo),Ha(t),Ve=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Xi,Xi=16777216,Ha(t),Xi=e):Ha(t));break;default:Ha(t)}}function zd(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Zi(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var i=e[n];Kt=i,Bd(i,t)}zd(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Ud(t),t=t.sibling}function Ud(t){switch(t.tag){case 0:case 11:case 15:Zi(t),t.flags&2048&&An(9,t,t.return);break;case 3:Zi(t);break;case 12:Zi(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,ps(t)):Zi(t);break;default:Zi(t)}}function ps(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var i=e[n];Kt=i,Bd(i,t)}zd(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:An(8,e,e.return),ps(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,ps(e));break;default:ps(e)}t=t.sibling}}function Bd(t,e){for(;Kt!==null;){var n=Kt;switch(n.tag){case 0:case 11:case 15:An(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var i=n.memoizedState.cachePool.pool;i!=null&&i.refCount++}break;case 24:Di(n.memoizedState.cache)}if(i=n.child,i!==null)i.return=n,Kt=i;else t:for(n=t;Kt!==null;){i=Kt;var s=i.sibling,o=i.return;if(_d(i),i===n){Kt=null;break t}if(s!==null){s.return=o,Kt=s;break t}Kt=o}}}var dv={getCacheForType:function(t){var e=It(Yt),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},mv=typeof WeakMap=="function"?WeakMap:Map,gt=0,At=null,ot=null,ht=0,vt=0,ge=null,Dn=!1,qa=!1,Hr=!1,rn=0,Nt=0,_n=0,aa=0,qr=0,_e=0,Ya=0,Qi=null,re=null,Yr=!1,Gr=0,ys=1/0,gs=null,Rn=null,Jt=0,On=null,Ga=null,Xa=0,Xr=0,Zr=null,wd=null,Ki=0,Qr=null;function ve(){if((gt&2)!==0&&ht!==0)return ht&-ht;if(N.T!==null){var t=Ca;return t!==0?t:$r()}return $c()}function Ld(){_e===0&&(_e=(ht&536870912)===0||pt?Pc():536870912);var t=De.current;return t!==null&&(t.flags|=32),_e}function Se(t,e,n){(t===At&&(vt===2||vt===9)||t.cancelPendingCommit!==null)&&(Za(t,0),Cn(t,ht,_e,!1)),ci(t,n),((gt&2)===0||t!==At)&&(t===At&&((gt&2)===0&&(aa|=n),Nt===4&&Cn(t,ht,_e,!1)),Ge(t))}function Hd(t,e,n){if((gt&6)!==0)throw Error(r(327));var i=!n&&(e&124)===0&&(e&t.expiredLanes)===0||oi(t,e),s=i?gv(t,e):Pr(t,e,!0),o=i;do{if(s===0){qa&&!i&&Cn(t,e,0,!1);break}else{if(n=t.current.alternate,o&&!pv(n)){s=Pr(t,e,!1),o=!1;continue}if(s===2){if(o=e,t.errorRecoveryDisabledLanes&o)var f=0;else f=t.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){e=f;t:{var y=t;s=Qi;var S=y.current.memoizedState.isDehydrated;if(S&&(Za(y,f).flags|=256),f=Pr(y,f,!1),f!==2){if(Hr&&!S){y.errorRecoveryDisabledLanes|=o,aa|=o,s=4;break t}o=re,re=s,o!==null&&(re===null?re=o:re.push.apply(re,o))}s=f}if(o=!1,s!==2)continue}}if(s===1){Za(t,0),Cn(t,e,0,!0);break}t:{switch(i=t,o=s,o){case 0:case 1:throw Error(r(345));case 4:if((e&4194048)!==e)break;case 6:Cn(i,e,_e,!Dn);break t;case 2:re=null;break;case 3:case 5:break;default:throw Error(r(329))}if((e&62914560)===e&&(s=Gr+300-we(),10<s)){if(Cn(i,e,_e,!Dn),Rl(i,0,!0)!==0)break t;i.timeoutHandle=mm(qd.bind(null,i,n,re,gs,Yr,e,_e,aa,Ya,Dn,o,2,-0,0),s);break t}qd(i,n,re,gs,Yr,e,_e,aa,Ya,Dn,o,0,-0,0)}}break}while(!0);Ge(t)}function qd(t,e,n,i,s,o,f,y,S,_,C,z,R,O){if(t.timeoutHandle=-1,z=e.subtreeFlags,(z&8192||(z&16785408)===16785408)&&(Ii={stylesheets:null,count:0,unsuspend:Fv},Vd(e),z=$v(),z!==null)){t.cancelPendingCommit=z(kd.bind(null,t,e,o,n,i,s,f,y,S,C,1,R,O)),Cn(t,o,f,!_);return}kd(t,e,o,n,i,s,f,y,S)}function pv(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var i=0;i<n.length;i++){var s=n[i],o=s.getSnapshot;s=s.value;try{if(!de(o(),s))return!1}catch{return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Cn(t,e,n,i){e&=~qr,e&=~aa,t.suspendedLanes|=e,t.pingedLanes&=~e,i&&(t.warmLanes|=e),i=t.expirationTimes;for(var s=e;0<s;){var o=31-he(s),f=1<<o;i[o]=-1,s&=~f}n!==0&&Fc(t,n,e)}function vs(){return(gt&6)===0?(ki(0),!1):!0}function Kr(){if(ot!==null){if(vt===0)var t=ot.return;else t=ot,Ie=Wn=null,or(t),Ba=null,wi=0,t=ot;for(;t!==null;)Sd(t.alternate,t),t=t.return;ot=null}}function Za(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,Vv(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),Kr(),At=t,ot=n=Fe(t.current,null),ht=e,vt=0,ge=null,Dn=!1,qa=oi(t,e),Hr=!1,Ya=_e=qr=aa=_n=Nt=0,re=Qi=null,Yr=!1,(e&8)!==0&&(e|=e&32);var i=t.entangledLanes;if(i!==0)for(t=t.entanglements,i&=e;0<i;){var s=31-he(i),o=1<<s;e|=t[s],i&=~o}return rn=e,ql(),n}function Yd(t,e){ut=null,N.H=ls,e===Ri||e===Jl?(e=ih(),vt=3):e===eh?(e=ih(),vt=4):vt=e===ld?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,ge=e,ot===null&&(Nt=1,cs(t,xe(e,t.current)))}function Gd(){var t=N.H;return N.H=ls,t===null?ls:t}function Xd(){var t=N.A;return N.A=dv,t}function kr(){Nt=4,Dn||(ht&4194048)!==ht&&De.current!==null||(qa=!0),(_n&134217727)===0&&(aa&134217727)===0||At===null||Cn(At,ht,_e,!1)}function Pr(t,e,n){var i=gt;gt|=2;var s=Gd(),o=Xd();(At!==t||ht!==e)&&(gs=null,Za(t,e)),e=!1;var f=Nt;t:do try{if(vt!==0&&ot!==null){var y=ot,S=ge;switch(vt){case 8:Kr(),f=6;break t;case 3:case 2:case 9:case 6:De.current===null&&(e=!0);var _=vt;if(vt=0,ge=null,Qa(t,y,S,_),n&&qa){f=0;break t}break;default:_=vt,vt=0,ge=null,Qa(t,y,S,_)}}yv(),f=Nt;break}catch(C){Yd(t,C)}while(!0);return e&&t.shellSuspendCounter++,Ie=Wn=null,gt=i,N.H=s,N.A=o,ot===null&&(At=null,ht=0,ql()),f}function yv(){for(;ot!==null;)Zd(ot)}function gv(t,e){var n=gt;gt|=2;var i=Gd(),s=Xd();At!==t||ht!==e?(gs=null,ys=we()+500,Za(t,e)):qa=oi(t,e);t:do try{if(vt!==0&&ot!==null){e=ot;var o=ge;e:switch(vt){case 1:vt=0,ge=null,Qa(t,e,o,1);break;case 2:case 9:if(nh(o)){vt=0,ge=null,Qd(e);break}e=function(){vt!==2&&vt!==9||At!==t||(vt=7),Ge(t)},o.then(e,e);break t;case 3:vt=7;break t;case 4:vt=5;break t;case 7:nh(o)?(vt=0,ge=null,Qd(e)):(vt=0,ge=null,Qa(t,e,o,7));break;case 5:var f=null;switch(ot.tag){case 26:f=ot.memoizedState;case 5:case 27:var y=ot;if(!f||Dm(f)){vt=0,ge=null;var S=y.sibling;if(S!==null)ot=S;else{var _=y.return;_!==null?(ot=_,Ss(_)):ot=null}break e}}vt=0,ge=null,Qa(t,e,o,5);break;case 6:vt=0,ge=null,Qa(t,e,o,6);break;case 8:Kr(),Nt=6;break t;default:throw Error(r(462))}}vv();break}catch(C){Yd(t,C)}while(!0);return Ie=Wn=null,N.H=i,N.A=s,gt=n,ot!==null?0:(At=null,ht=0,ql(),Nt)}function vv(){for(;ot!==null&&!qg();)Zd(ot)}function Zd(t){var e=gd(t.alternate,t,rn);t.memoizedProps=t.pendingProps,e===null?Ss(t):ot=e}function Qd(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=fd(n,e,e.pendingProps,e.type,void 0,ht);break;case 11:e=fd(n,e,e.pendingProps,e.type.render,e.ref,ht);break;case 5:or(e);default:Sd(n,e),e=ot=Kf(e,rn),e=gd(n,e,rn)}t.memoizedProps=t.pendingProps,e===null?Ss(t):ot=e}function Qa(t,e,n,i){Ie=Wn=null,or(e),Ba=null,wi=0;var s=e.return;try{if(uv(t,s,e,n,ht)){Nt=1,cs(t,xe(n,t.current)),ot=null;return}}catch(o){if(s!==null)throw ot=s,o;Nt=1,cs(t,xe(n,t.current)),ot=null;return}e.flags&32768?(pt||i===1?t=!0:qa||(ht&536870912)!==0?t=!1:(Dn=t=!0,(i===2||i===9||i===3||i===6)&&(i=De.current,i!==null&&i.tag===13&&(i.flags|=16384))),Kd(e,t)):Ss(e)}function Ss(t){var e=t;do{if((e.flags&32768)!==0){Kd(e,Dn);return}t=e.return;var n=ov(e.alternate,e,rn);if(n!==null){ot=n;return}if(e=e.sibling,e!==null){ot=e;return}ot=e=t}while(e!==null);Nt===0&&(Nt=5)}function Kd(t,e){do{var n=cv(t.alternate,t);if(n!==null){n.flags&=32767,ot=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){ot=t;return}ot=t=n}while(t!==null);Nt=6,ot=null}function kd(t,e,n,i,s,o,f,y,S){t.cancelPendingCommit=null;do bs();while(Jt!==0);if((gt&6)!==0)throw Error(r(327));if(e!==null){if(e===t.current)throw Error(r(177));if(o=e.lanes|e.childLanes,o|=Lu,Fg(t,n,o,f,y,S),t===At&&(ot=At=null,ht=0),Ga=e,On=t,Xa=n,Xr=o,Zr=s,wd=i,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,xv(Ml,function(){return $d(),null})):(t.callbackNode=null,t.callbackPriority=0),i=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||i){i=N.T,N.T=null,s=w.p,w.p=2,f=gt,gt|=4;try{fv(t,e,n)}finally{gt=f,w.p=s,N.T=i}}Jt=1,Pd(),Jd(),Fd()}}function Pd(){if(Jt===1){Jt=0;var t=On,e=Ga,n=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||n){n=N.T,N.T=null;var i=w.p;w.p=2;var s=gt;gt|=4;try{Cd(e,t);var o=so,f=Bf(t.containerInfo),y=o.focusedElem,S=o.selectionRange;if(f!==y&&y&&y.ownerDocument&&Uf(y.ownerDocument.documentElement,y)){if(S!==null&&Vu(y)){var _=S.start,C=S.end;if(C===void 0&&(C=_),"selectionStart"in y)y.selectionStart=_,y.selectionEnd=Math.min(C,y.value.length);else{var z=y.ownerDocument||document,R=z&&z.defaultView||window;if(R.getSelection){var O=R.getSelection(),at=y.textContent.length,I=Math.min(S.start,at),Tt=S.end===void 0?I:Math.min(S.end,at);!O.extend&&I>Tt&&(f=Tt,Tt=I,I=f);var M=zf(y,I),A=zf(y,Tt);if(M&&A&&(O.rangeCount!==1||O.anchorNode!==M.node||O.anchorOffset!==M.offset||O.focusNode!==A.node||O.focusOffset!==A.offset)){var D=z.createRange();D.setStart(M.node,M.offset),O.removeAllRanges(),I>Tt?(O.addRange(D),O.extend(A.node,A.offset)):(D.setEnd(A.node,A.offset),O.addRange(D))}}}}for(z=[],O=y;O=O.parentNode;)O.nodeType===1&&z.push({element:O,left:O.scrollLeft,top:O.scrollTop});for(typeof y.focus=="function"&&y.focus(),y=0;y<z.length;y++){var V=z[y];V.element.scrollLeft=V.left,V.element.scrollTop=V.top}}js=!!lo,so=lo=null}finally{gt=s,w.p=i,N.T=n}}t.current=e,Jt=2}}function Jd(){if(Jt===2){Jt=0;var t=On,e=Ga,n=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||n){n=N.T,N.T=null;var i=w.p;w.p=2;var s=gt;gt|=4;try{Dd(t,e.alternate,e)}finally{gt=s,w.p=i,N.T=n}}Jt=3}}function Fd(){if(Jt===4||Jt===3){Jt=0,Yg();var t=On,e=Ga,n=Xa,i=wd;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Jt=5:(Jt=0,Ga=On=null,Wd(t,t.pendingLanes));var s=t.pendingLanes;if(s===0&&(Rn=null),hu(n),e=e.stateNode,fe&&typeof fe.onCommitFiberRoot=="function")try{fe.onCommitFiberRoot(ri,e,void 0,(e.current.flags&128)===128)}catch{}if(i!==null){e=N.T,s=w.p,w.p=2,N.T=null;try{for(var o=t.onRecoverableError,f=0;f<i.length;f++){var y=i[f];o(y.value,{componentStack:y.stack})}}finally{N.T=e,w.p=s}}(Xa&3)!==0&&bs(),Ge(t),s=t.pendingLanes,(n&4194090)!==0&&(s&42)!==0?t===Qr?Ki++:(Ki=0,Qr=t):Ki=0,ki(0)}}function Wd(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Di(e)))}function bs(t){return Pd(),Jd(),Fd(),$d()}function $d(){if(Jt!==5)return!1;var t=On,e=Xr;Xr=0;var n=hu(Xa),i=N.T,s=w.p;try{w.p=32>n?32:n,N.T=null,n=Zr,Zr=null;var o=On,f=Xa;if(Jt=0,Ga=On=null,Xa=0,(gt&6)!==0)throw Error(r(331));var y=gt;if(gt|=4,Ud(o.current),jd(o,o.current,f,n),gt=y,ki(0,!1),fe&&typeof fe.onPostCommitFiberRoot=="function")try{fe.onPostCommitFiberRoot(ri,o)}catch{}return!0}finally{w.p=s,N.T=i,Wd(t,e)}}function Id(t,e,n){e=xe(n,e),e=Ar(t.stateNode,e,2),t=Sn(t,e,2),t!==null&&(ci(t,2),Ge(t))}function xt(t,e,n){if(t.tag===3)Id(t,t,n);else for(;e!==null;){if(e.tag===3){Id(e,t,n);break}else if(e.tag===1){var i=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof i.componentDidCatch=="function"&&(Rn===null||!Rn.has(i))){t=xe(n,t),n=ad(2),i=Sn(e,n,2),i!==null&&(id(n,i,e,t),ci(i,2),Ge(i));break}}e=e.return}}function Jr(t,e,n){var i=t.pingCache;if(i===null){i=t.pingCache=new mv;var s=new Set;i.set(e,s)}else s=i.get(e),s===void 0&&(s=new Set,i.set(e,s));s.has(n)||(Hr=!0,s.add(n),t=Sv.bind(null,t,e,n),e.then(t,t))}function Sv(t,e,n){var i=t.pingCache;i!==null&&i.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,At===t&&(ht&n)===n&&(Nt===4||Nt===3&&(ht&62914560)===ht&&300>we()-Gr?(gt&2)===0&&Za(t,0):qr|=n,Ya===ht&&(Ya=0)),Ge(t)}function tm(t,e){e===0&&(e=Jc()),t=Da(t,e),t!==null&&(ci(t,e),Ge(t))}function bv(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),tm(t,n)}function Tv(t,e){var n=0;switch(t.tag){case 13:var i=t.stateNode,s=t.memoizedState;s!==null&&(n=s.retryLane);break;case 19:i=t.stateNode;break;case 22:i=t.stateNode._retryCache;break;default:throw Error(r(314))}i!==null&&i.delete(e),tm(t,n)}function xv(t,e){return ru(t,e)}var Ts=null,Ka=null,Fr=!1,xs=!1,Wr=!1,ia=0;function Ge(t){t!==Ka&&t.next===null&&(Ka===null?Ts=Ka=t:Ka=Ka.next=t),xs=!0,Fr||(Fr=!0,Ev())}function ki(t,e){if(!Wr&&xs){Wr=!0;do for(var n=!1,i=Ts;i!==null;){if(t!==0){var s=i.pendingLanes;if(s===0)var o=0;else{var f=i.suspendedLanes,y=i.pingedLanes;o=(1<<31-he(42|t)+1)-1,o&=s&~(f&~y),o=o&201326741?o&201326741|1:o?o|2:0}o!==0&&(n=!0,im(i,o))}else o=ht,o=Rl(i,i===At?o:0,i.cancelPendingCommit!==null||i.timeoutHandle!==-1),(o&3)===0||oi(i,o)||(n=!0,im(i,o));i=i.next}while(n);Wr=!1}}function Av(){em()}function em(){xs=Fr=!1;var t=0;ia!==0&&(jv()&&(t=ia),ia=0);for(var e=we(),n=null,i=Ts;i!==null;){var s=i.next,o=nm(i,e);o===0?(i.next=null,n===null?Ts=s:n.next=s,s===null&&(Ka=n)):(n=i,(t!==0||(o&3)!==0)&&(xs=!0)),i=s}ki(t)}function nm(t,e){for(var n=t.suspendedLanes,i=t.pingedLanes,s=t.expirationTimes,o=t.pendingLanes&-62914561;0<o;){var f=31-he(o),y=1<<f,S=s[f];S===-1?((y&n)===0||(y&i)!==0)&&(s[f]=Jg(y,e)):S<=e&&(t.expiredLanes|=y),o&=~y}if(e=At,n=ht,n=Rl(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),i=t.callbackNode,n===0||t===e&&(vt===2||vt===9)||t.cancelPendingCommit!==null)return i!==null&&i!==null&&ou(i),t.callbackNode=null,t.callbackPriority=0;if((n&3)===0||oi(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(i!==null&&ou(i),hu(n)){case 2:case 8:n=Kc;break;case 32:n=Ml;break;case 268435456:n=kc;break;default:n=Ml}return i=am.bind(null,t),n=ru(n,i),t.callbackPriority=e,t.callbackNode=n,e}return i!==null&&i!==null&&ou(i),t.callbackPriority=2,t.callbackNode=null,2}function am(t,e){if(Jt!==0&&Jt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(bs()&&t.callbackNode!==n)return null;var i=ht;return i=Rl(t,t===At?i:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),i===0?null:(Hd(t,i,e),nm(t,we()),t.callbackNode!=null&&t.callbackNode===n?am.bind(null,t):null)}function im(t,e){if(bs())return null;Hd(t,e,!0)}function Ev(){zv(function(){(gt&6)!==0?ru(Qc,Av):em()})}function $r(){return ia===0&&(ia=Pc()),ia}function lm(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Vl(""+t)}function sm(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function Mv(t,e,n,i,s){if(e==="submit"&&n&&n.stateNode===s){var o=lm((s[ie]||null).action),f=i.submitter;f&&(e=(e=f[ie]||null)?lm(e.formAction):f.getAttribute("formAction"),e!==null&&(o=e,f=null));var y=new wl("action","action",null,i,s);t.push({event:y,listeners:[{instance:null,listener:function(){if(i.defaultPrevented){if(ia!==0){var S=f?sm(s,f):new FormData(s);vr(n,{pending:!0,data:S,method:s.method,action:o},null,S)}}else typeof o=="function"&&(y.preventDefault(),S=f?sm(s,f):new FormData(s),vr(n,{pending:!0,data:S,method:s.method,action:o},o,S))},currentTarget:s}]})}}for(var Ir=0;Ir<wu.length;Ir++){var to=wu[Ir],Dv=to.toLowerCase(),_v=to[0].toUpperCase()+to.slice(1);je(Dv,"on"+_v)}je(Hf,"onAnimationEnd"),je(qf,"onAnimationIteration"),je(Yf,"onAnimationStart"),je("dblclick","onDoubleClick"),je("focusin","onFocus"),je("focusout","onBlur"),je(Z0,"onTransitionRun"),je(Q0,"onTransitionStart"),je(K0,"onTransitionCancel"),je(Gf,"onTransitionEnd"),ya("onMouseEnter",["mouseout","mouseover"]),ya("onMouseLeave",["mouseout","mouseover"]),ya("onPointerEnter",["pointerout","pointerover"]),ya("onPointerLeave",["pointerout","pointerover"]),Gn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Gn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Gn("onBeforeInput",["compositionend","keypress","textInput","paste"]),Gn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Gn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Gn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Pi="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Rv=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Pi));function um(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var i=t[n],s=i.event;i=i.listeners;t:{var o=void 0;if(e)for(var f=i.length-1;0<=f;f--){var y=i[f],S=y.instance,_=y.currentTarget;if(y=y.listener,S!==o&&s.isPropagationStopped())break t;o=y,s.currentTarget=_;try{o(s)}catch(C){os(C)}s.currentTarget=null,o=S}else for(f=0;f<i.length;f++){if(y=i[f],S=y.instance,_=y.currentTarget,y=y.listener,S!==o&&s.isPropagationStopped())break t;o=y,s.currentTarget=_;try{o(s)}catch(C){os(C)}s.currentTarget=null,o=S}}}}function ct(t,e){var n=e[du];n===void 0&&(n=e[du]=new Set);var i=t+"__bubble";n.has(i)||(rm(e,t,2,!1),n.add(i))}function eo(t,e,n){var i=0;e&&(i|=4),rm(n,t,i,e)}var As="_reactListening"+Math.random().toString(36).slice(2);function no(t){if(!t[As]){t[As]=!0,tf.forEach(function(n){n!=="selectionchange"&&(Rv.has(n)||eo(n,!1,t),eo(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[As]||(e[As]=!0,eo("selectionchange",!1,e))}}function rm(t,e,n,i){switch(jm(e)){case 2:var s=e1;break;case 8:s=n1;break;default:s=go}n=s.bind(null,e,n,t),s=void 0,!Eu||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(s=!0),i?s!==void 0?t.addEventListener(e,n,{capture:!0,passive:s}):t.addEventListener(e,n,!0):s!==void 0?t.addEventListener(e,n,{passive:s}):t.addEventListener(e,n,!1)}function ao(t,e,n,i,s){var o=i;if((e&1)===0&&(e&2)===0&&i!==null)t:for(;;){if(i===null)return;var f=i.tag;if(f===3||f===4){var y=i.stateNode.containerInfo;if(y===s)break;if(f===4)for(f=i.return;f!==null;){var S=f.tag;if((S===3||S===4)&&f.stateNode.containerInfo===s)return;f=f.return}for(;y!==null;){if(f=da(y),f===null)return;if(S=f.tag,S===5||S===6||S===26||S===27){i=o=f;continue t}y=y.parentNode}}i=i.return}pf(function(){var _=o,C=xu(n),z=[];t:{var R=Xf.get(t);if(R!==void 0){var O=wl,at=t;switch(t){case"keypress":if(Ul(n)===0)break t;case"keydown":case"keyup":O=x0;break;case"focusin":at="focus",O=Ru;break;case"focusout":at="blur",O=Ru;break;case"beforeblur":case"afterblur":O=Ru;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":O=vf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":O=c0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":O=M0;break;case Hf:case qf:case Yf:O=d0;break;case Gf:O=_0;break;case"scroll":case"scrollend":O=r0;break;case"wheel":O=O0;break;case"copy":case"cut":case"paste":O=p0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":O=bf;break;case"toggle":case"beforetoggle":O=N0}var I=(e&4)!==0,Tt=!I&&(t==="scroll"||t==="scrollend"),M=I?R!==null?R+"Capture":null:R;I=[];for(var A=_,D;A!==null;){var V=A;if(D=V.stateNode,V=V.tag,V!==5&&V!==26&&V!==27||D===null||M===null||(V=di(A,M),V!=null&&I.push(Ji(A,V,D))),Tt)break;A=A.return}0<I.length&&(R=new O(R,at,null,n,C),z.push({event:R,listeners:I}))}}if((e&7)===0){t:{if(R=t==="mouseover"||t==="pointerover",O=t==="mouseout"||t==="pointerout",R&&n!==Tu&&(at=n.relatedTarget||n.fromElement)&&(da(at)||at[ha]))break t;if((O||R)&&(R=C.window===C?C:(R=C.ownerDocument)?R.defaultView||R.parentWindow:window,O?(at=n.relatedTarget||n.toElement,O=_,at=at?da(at):null,at!==null&&(Tt=d(at),I=at.tag,at!==Tt||I!==5&&I!==27&&I!==6)&&(at=null)):(O=null,at=_),O!==at)){if(I=vf,V="onMouseLeave",M="onMouseEnter",A="mouse",(t==="pointerout"||t==="pointerover")&&(I=bf,V="onPointerLeave",M="onPointerEnter",A="pointer"),Tt=O==null?R:hi(O),D=at==null?R:hi(at),R=new I(V,A+"leave",O,n,C),R.target=Tt,R.relatedTarget=D,V=null,da(C)===_&&(I=new I(M,A+"enter",at,n,C),I.target=D,I.relatedTarget=Tt,V=I),Tt=V,O&&at)e:{for(I=O,M=at,A=0,D=I;D;D=ka(D))A++;for(D=0,V=M;V;V=ka(V))D++;for(;0<A-D;)I=ka(I),A--;for(;0<D-A;)M=ka(M),D--;for(;A--;){if(I===M||M!==null&&I===M.alternate)break e;I=ka(I),M=ka(M)}I=null}else I=null;O!==null&&om(z,R,O,I,!1),at!==null&&Tt!==null&&om(z,Tt,at,I,!0)}}t:{if(R=_?hi(_):window,O=R.nodeName&&R.nodeName.toLowerCase(),O==="select"||O==="input"&&R.type==="file")var k=Rf;else if(Df(R))if(Of)k=Y0;else{k=H0;var rt=L0}else O=R.nodeName,!O||O.toLowerCase()!=="input"||R.type!=="checkbox"&&R.type!=="radio"?_&&bu(_.elementType)&&(k=Rf):k=q0;if(k&&(k=k(t,_))){_f(z,k,n,C);break t}rt&&rt(t,R,_),t==="focusout"&&_&&R.type==="number"&&_.memoizedProps.value!=null&&Su(R,"number",R.value)}switch(rt=_?hi(_):window,t){case"focusin":(Df(rt)||rt.contentEditable==="true")&&(Aa=rt,zu=_,Ti=null);break;case"focusout":Ti=zu=Aa=null;break;case"mousedown":Uu=!0;break;case"contextmenu":case"mouseup":case"dragend":Uu=!1,wf(z,n,C);break;case"selectionchange":if(X0)break;case"keydown":case"keyup":wf(z,n,C)}var F;if(Cu)t:{switch(t){case"compositionstart":var tt="onCompositionStart";break t;case"compositionend":tt="onCompositionEnd";break t;case"compositionupdate":tt="onCompositionUpdate";break t}tt=void 0}else xa?Ef(t,n)&&(tt="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(tt="onCompositionStart");tt&&(Tf&&n.locale!=="ko"&&(xa||tt!=="onCompositionStart"?tt==="onCompositionEnd"&&xa&&(F=yf()):(pn=C,Mu="value"in pn?pn.value:pn.textContent,xa=!0)),rt=Es(_,tt),0<rt.length&&(tt=new Sf(tt,t,null,n,C),z.push({event:tt,listeners:rt}),F?tt.data=F:(F=Mf(n),F!==null&&(tt.data=F)))),(F=V0?z0(t,n):U0(t,n))&&(tt=Es(_,"onBeforeInput"),0<tt.length&&(rt=new Sf("onBeforeInput","beforeinput",null,n,C),z.push({event:rt,listeners:tt}),rt.data=F)),Mv(z,t,_,n,C)}um(z,e)})}function Ji(t,e,n){return{instance:t,listener:e,currentTarget:n}}function Es(t,e){for(var n=e+"Capture",i=[];t!==null;){var s=t,o=s.stateNode;if(s=s.tag,s!==5&&s!==26&&s!==27||o===null||(s=di(t,n),s!=null&&i.unshift(Ji(t,s,o)),s=di(t,e),s!=null&&i.push(Ji(t,s,o))),t.tag===3)return i;t=t.return}return[]}function ka(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function om(t,e,n,i,s){for(var o=e._reactName,f=[];n!==null&&n!==i;){var y=n,S=y.alternate,_=y.stateNode;if(y=y.tag,S!==null&&S===i)break;y!==5&&y!==26&&y!==27||_===null||(S=_,s?(_=di(n,o),_!=null&&f.unshift(Ji(n,_,S))):s||(_=di(n,o),_!=null&&f.push(Ji(n,_,S)))),n=n.return}f.length!==0&&t.push({event:e,listeners:f})}var Ov=/\r\n?/g,Cv=/\u0000|\uFFFD/g;function cm(t){return(typeof t=="string"?t:""+t).replace(Ov,`
`).replace(Cv,"")}function fm(t,e){return e=cm(e),cm(t)===e}function Ms(){}function bt(t,e,n,i,s,o){switch(n){case"children":typeof i=="string"?e==="body"||e==="textarea"&&i===""||Sa(t,i):(typeof i=="number"||typeof i=="bigint")&&e!=="body"&&Sa(t,""+i);break;case"className":Cl(t,"class",i);break;case"tabIndex":Cl(t,"tabindex",i);break;case"dir":case"role":case"viewBox":case"width":case"height":Cl(t,n,i);break;case"style":df(t,i,o);break;case"data":if(e!=="object"){Cl(t,"data",i);break}case"src":case"href":if(i===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(i==null||typeof i=="function"||typeof i=="symbol"||typeof i=="boolean"){t.removeAttribute(n);break}i=Vl(""+i),t.setAttribute(n,i);break;case"action":case"formAction":if(typeof i=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof o=="function"&&(n==="formAction"?(e!=="input"&&bt(t,e,"name",s.name,s,null),bt(t,e,"formEncType",s.formEncType,s,null),bt(t,e,"formMethod",s.formMethod,s,null),bt(t,e,"formTarget",s.formTarget,s,null)):(bt(t,e,"encType",s.encType,s,null),bt(t,e,"method",s.method,s,null),bt(t,e,"target",s.target,s,null)));if(i==null||typeof i=="symbol"||typeof i=="boolean"){t.removeAttribute(n);break}i=Vl(""+i),t.setAttribute(n,i);break;case"onClick":i!=null&&(t.onclick=Ms);break;case"onScroll":i!=null&&ct("scroll",t);break;case"onScrollEnd":i!=null&&ct("scrollend",t);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(r(61));if(n=i.__html,n!=null){if(s.children!=null)throw Error(r(60));t.innerHTML=n}}break;case"multiple":t.multiple=i&&typeof i!="function"&&typeof i!="symbol";break;case"muted":t.muted=i&&typeof i!="function"&&typeof i!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(i==null||typeof i=="function"||typeof i=="boolean"||typeof i=="symbol"){t.removeAttribute("xlink:href");break}n=Vl(""+i),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":i!=null&&typeof i!="function"&&typeof i!="symbol"?t.setAttribute(n,""+i):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":i&&typeof i!="function"&&typeof i!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":i===!0?t.setAttribute(n,""):i!==!1&&i!=null&&typeof i!="function"&&typeof i!="symbol"?t.setAttribute(n,i):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":i!=null&&typeof i!="function"&&typeof i!="symbol"&&!isNaN(i)&&1<=i?t.setAttribute(n,i):t.removeAttribute(n);break;case"rowSpan":case"start":i==null||typeof i=="function"||typeof i=="symbol"||isNaN(i)?t.removeAttribute(n):t.setAttribute(n,i);break;case"popover":ct("beforetoggle",t),ct("toggle",t),Ol(t,"popover",i);break;case"xlinkActuate":Pe(t,"http://www.w3.org/1999/xlink","xlink:actuate",i);break;case"xlinkArcrole":Pe(t,"http://www.w3.org/1999/xlink","xlink:arcrole",i);break;case"xlinkRole":Pe(t,"http://www.w3.org/1999/xlink","xlink:role",i);break;case"xlinkShow":Pe(t,"http://www.w3.org/1999/xlink","xlink:show",i);break;case"xlinkTitle":Pe(t,"http://www.w3.org/1999/xlink","xlink:title",i);break;case"xlinkType":Pe(t,"http://www.w3.org/1999/xlink","xlink:type",i);break;case"xmlBase":Pe(t,"http://www.w3.org/XML/1998/namespace","xml:base",i);break;case"xmlLang":Pe(t,"http://www.w3.org/XML/1998/namespace","xml:lang",i);break;case"xmlSpace":Pe(t,"http://www.w3.org/XML/1998/namespace","xml:space",i);break;case"is":Ol(t,"is",i);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=s0.get(n)||n,Ol(t,n,i))}}function io(t,e,n,i,s,o){switch(n){case"style":df(t,i,o);break;case"dangerouslySetInnerHTML":if(i!=null){if(typeof i!="object"||!("__html"in i))throw Error(r(61));if(n=i.__html,n!=null){if(s.children!=null)throw Error(r(60));t.innerHTML=n}}break;case"children":typeof i=="string"?Sa(t,i):(typeof i=="number"||typeof i=="bigint")&&Sa(t,""+i);break;case"onScroll":i!=null&&ct("scroll",t);break;case"onScrollEnd":i!=null&&ct("scrollend",t);break;case"onClick":i!=null&&(t.onclick=Ms);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!ef.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(s=n.endsWith("Capture"),e=n.slice(2,s?n.length-7:void 0),o=t[ie]||null,o=o!=null?o[n]:null,typeof o=="function"&&t.removeEventListener(e,o,s),typeof i=="function")){typeof o!="function"&&o!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,i,s);break t}n in t?t[n]=i:i===!0?t.setAttribute(n,""):Ol(t,n,i)}}}function Ft(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ct("error",t),ct("load",t);var i=!1,s=!1,o;for(o in n)if(n.hasOwnProperty(o)){var f=n[o];if(f!=null)switch(o){case"src":i=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:bt(t,e,o,f,n,null)}}s&&bt(t,e,"srcSet",n.srcSet,n,null),i&&bt(t,e,"src",n.src,n,null);return;case"input":ct("invalid",t);var y=o=f=s=null,S=null,_=null;for(i in n)if(n.hasOwnProperty(i)){var C=n[i];if(C!=null)switch(i){case"name":s=C;break;case"type":f=C;break;case"checked":S=C;break;case"defaultChecked":_=C;break;case"value":o=C;break;case"defaultValue":y=C;break;case"children":case"dangerouslySetInnerHTML":if(C!=null)throw Error(r(137,e));break;default:bt(t,e,i,C,n,null)}}of(t,o,y,S,_,f,s,!1),Nl(t);return;case"select":ct("invalid",t),i=f=o=null;for(s in n)if(n.hasOwnProperty(s)&&(y=n[s],y!=null))switch(s){case"value":o=y;break;case"defaultValue":f=y;break;case"multiple":i=y;default:bt(t,e,s,y,n,null)}e=o,n=f,t.multiple=!!i,e!=null?va(t,!!i,e,!1):n!=null&&va(t,!!i,n,!0);return;case"textarea":ct("invalid",t),o=s=i=null;for(f in n)if(n.hasOwnProperty(f)&&(y=n[f],y!=null))switch(f){case"value":i=y;break;case"defaultValue":s=y;break;case"children":o=y;break;case"dangerouslySetInnerHTML":if(y!=null)throw Error(r(91));break;default:bt(t,e,f,y,n,null)}ff(t,i,s,o),Nl(t);return;case"option":for(S in n)if(n.hasOwnProperty(S)&&(i=n[S],i!=null))switch(S){case"selected":t.selected=i&&typeof i!="function"&&typeof i!="symbol";break;default:bt(t,e,S,i,n,null)}return;case"dialog":ct("beforetoggle",t),ct("toggle",t),ct("cancel",t),ct("close",t);break;case"iframe":case"object":ct("load",t);break;case"video":case"audio":for(i=0;i<Pi.length;i++)ct(Pi[i],t);break;case"image":ct("error",t),ct("load",t);break;case"details":ct("toggle",t);break;case"embed":case"source":case"link":ct("error",t),ct("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(_ in n)if(n.hasOwnProperty(_)&&(i=n[_],i!=null))switch(_){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,e));default:bt(t,e,_,i,n,null)}return;default:if(bu(e)){for(C in n)n.hasOwnProperty(C)&&(i=n[C],i!==void 0&&io(t,e,C,i,n,void 0));return}}for(y in n)n.hasOwnProperty(y)&&(i=n[y],i!=null&&bt(t,e,y,i,n,null))}function Nv(t,e,n,i){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var s=null,o=null,f=null,y=null,S=null,_=null,C=null;for(O in n){var z=n[O];if(n.hasOwnProperty(O)&&z!=null)switch(O){case"checked":break;case"value":break;case"defaultValue":S=z;default:i.hasOwnProperty(O)||bt(t,e,O,null,i,z)}}for(var R in i){var O=i[R];if(z=n[R],i.hasOwnProperty(R)&&(O!=null||z!=null))switch(R){case"type":o=O;break;case"name":s=O;break;case"checked":_=O;break;case"defaultChecked":C=O;break;case"value":f=O;break;case"defaultValue":y=O;break;case"children":case"dangerouslySetInnerHTML":if(O!=null)throw Error(r(137,e));break;default:O!==z&&bt(t,e,R,O,i,z)}}vu(t,f,y,S,_,C,o,s);return;case"select":O=f=y=R=null;for(o in n)if(S=n[o],n.hasOwnProperty(o)&&S!=null)switch(o){case"value":break;case"multiple":O=S;default:i.hasOwnProperty(o)||bt(t,e,o,null,i,S)}for(s in i)if(o=i[s],S=n[s],i.hasOwnProperty(s)&&(o!=null||S!=null))switch(s){case"value":R=o;break;case"defaultValue":y=o;break;case"multiple":f=o;default:o!==S&&bt(t,e,s,o,i,S)}e=y,n=f,i=O,R!=null?va(t,!!n,R,!1):!!i!=!!n&&(e!=null?va(t,!!n,e,!0):va(t,!!n,n?[]:"",!1));return;case"textarea":O=R=null;for(y in n)if(s=n[y],n.hasOwnProperty(y)&&s!=null&&!i.hasOwnProperty(y))switch(y){case"value":break;case"children":break;default:bt(t,e,y,null,i,s)}for(f in i)if(s=i[f],o=n[f],i.hasOwnProperty(f)&&(s!=null||o!=null))switch(f){case"value":R=s;break;case"defaultValue":O=s;break;case"children":break;case"dangerouslySetInnerHTML":if(s!=null)throw Error(r(91));break;default:s!==o&&bt(t,e,f,s,i,o)}cf(t,R,O);return;case"option":for(var at in n)if(R=n[at],n.hasOwnProperty(at)&&R!=null&&!i.hasOwnProperty(at))switch(at){case"selected":t.selected=!1;break;default:bt(t,e,at,null,i,R)}for(S in i)if(R=i[S],O=n[S],i.hasOwnProperty(S)&&R!==O&&(R!=null||O!=null))switch(S){case"selected":t.selected=R&&typeof R!="function"&&typeof R!="symbol";break;default:bt(t,e,S,R,i,O)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var I in n)R=n[I],n.hasOwnProperty(I)&&R!=null&&!i.hasOwnProperty(I)&&bt(t,e,I,null,i,R);for(_ in i)if(R=i[_],O=n[_],i.hasOwnProperty(_)&&R!==O&&(R!=null||O!=null))switch(_){case"children":case"dangerouslySetInnerHTML":if(R!=null)throw Error(r(137,e));break;default:bt(t,e,_,R,i,O)}return;default:if(bu(e)){for(var Tt in n)R=n[Tt],n.hasOwnProperty(Tt)&&R!==void 0&&!i.hasOwnProperty(Tt)&&io(t,e,Tt,void 0,i,R);for(C in i)R=i[C],O=n[C],!i.hasOwnProperty(C)||R===O||R===void 0&&O===void 0||io(t,e,C,R,i,O);return}}for(var M in n)R=n[M],n.hasOwnProperty(M)&&R!=null&&!i.hasOwnProperty(M)&&bt(t,e,M,null,i,R);for(z in i)R=i[z],O=n[z],!i.hasOwnProperty(z)||R===O||R==null&&O==null||bt(t,e,z,R,i,O)}var lo=null,so=null;function Ds(t){return t.nodeType===9?t:t.ownerDocument}function hm(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function dm(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function uo(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var ro=null;function jv(){var t=window.event;return t&&t.type==="popstate"?t===ro?!1:(ro=t,!0):(ro=null,!1)}var mm=typeof setTimeout=="function"?setTimeout:void 0,Vv=typeof clearTimeout=="function"?clearTimeout:void 0,pm=typeof Promise=="function"?Promise:void 0,zv=typeof queueMicrotask=="function"?queueMicrotask:typeof pm<"u"?function(t){return pm.resolve(null).then(t).catch(Uv)}:mm;function Uv(t){setTimeout(function(){throw t})}function Nn(t){return t==="head"}function ym(t,e){var n=e,i=0,s=0;do{var o=n.nextSibling;if(t.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(0<i&&8>i){n=i;var f=t.ownerDocument;if(n&1&&Fi(f.documentElement),n&2&&Fi(f.body),n&4)for(n=f.head,Fi(n),f=n.firstChild;f;){var y=f.nextSibling,S=f.nodeName;f[fi]||S==="SCRIPT"||S==="STYLE"||S==="LINK"&&f.rel.toLowerCase()==="stylesheet"||n.removeChild(f),f=y}}if(s===0){t.removeChild(o),il(e);return}s--}else n==="$"||n==="$?"||n==="$!"?s++:i=n.charCodeAt(0)-48;else i=0;n=o}while(n);il(e)}function oo(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":oo(n),mu(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function Bv(t,e,n,i){for(;t.nodeType===1;){var s=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!i&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(i){if(!t[fi])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(o=t.getAttribute("rel"),o==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(o!==s.rel||t.getAttribute("href")!==(s.href==null||s.href===""?null:s.href)||t.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin)||t.getAttribute("title")!==(s.title==null?null:s.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(o=t.getAttribute("src"),(o!==(s.src==null?null:s.src)||t.getAttribute("type")!==(s.type==null?null:s.type)||t.getAttribute("crossorigin")!==(s.crossOrigin==null?null:s.crossOrigin))&&o&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var o=s.name==null?null:""+s.name;if(s.type==="hidden"&&t.getAttribute("name")===o)return t}else return t;if(t=ze(t.nextSibling),t===null)break}return null}function wv(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=ze(t.nextSibling),t===null))return null;return t}function co(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function Lv(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var i=function(){e(),n.removeEventListener("DOMContentLoaded",i)};n.addEventListener("DOMContentLoaded",i),t._reactRetry=i}}function ze(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var fo=null;function gm(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function vm(t,e,n){switch(e=Ds(n),t){case"html":if(t=e.documentElement,!t)throw Error(r(452));return t;case"head":if(t=e.head,!t)throw Error(r(453));return t;case"body":if(t=e.body,!t)throw Error(r(454));return t;default:throw Error(r(451))}}function Fi(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);mu(t)}var Re=new Map,Sm=new Set;function _s(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var on=w.d;w.d={f:Hv,r:qv,D:Yv,C:Gv,L:Xv,m:Zv,X:Kv,S:Qv,M:kv};function Hv(){var t=on.f(),e=vs();return t||e}function qv(t){var e=ma(t);e!==null&&e.tag===5&&e.type==="form"?Hh(e):on.r(t)}var Pa=typeof document>"u"?null:document;function bm(t,e,n){var i=Pa;if(i&&typeof e=="string"&&e){var s=Te(e);s='link[rel="'+t+'"][href="'+s+'"]',typeof n=="string"&&(s+='[crossorigin="'+n+'"]'),Sm.has(s)||(Sm.add(s),t={rel:t,crossOrigin:n,href:e},i.querySelector(s)===null&&(e=i.createElement("link"),Ft(e,"link",t),Zt(e),i.head.appendChild(e)))}}function Yv(t){on.D(t),bm("dns-prefetch",t,null)}function Gv(t,e){on.C(t,e),bm("preconnect",t,e)}function Xv(t,e,n){on.L(t,e,n);var i=Pa;if(i&&t&&e){var s='link[rel="preload"][as="'+Te(e)+'"]';e==="image"&&n&&n.imageSrcSet?(s+='[imagesrcset="'+Te(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(s+='[imagesizes="'+Te(n.imageSizes)+'"]')):s+='[href="'+Te(t)+'"]';var o=s;switch(e){case"style":o=Ja(t);break;case"script":o=Fa(t)}Re.has(o)||(t=v({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),Re.set(o,t),i.querySelector(s)!==null||e==="style"&&i.querySelector(Wi(o))||e==="script"&&i.querySelector($i(o))||(e=i.createElement("link"),Ft(e,"link",t),Zt(e),i.head.appendChild(e)))}}function Zv(t,e){on.m(t,e);var n=Pa;if(n&&t){var i=e&&typeof e.as=="string"?e.as:"script",s='link[rel="modulepreload"][as="'+Te(i)+'"][href="'+Te(t)+'"]',o=s;switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":o=Fa(t)}if(!Re.has(o)&&(t=v({rel:"modulepreload",href:t},e),Re.set(o,t),n.querySelector(s)===null)){switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector($i(o)))return}i=n.createElement("link"),Ft(i,"link",t),Zt(i),n.head.appendChild(i)}}}function Qv(t,e,n){on.S(t,e,n);var i=Pa;if(i&&t){var s=pa(i).hoistableStyles,o=Ja(t);e=e||"default";var f=s.get(o);if(!f){var y={loading:0,preload:null};if(f=i.querySelector(Wi(o)))y.loading=5;else{t=v({rel:"stylesheet",href:t,"data-precedence":e},n),(n=Re.get(o))&&ho(t,n);var S=f=i.createElement("link");Zt(S),Ft(S,"link",t),S._p=new Promise(function(_,C){S.onload=_,S.onerror=C}),S.addEventListener("load",function(){y.loading|=1}),S.addEventListener("error",function(){y.loading|=2}),y.loading|=4,Rs(f,e,i)}f={type:"stylesheet",instance:f,count:1,state:y},s.set(o,f)}}}function Kv(t,e){on.X(t,e);var n=Pa;if(n&&t){var i=pa(n).hoistableScripts,s=Fa(t),o=i.get(s);o||(o=n.querySelector($i(s)),o||(t=v({src:t,async:!0},e),(e=Re.get(s))&&mo(t,e),o=n.createElement("script"),Zt(o),Ft(o,"link",t),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},i.set(s,o))}}function kv(t,e){on.M(t,e);var n=Pa;if(n&&t){var i=pa(n).hoistableScripts,s=Fa(t),o=i.get(s);o||(o=n.querySelector($i(s)),o||(t=v({src:t,async:!0,type:"module"},e),(e=Re.get(s))&&mo(t,e),o=n.createElement("script"),Zt(o),Ft(o,"link",t),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},i.set(s,o))}}function Tm(t,e,n,i){var s=(s=lt.current)?_s(s):null;if(!s)throw Error(r(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=Ja(n.href),n=pa(s).hoistableStyles,i=n.get(e),i||(i={type:"style",instance:null,count:0,state:null},n.set(e,i)),i):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=Ja(n.href);var o=pa(s).hoistableStyles,f=o.get(t);if(f||(s=s.ownerDocument||s,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},o.set(t,f),(o=s.querySelector(Wi(t)))&&!o._p&&(f.instance=o,f.state.loading=5),Re.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Re.set(t,n),o||Pv(s,t,n,f.state))),e&&i===null)throw Error(r(528,""));return f}if(e&&i!==null)throw Error(r(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Fa(n),n=pa(s).hoistableScripts,i=n.get(e),i||(i={type:"script",instance:null,count:0,state:null},n.set(e,i)),i):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,t))}}function Ja(t){return'href="'+Te(t)+'"'}function Wi(t){return'link[rel="stylesheet"]['+t+"]"}function xm(t){return v({},t,{"data-precedence":t.precedence,precedence:null})}function Pv(t,e,n,i){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?i.loading=1:(e=t.createElement("link"),i.preload=e,e.addEventListener("load",function(){return i.loading|=1}),e.addEventListener("error",function(){return i.loading|=2}),Ft(e,"link",n),Zt(e),t.head.appendChild(e))}function Fa(t){return'[src="'+Te(t)+'"]'}function $i(t){return"script[async]"+t}function Am(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var i=t.querySelector('style[data-href~="'+Te(n.href)+'"]');if(i)return e.instance=i,Zt(i),i;var s=v({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return i=(t.ownerDocument||t).createElement("style"),Zt(i),Ft(i,"style",s),Rs(i,n.precedence,t),e.instance=i;case"stylesheet":s=Ja(n.href);var o=t.querySelector(Wi(s));if(o)return e.state.loading|=4,e.instance=o,Zt(o),o;i=xm(n),(s=Re.get(s))&&ho(i,s),o=(t.ownerDocument||t).createElement("link"),Zt(o);var f=o;return f._p=new Promise(function(y,S){f.onload=y,f.onerror=S}),Ft(o,"link",i),e.state.loading|=4,Rs(o,n.precedence,t),e.instance=o;case"script":return o=Fa(n.src),(s=t.querySelector($i(o)))?(e.instance=s,Zt(s),s):(i=n,(s=Re.get(o))&&(i=v({},n),mo(i,s)),t=t.ownerDocument||t,s=t.createElement("script"),Zt(s),Ft(s,"link",i),t.head.appendChild(s),e.instance=s);case"void":return null;default:throw Error(r(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(i=e.instance,e.state.loading|=4,Rs(i,n.precedence,t));return e.instance}function Rs(t,e,n){for(var i=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),s=i.length?i[i.length-1]:null,o=s,f=0;f<i.length;f++){var y=i[f];if(y.dataset.precedence===e)o=y;else if(o!==s)break}o?o.parentNode.insertBefore(t,o.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function ho(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function mo(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Os=null;function Em(t,e,n){if(Os===null){var i=new Map,s=Os=new Map;s.set(n,i)}else s=Os,i=s.get(n),i||(i=new Map,s.set(n,i));if(i.has(t))return i;for(i.set(t,null),n=n.getElementsByTagName(t),s=0;s<n.length;s++){var o=n[s];if(!(o[fi]||o[$t]||t==="link"&&o.getAttribute("rel")==="stylesheet")&&o.namespaceURI!=="http://www.w3.org/2000/svg"){var f=o.getAttribute(e)||"";f=t+f;var y=i.get(f);y?y.push(o):i.set(f,[o])}}return i}function Mm(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function Jv(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Dm(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Ii=null;function Fv(){}function Wv(t,e,n){if(Ii===null)throw Error(r(475));var i=Ii;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var s=Ja(n.href),o=t.querySelector(Wi(s));if(o){t=o._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(i.count++,i=Cs.bind(i),t.then(i,i)),e.state.loading|=4,e.instance=o,Zt(o);return}o=t.ownerDocument||t,n=xm(n),(s=Re.get(s))&&ho(n,s),o=o.createElement("link"),Zt(o);var f=o;f._p=new Promise(function(y,S){f.onload=y,f.onerror=S}),Ft(o,"link",n),e.instance=o}i.stylesheets===null&&(i.stylesheets=new Map),i.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(i.count++,e=Cs.bind(i),t.addEventListener("load",e),t.addEventListener("error",e))}}function $v(){if(Ii===null)throw Error(r(475));var t=Ii;return t.stylesheets&&t.count===0&&po(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&po(t,t.stylesheets),t.unsuspend){var i=t.unsuspend;t.unsuspend=null,i()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function Cs(){if(this.count--,this.count===0){if(this.stylesheets)po(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Ns=null;function po(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Ns=new Map,e.forEach(Iv,t),Ns=null,Cs.call(t))}function Iv(t,e){if(!(e.state.loading&4)){var n=Ns.get(t);if(n)var i=n.get(null);else{n=new Map,Ns.set(t,n);for(var s=t.querySelectorAll("link[data-precedence],style[data-precedence]"),o=0;o<s.length;o++){var f=s[o];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(n.set(f.dataset.precedence,f),i=f)}i&&n.set(null,i)}s=e.instance,f=s.getAttribute("data-precedence"),o=n.get(f)||i,o===i&&n.set(null,s),n.set(f,s),this.count++,i=Cs.bind(this),s.addEventListener("load",i),s.addEventListener("error",i),o?o.parentNode.insertBefore(s,o.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(s,t.firstChild)),e.state.loading|=4}}var tl={$$typeof:H,Provider:null,Consumer:null,_currentValue:P,_currentValue2:P,_threadCount:0};function t1(t,e,n,i,s,o,f,y){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=cu(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=cu(0),this.hiddenUpdates=cu(null),this.identifierPrefix=i,this.onUncaughtError=s,this.onCaughtError=o,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=y,this.incompleteTransitions=new Map}function _m(t,e,n,i,s,o,f,y,S,_,C,z){return t=new t1(t,e,n,f,y,S,_,z),e=1,o===!0&&(e|=24),o=me(3,null,null,e),t.current=o,o.stateNode=t,e=Ju(),e.refCount++,t.pooledCache=e,e.refCount++,o.memoizedState={element:i,isDehydrated:n,cache:e},Iu(o),t}function Rm(t){return t?(t=_a,t):_a}function Om(t,e,n,i,s,o){s=Rm(s),i.context===null?i.context=s:i.pendingContext=s,i=vn(e),i.payload={element:n},o=o===void 0?null:o,o!==null&&(i.callback=o),n=Sn(t,i,e),n!==null&&(Se(n,t,e),Ci(n,t,e))}function Cm(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function yo(t,e){Cm(t,e),(t=t.alternate)&&Cm(t,e)}function Nm(t){if(t.tag===13){var e=Da(t,67108864);e!==null&&Se(e,t,67108864),yo(t,67108864)}}var js=!0;function e1(t,e,n,i){var s=N.T;N.T=null;var o=w.p;try{w.p=2,go(t,e,n,i)}finally{w.p=o,N.T=s}}function n1(t,e,n,i){var s=N.T;N.T=null;var o=w.p;try{w.p=8,go(t,e,n,i)}finally{w.p=o,N.T=s}}function go(t,e,n,i){if(js){var s=vo(i);if(s===null)ao(t,e,i,Vs,n),Vm(t,i);else if(i1(s,t,e,n,i))i.stopPropagation();else if(Vm(t,i),e&4&&-1<a1.indexOf(t)){for(;s!==null;){var o=ma(s);if(o!==null)switch(o.tag){case 3:if(o=o.stateNode,o.current.memoizedState.isDehydrated){var f=Yn(o.pendingLanes);if(f!==0){var y=o;for(y.pendingLanes|=2,y.entangledLanes|=2;f;){var S=1<<31-he(f);y.entanglements[1]|=S,f&=~S}Ge(o),(gt&6)===0&&(ys=we()+500,ki(0))}}break;case 13:y=Da(o,2),y!==null&&Se(y,o,2),vs(),yo(o,2)}if(o=vo(i),o===null&&ao(t,e,i,Vs,n),o===s)break;s=o}s!==null&&i.stopPropagation()}else ao(t,e,i,null,n)}}function vo(t){return t=xu(t),So(t)}var Vs=null;function So(t){if(Vs=null,t=da(t),t!==null){var e=d(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=h(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Vs=t,null}function jm(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Gg()){case Qc:return 2;case Kc:return 8;case Ml:case Xg:return 32;case kc:return 268435456;default:return 32}default:return 32}}var bo=!1,jn=null,Vn=null,zn=null,el=new Map,nl=new Map,Un=[],a1="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Vm(t,e){switch(t){case"focusin":case"focusout":jn=null;break;case"dragenter":case"dragleave":Vn=null;break;case"mouseover":case"mouseout":zn=null;break;case"pointerover":case"pointerout":el.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":nl.delete(e.pointerId)}}function al(t,e,n,i,s,o){return t===null||t.nativeEvent!==o?(t={blockedOn:e,domEventName:n,eventSystemFlags:i,nativeEvent:o,targetContainers:[s]},e!==null&&(e=ma(e),e!==null&&Nm(e)),t):(t.eventSystemFlags|=i,e=t.targetContainers,s!==null&&e.indexOf(s)===-1&&e.push(s),t)}function i1(t,e,n,i,s){switch(e){case"focusin":return jn=al(jn,t,e,n,i,s),!0;case"dragenter":return Vn=al(Vn,t,e,n,i,s),!0;case"mouseover":return zn=al(zn,t,e,n,i,s),!0;case"pointerover":var o=s.pointerId;return el.set(o,al(el.get(o)||null,t,e,n,i,s)),!0;case"gotpointercapture":return o=s.pointerId,nl.set(o,al(nl.get(o)||null,t,e,n,i,s)),!0}return!1}function zm(t){var e=da(t.target);if(e!==null){var n=d(e);if(n!==null){if(e=n.tag,e===13){if(e=h(n),e!==null){t.blockedOn=e,Wg(t.priority,function(){if(n.tag===13){var i=ve();i=fu(i);var s=Da(n,i);s!==null&&Se(s,n,i),yo(n,i)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function zs(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=vo(t.nativeEvent);if(n===null){n=t.nativeEvent;var i=new n.constructor(n.type,n);Tu=i,n.target.dispatchEvent(i),Tu=null}else return e=ma(n),e!==null&&Nm(e),t.blockedOn=n,!1;e.shift()}return!0}function Um(t,e,n){zs(t)&&n.delete(e)}function l1(){bo=!1,jn!==null&&zs(jn)&&(jn=null),Vn!==null&&zs(Vn)&&(Vn=null),zn!==null&&zs(zn)&&(zn=null),el.forEach(Um),nl.forEach(Um)}function Us(t,e){t.blockedOn===e&&(t.blockedOn=null,bo||(bo=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,l1)))}var Bs=null;function Bm(t){Bs!==t&&(Bs=t,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){Bs===t&&(Bs=null);for(var e=0;e<t.length;e+=3){var n=t[e],i=t[e+1],s=t[e+2];if(typeof i!="function"){if(So(i||n)===null)continue;break}var o=ma(n);o!==null&&(t.splice(e,3),e-=3,vr(o,{pending:!0,data:s,method:n.method,action:i},i,s))}}))}function il(t){function e(S){return Us(S,t)}jn!==null&&Us(jn,t),Vn!==null&&Us(Vn,t),zn!==null&&Us(zn,t),el.forEach(e),nl.forEach(e);for(var n=0;n<Un.length;n++){var i=Un[n];i.blockedOn===t&&(i.blockedOn=null)}for(;0<Un.length&&(n=Un[0],n.blockedOn===null);)zm(n),n.blockedOn===null&&Un.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(i=0;i<n.length;i+=3){var s=n[i],o=n[i+1],f=s[ie]||null;if(typeof o=="function")f||Bm(n);else if(f){var y=null;if(o&&o.hasAttribute("formAction")){if(s=o,f=o[ie]||null)y=f.formAction;else if(So(s)!==null)continue}else y=f.action;typeof y=="function"?n[i+1]=y:(n.splice(i,3),i-=3),Bm(n)}}}function To(t){this._internalRoot=t}ws.prototype.render=To.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(r(409));var n=e.current,i=ve();Om(n,i,t,e,null,null)},ws.prototype.unmount=To.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Om(t.current,2,null,t,null,null),vs(),e[ha]=null}};function ws(t){this._internalRoot=t}ws.prototype.unstable_scheduleHydration=function(t){if(t){var e=$c();t={blockedOn:null,target:t,priority:e};for(var n=0;n<Un.length&&e!==0&&e<Un[n].priority;n++);Un.splice(n,0,t),n===0&&zm(t)}};var wm=l.version;if(wm!=="19.1.1")throw Error(r(527,wm,"19.1.1"));w.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(r(188)):(t=Object.keys(t).join(","),Error(r(268,t)));return t=p(e),t=t!==null?m(t):null,t=t===null?null:t.stateNode,t};var s1={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:N,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ls=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ls.isDisabled&&Ls.supportsFiber)try{ri=Ls.inject(s1),fe=Ls}catch{}}return sl.createRoot=function(t,e){if(!c(t))throw Error(r(299));var n=!1,i="",s=Ih,o=td,f=ed,y=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(i=e.identifierPrefix),e.onUncaughtError!==void 0&&(s=e.onUncaughtError),e.onCaughtError!==void 0&&(o=e.onCaughtError),e.onRecoverableError!==void 0&&(f=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(y=e.unstable_transitionCallbacks)),e=_m(t,1,!1,null,null,n,i,s,o,f,y,null),t[ha]=e.current,no(t),new To(e)},sl.hydrateRoot=function(t,e,n){if(!c(t))throw Error(r(299));var i=!1,s="",o=Ih,f=td,y=ed,S=null,_=null;return n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onUncaughtError!==void 0&&(o=n.onUncaughtError),n.onCaughtError!==void 0&&(f=n.onCaughtError),n.onRecoverableError!==void 0&&(y=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(S=n.unstable_transitionCallbacks),n.formState!==void 0&&(_=n.formState)),e=_m(t,1,!0,e,n??null,i,s,o,f,y,S,_),e.context=Rm(null),n=e.current,i=ve(),i=fu(i),s=vn(i),s.callback=null,Sn(n,s,i),n=i,e.current.lanes=n,ci(e,n),Ge(e),t[ha]=e.current,no(t),new ws(e)},sl.version="19.1.1",sl}var km;function y1(){if(km)return Eo.exports;km=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(l){console.error(l)}}return a(),Eo.exports=p1(),Eo.exports}var g1=y1();const fc=K.createContext({});function hc(a){const l=K.useRef(null);return l.current===null&&(l.current=a()),l.current}const dc=typeof window<"u",sy=dc?K.useLayoutEffect:K.useEffect,Is=K.createContext(null);function mc(a,l){a.indexOf(l)===-1&&a.push(l)}function pc(a,l){const u=a.indexOf(l);u>-1&&a.splice(u,1)}const cn=(a,l,u)=>u>l?l:u<a?a:u;let yc=()=>{};const fn={},uy=a=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(a);function ry(a){return typeof a=="object"&&a!==null}const oy=a=>/^0[^.\s]+$/u.test(a);function gc(a){let l;return()=>(l===void 0&&(l=a()),l)}const Ne=a=>a,v1=(a,l)=>u=>l(a(u)),bl=(...a)=>a.reduce(v1),ml=(a,l,u)=>{const r=l-a;return r===0?1:(u-a)/r};class vc{constructor(){this.subscriptions=[]}add(l){return mc(this.subscriptions,l),()=>pc(this.subscriptions,l)}notify(l,u,r){const c=this.subscriptions.length;if(c)if(c===1)this.subscriptions[0](l,u,r);else for(let d=0;d<c;d++){const h=this.subscriptions[d];h&&h(l,u,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Xe=a=>a*1e3,Ze=a=>a/1e3;function cy(a,l){return l?a*(1e3/l):0}const fy=(a,l,u)=>(((1-3*u+3*l)*a+(3*u-6*l))*a+3*l)*a,S1=1e-7,b1=12;function T1(a,l,u,r,c){let d,h,g=0;do h=l+(u-l)/2,d=fy(h,r,c)-a,d>0?u=h:l=h;while(Math.abs(d)>S1&&++g<b1);return h}function Tl(a,l,u,r){if(a===l&&u===r)return Ne;const c=d=>T1(d,0,1,a,u);return d=>d===0||d===1?d:fy(c(d),l,r)}const hy=a=>l=>l<=.5?a(2*l)/2:(2-a(2*(1-l)))/2,dy=a=>l=>1-a(1-l),my=Tl(.33,1.53,.69,.99),Sc=dy(my),py=hy(Sc),yy=a=>(a*=2)<1?.5*Sc(a):.5*(2-Math.pow(2,-10*(a-1))),bc=a=>1-Math.sin(Math.acos(a)),gy=dy(bc),vy=hy(bc),x1=Tl(.42,0,1,1),A1=Tl(0,0,.58,1),Sy=Tl(.42,0,.58,1),E1=a=>Array.isArray(a)&&typeof a[0]!="number",by=a=>Array.isArray(a)&&typeof a[0]=="number",M1={linear:Ne,easeIn:x1,easeInOut:Sy,easeOut:A1,circIn:bc,circInOut:vy,circOut:gy,backIn:Sc,backInOut:py,backOut:my,anticipate:yy},D1=a=>typeof a=="string",Pm=a=>{if(by(a)){yc(a.length===4);const[l,u,r,c]=a;return Tl(l,u,r,c)}else if(D1(a))return M1[a];return a},Hs=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];function _1(a,l){let u=new Set,r=new Set,c=!1,d=!1;const h=new WeakSet;let g={delta:0,timestamp:0,isProcessing:!1};function p(v){h.has(v)&&(m.schedule(v),a()),v(g)}const m={schedule:(v,b=!1,E=!1)=>{const B=E&&c?u:r;return b&&h.add(v),B.has(v)||B.add(v),v},cancel:v=>{r.delete(v),h.delete(v)},process:v=>{if(g=v,c){d=!0;return}c=!0,[u,r]=[r,u],u.forEach(p),u.clear(),c=!1,d&&(d=!1,m.process(v))}};return m}const R1=40;function Ty(a,l){let u=!1,r=!0;const c={delta:0,timestamp:0,isProcessing:!1},d=()=>u=!0,h=Hs.reduce((H,et)=>(H[et]=_1(d),H),{}),{setup:g,read:p,resolveKeyframes:m,preUpdate:v,update:b,preRender:E,render:j,postRender:B}=h,q=()=>{const H=fn.useManualTiming?c.timestamp:performance.now();u=!1,fn.useManualTiming||(c.delta=r?1e3/60:Math.max(Math.min(H-c.timestamp,R1),1)),c.timestamp=H,c.isProcessing=!0,g.process(c),p.process(c),m.process(c),v.process(c),b.process(c),E.process(c),j.process(c),B.process(c),c.isProcessing=!1,u&&l&&(r=!1,a(q))},X=()=>{u=!0,r=!0,c.isProcessing||a(q)};return{schedule:Hs.reduce((H,et)=>{const L=h[et];return H[et]=(W,nt=!1,J=!1)=>(u||X(),L.schedule(W,nt,J)),H},{}),cancel:H=>{for(let et=0;et<Hs.length;et++)h[Hs[et]].cancel(H)},state:c,steps:h}}const{schedule:Dt,cancel:Ln,state:Wt,steps:Ro}=Ty(typeof requestAnimationFrame<"u"?requestAnimationFrame:Ne,!0);let Xs;function O1(){Xs=void 0}const oe={now:()=>(Xs===void 0&&oe.set(Wt.isProcessing||fn.useManualTiming?Wt.timestamp:performance.now()),Xs),set:a=>{Xs=a,queueMicrotask(O1)}},xy=a=>l=>typeof l=="string"&&l.startsWith(a),Tc=xy("--"),C1=xy("var(--"),xc=a=>C1(a)?N1.test(a.split("/*")[0].trim()):!1,N1=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,li={test:a=>typeof a=="number",parse:parseFloat,transform:a=>a},pl={...li,transform:a=>cn(0,1,a)},qs={...li,default:1},ol=a=>Math.round(a*1e5)/1e5,Ac=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function j1(a){return a==null}const V1=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Ec=(a,l)=>u=>!!(typeof u=="string"&&V1.test(u)&&u.startsWith(a)||l&&!j1(u)&&Object.prototype.hasOwnProperty.call(u,l)),Ay=(a,l,u)=>r=>{if(typeof r!="string")return r;const[c,d,h,g]=r.match(Ac);return{[a]:parseFloat(c),[l]:parseFloat(d),[u]:parseFloat(h),alpha:g!==void 0?parseFloat(g):1}},z1=a=>cn(0,255,a),Oo={...li,transform:a=>Math.round(z1(a))},ua={test:Ec("rgb","red"),parse:Ay("red","green","blue"),transform:({red:a,green:l,blue:u,alpha:r=1})=>"rgba("+Oo.transform(a)+", "+Oo.transform(l)+", "+Oo.transform(u)+", "+ol(pl.transform(r))+")"};function U1(a){let l="",u="",r="",c="";return a.length>5?(l=a.substring(1,3),u=a.substring(3,5),r=a.substring(5,7),c=a.substring(7,9)):(l=a.substring(1,2),u=a.substring(2,3),r=a.substring(3,4),c=a.substring(4,5),l+=l,u+=u,r+=r,c+=c),{red:parseInt(l,16),green:parseInt(u,16),blue:parseInt(r,16),alpha:c?parseInt(c,16)/255:1}}const Xo={test:Ec("#"),parse:U1,transform:ua.transform},xl=a=>({test:l=>typeof l=="string"&&l.endsWith(a)&&l.split(" ").length===1,parse:parseFloat,transform:l=>`${l}${a}`}),wn=xl("deg"),Qe=xl("%"),it=xl("px"),B1=xl("vh"),w1=xl("vw"),Jm={...Qe,parse:a=>Qe.parse(a)/100,transform:a=>Qe.transform(a*100)},Wa={test:Ec("hsl","hue"),parse:Ay("hue","saturation","lightness"),transform:({hue:a,saturation:l,lightness:u,alpha:r=1})=>"hsla("+Math.round(a)+", "+Qe.transform(ol(l))+", "+Qe.transform(ol(u))+", "+ol(pl.transform(r))+")"},Lt={test:a=>ua.test(a)||Xo.test(a)||Wa.test(a),parse:a=>ua.test(a)?ua.parse(a):Wa.test(a)?Wa.parse(a):Xo.parse(a),transform:a=>typeof a=="string"?a:a.hasOwnProperty("red")?ua.transform(a):Wa.transform(a),getAnimatableNone:a=>{const l=Lt.parse(a);return l.alpha=0,Lt.transform(l)}},L1=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function H1(a){return isNaN(a)&&typeof a=="string"&&(a.match(Ac)?.length||0)+(a.match(L1)?.length||0)>0}const Ey="number",My="color",q1="var",Y1="var(",Fm="${}",G1=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function yl(a){const l=a.toString(),u=[],r={color:[],number:[],var:[]},c=[];let d=0;const g=l.replace(G1,p=>(Lt.test(p)?(r.color.push(d),c.push(My),u.push(Lt.parse(p))):p.startsWith(Y1)?(r.var.push(d),c.push(q1),u.push(p)):(r.number.push(d),c.push(Ey),u.push(parseFloat(p))),++d,Fm)).split(Fm);return{values:u,split:g,indexes:r,types:c}}function Dy(a){return yl(a).values}function _y(a){const{split:l,types:u}=yl(a),r=l.length;return c=>{let d="";for(let h=0;h<r;h++)if(d+=l[h],c[h]!==void 0){const g=u[h];g===Ey?d+=ol(c[h]):g===My?d+=Lt.transform(c[h]):d+=c[h]}return d}}const X1=a=>typeof a=="number"?0:Lt.test(a)?Lt.getAnimatableNone(a):a;function Z1(a){const l=Dy(a);return _y(a)(l.map(X1))}const Hn={test:H1,parse:Dy,createTransformer:_y,getAnimatableNone:Z1};function Co(a,l,u){return u<0&&(u+=1),u>1&&(u-=1),u<1/6?a+(l-a)*6*u:u<1/2?l:u<2/3?a+(l-a)*(2/3-u)*6:a}function Q1({hue:a,saturation:l,lightness:u,alpha:r}){a/=360,l/=100,u/=100;let c=0,d=0,h=0;if(!l)c=d=h=u;else{const g=u<.5?u*(1+l):u+l-u*l,p=2*u-g;c=Co(p,g,a+1/3),d=Co(p,g,a),h=Co(p,g,a-1/3)}return{red:Math.round(c*255),green:Math.round(d*255),blue:Math.round(h*255),alpha:r}}function Ps(a,l){return u=>u>0?l:a}const Rt=(a,l,u)=>a+(l-a)*u,No=(a,l,u)=>{const r=a*a,c=u*(l*l-r)+r;return c<0?0:Math.sqrt(c)},K1=[Xo,ua,Wa],k1=a=>K1.find(l=>l.test(a));function Wm(a){const l=k1(a);if(!l)return!1;let u=l.parse(a);return l===Wa&&(u=Q1(u)),u}const $m=(a,l)=>{const u=Wm(a),r=Wm(l);if(!u||!r)return Ps(a,l);const c={...u};return d=>(c.red=No(u.red,r.red,d),c.green=No(u.green,r.green,d),c.blue=No(u.blue,r.blue,d),c.alpha=Rt(u.alpha,r.alpha,d),ua.transform(c))},Zo=new Set(["none","hidden"]);function P1(a,l){return Zo.has(a)?u=>u<=0?a:l:u=>u>=1?l:a}function J1(a,l){return u=>Rt(a,l,u)}function Mc(a){return typeof a=="number"?J1:typeof a=="string"?xc(a)?Ps:Lt.test(a)?$m:$1:Array.isArray(a)?Ry:typeof a=="object"?Lt.test(a)?$m:F1:Ps}function Ry(a,l){const u=[...a],r=u.length,c=a.map((d,h)=>Mc(d)(d,l[h]));return d=>{for(let h=0;h<r;h++)u[h]=c[h](d);return u}}function F1(a,l){const u={...a,...l},r={};for(const c in u)a[c]!==void 0&&l[c]!==void 0&&(r[c]=Mc(a[c])(a[c],l[c]));return c=>{for(const d in r)u[d]=r[d](c);return u}}function W1(a,l){const u=[],r={color:0,var:0,number:0};for(let c=0;c<l.values.length;c++){const d=l.types[c],h=a.indexes[d][r[d]],g=a.values[h]??0;u[c]=g,r[d]++}return u}const $1=(a,l)=>{const u=Hn.createTransformer(l),r=yl(a),c=yl(l);return r.indexes.var.length===c.indexes.var.length&&r.indexes.color.length===c.indexes.color.length&&r.indexes.number.length>=c.indexes.number.length?Zo.has(a)&&!c.values.length||Zo.has(l)&&!r.values.length?P1(a,l):bl(Ry(W1(r,c),c.values),u):Ps(a,l)};function Oy(a,l,u){return typeof a=="number"&&typeof l=="number"&&typeof u=="number"?Rt(a,l,u):Mc(a)(a,l)}const I1=a=>{const l=({timestamp:u})=>a(u);return{start:(u=!0)=>Dt.update(l,u),stop:()=>Ln(l),now:()=>Wt.isProcessing?Wt.timestamp:oe.now()}},Cy=(a,l,u=10)=>{let r="";const c=Math.max(Math.round(l/u),2);for(let d=0;d<c;d++)r+=Math.round(a(d/(c-1))*1e4)/1e4+", ";return`linear(${r.substring(0,r.length-2)})`},Js=2e4;function Dc(a){let l=0;const u=50;let r=a.next(l);for(;!r.done&&l<Js;)l+=u,r=a.next(l);return l>=Js?1/0:l}function tS(a,l=100,u){const r=u({...a,keyframes:[0,l]}),c=Math.min(Dc(r),Js);return{type:"keyframes",ease:d=>r.next(c*d).value/l,duration:Ze(c)}}const eS=5;function Ny(a,l,u){const r=Math.max(l-eS,0);return cy(u-a(r),l-r)}const jt={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},jo=.001;function nS({duration:a=jt.duration,bounce:l=jt.bounce,velocity:u=jt.velocity,mass:r=jt.mass}){let c,d,h=1-l;h=cn(jt.minDamping,jt.maxDamping,h),a=cn(jt.minDuration,jt.maxDuration,Ze(a)),h<1?(c=m=>{const v=m*h,b=v*a,E=v-u,j=Qo(m,h),B=Math.exp(-b);return jo-E/j*B},d=m=>{const b=m*h*a,E=b*u+u,j=Math.pow(h,2)*Math.pow(m,2)*a,B=Math.exp(-b),q=Qo(Math.pow(m,2),h);return(-c(m)+jo>0?-1:1)*((E-j)*B)/q}):(c=m=>{const v=Math.exp(-m*a),b=(m-u)*a+1;return-jo+v*b},d=m=>{const v=Math.exp(-m*a),b=(u-m)*(a*a);return v*b});const g=5/a,p=iS(c,d,g);if(a=Xe(a),isNaN(p))return{stiffness:jt.stiffness,damping:jt.damping,duration:a};{const m=Math.pow(p,2)*r;return{stiffness:m,damping:h*2*Math.sqrt(r*m),duration:a}}}const aS=12;function iS(a,l,u){let r=u;for(let c=1;c<aS;c++)r=r-a(r)/l(r);return r}function Qo(a,l){return a*Math.sqrt(1-l*l)}const lS=["duration","bounce"],sS=["stiffness","damping","mass"];function Im(a,l){return l.some(u=>a[u]!==void 0)}function uS(a){let l={velocity:jt.velocity,stiffness:jt.stiffness,damping:jt.damping,mass:jt.mass,isResolvedFromDuration:!1,...a};if(!Im(a,sS)&&Im(a,lS))if(a.visualDuration){const u=a.visualDuration,r=2*Math.PI/(u*1.2),c=r*r,d=2*cn(.05,1,1-(a.bounce||0))*Math.sqrt(c);l={...l,mass:jt.mass,stiffness:c,damping:d}}else{const u=nS(a);l={...l,...u,mass:jt.mass},l.isResolvedFromDuration=!0}return l}function Fs(a=jt.visualDuration,l=jt.bounce){const u=typeof a!="object"?{visualDuration:a,keyframes:[0,1],bounce:l}:a;let{restSpeed:r,restDelta:c}=u;const d=u.keyframes[0],h=u.keyframes[u.keyframes.length-1],g={done:!1,value:d},{stiffness:p,damping:m,mass:v,duration:b,velocity:E,isResolvedFromDuration:j}=uS({...u,velocity:-Ze(u.velocity||0)}),B=E||0,q=m/(2*Math.sqrt(p*v)),X=h-d,Y=Ze(Math.sqrt(p/v)),Z=Math.abs(X)<5;r||(r=Z?jt.restSpeed.granular:jt.restSpeed.default),c||(c=Z?jt.restDelta.granular:jt.restDelta.default);let H;if(q<1){const L=Qo(Y,q);H=W=>{const nt=Math.exp(-q*Y*W);return h-nt*((B+q*Y*X)/L*Math.sin(L*W)+X*Math.cos(L*W))}}else if(q===1)H=L=>h-Math.exp(-Y*L)*(X+(B+Y*X)*L);else{const L=Y*Math.sqrt(q*q-1);H=W=>{const nt=Math.exp(-q*Y*W),J=Math.min(L*W,300);return h-nt*((B+q*Y*X)*Math.sinh(J)+L*X*Math.cosh(J))/L}}const et={calculatedDuration:j&&b||null,next:L=>{const W=H(L);if(j)g.done=L>=b;else{let nt=L===0?B:0;q<1&&(nt=L===0?Xe(B):Ny(H,L,W));const J=Math.abs(nt)<=r,yt=Math.abs(h-W)<=c;g.done=J&&yt}return g.value=g.done?h:W,g},toString:()=>{const L=Math.min(Dc(et),Js),W=Cy(nt=>et.next(L*nt).value,L,30);return L+"ms "+W},toTransition:()=>{}};return et}Fs.applyToOptions=a=>{const l=tS(a,100,Fs);return a.ease=l.ease,a.duration=Xe(l.duration),a.type="keyframes",a};function Ko({keyframes:a,velocity:l=0,power:u=.8,timeConstant:r=325,bounceDamping:c=10,bounceStiffness:d=500,modifyTarget:h,min:g,max:p,restDelta:m=.5,restSpeed:v}){const b=a[0],E={done:!1,value:b},j=J=>g!==void 0&&J<g||p!==void 0&&J>p,B=J=>g===void 0?p:p===void 0||Math.abs(g-J)<Math.abs(p-J)?g:p;let q=u*l;const X=b+q,Y=h===void 0?X:h(X);Y!==X&&(q=Y-b);const Z=J=>-q*Math.exp(-J/r),H=J=>Y+Z(J),et=J=>{const yt=Z(J),Ot=H(J);E.done=Math.abs(yt)<=m,E.value=E.done?Y:Ot};let L,W;const nt=J=>{j(E.value)&&(L=J,W=Fs({keyframes:[E.value,B(E.value)],velocity:Ny(H,J,E.value),damping:c,stiffness:d,restDelta:m,restSpeed:v}))};return nt(0),{calculatedDuration:null,next:J=>{let yt=!1;return!W&&L===void 0&&(yt=!0,et(J),nt(J)),L!==void 0&&J>=L?W.next(J-L):(!yt&&et(J),E)}}}function rS(a,l,u){const r=[],c=u||fn.mix||Oy,d=a.length-1;for(let h=0;h<d;h++){let g=c(a[h],a[h+1]);if(l){const p=Array.isArray(l)?l[h]||Ne:l;g=bl(p,g)}r.push(g)}return r}function oS(a,l,{clamp:u=!0,ease:r,mixer:c}={}){const d=a.length;if(yc(d===l.length),d===1)return()=>l[0];if(d===2&&l[0]===l[1])return()=>l[1];const h=a[0]===a[1];a[0]>a[d-1]&&(a=[...a].reverse(),l=[...l].reverse());const g=rS(l,r,c),p=g.length,m=v=>{if(h&&v<a[0])return l[0];let b=0;if(p>1)for(;b<a.length-2&&!(v<a[b+1]);b++);const E=ml(a[b],a[b+1],v);return g[b](E)};return u?v=>m(cn(a[0],a[d-1],v)):m}function cS(a,l){const u=a[a.length-1];for(let r=1;r<=l;r++){const c=ml(0,l,r);a.push(Rt(u,1,c))}}function fS(a){const l=[0];return cS(l,a.length-1),l}function hS(a,l){return a.map(u=>u*l)}function dS(a,l){return a.map(()=>l||Sy).splice(0,a.length-1)}function cl({duration:a=300,keyframes:l,times:u,ease:r="easeInOut"}){const c=E1(r)?r.map(Pm):Pm(r),d={done:!1,value:l[0]},h=hS(u&&u.length===l.length?u:fS(l),a),g=oS(h,l,{ease:Array.isArray(c)?c:dS(l,c)});return{calculatedDuration:a,next:p=>(d.value=g(p),d.done=p>=a,d)}}const mS=a=>a!==null;function _c(a,{repeat:l,repeatType:u="loop"},r,c=1){const d=a.filter(mS),g=c<0||l&&u!=="loop"&&l%2===1?0:d.length-1;return!g||r===void 0?d[g]:r}const pS={decay:Ko,inertia:Ko,tween:cl,keyframes:cl,spring:Fs};function jy(a){typeof a.type=="string"&&(a.type=pS[a.type])}class Rc{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(l=>{this.resolve=l})}notifyFinished(){this.resolve()}then(l,u){return this.finished.then(l,u)}}const yS=a=>a/100;class Oc extends Rc{constructor(l){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{const{motionValue:u}=this.options;u&&u.updatedAt!==oe.now()&&this.tick(oe.now()),this.isStopped=!0,this.state!=="idle"&&(this.teardown(),this.options.onStop?.())},this.options=l,this.initAnimation(),this.play(),l.autoplay===!1&&this.pause()}initAnimation(){const{options:l}=this;jy(l);const{type:u=cl,repeat:r=0,repeatDelay:c=0,repeatType:d,velocity:h=0}=l;let{keyframes:g}=l;const p=u||cl;p!==cl&&typeof g[0]!="number"&&(this.mixKeyframes=bl(yS,Oy(g[0],g[1])),g=[0,100]);const m=p({...l,keyframes:g});d==="mirror"&&(this.mirroredGenerator=p({...l,keyframes:[...g].reverse(),velocity:-h})),m.calculatedDuration===null&&(m.calculatedDuration=Dc(m));const{calculatedDuration:v}=m;this.calculatedDuration=v,this.resolvedDuration=v+c,this.totalDuration=this.resolvedDuration*(r+1)-c,this.generator=m}updateTime(l){const u=Math.round(l-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=u}tick(l,u=!1){const{generator:r,totalDuration:c,mixKeyframes:d,mirroredGenerator:h,resolvedDuration:g,calculatedDuration:p}=this;if(this.startTime===null)return r.next(0);const{delay:m=0,keyframes:v,repeat:b,repeatType:E,repeatDelay:j,type:B,onUpdate:q,finalKeyframe:X}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,l):this.speed<0&&(this.startTime=Math.min(l-c/this.speed,this.startTime)),u?this.currentTime=l:this.updateTime(l);const Y=this.currentTime-m*(this.playbackSpeed>=0?1:-1),Z=this.playbackSpeed>=0?Y<0:Y>c;this.currentTime=Math.max(Y,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let H=this.currentTime,et=r;if(b){const J=Math.min(this.currentTime,c)/g;let yt=Math.floor(J),Ot=J%1;!Ot&&J>=1&&(Ot=1),Ot===1&&yt--,yt=Math.min(yt,b+1),!!(yt%2)&&(E==="reverse"?(Ot=1-Ot,j&&(Ot-=j/g)):E==="mirror"&&(et=h)),H=cn(0,1,Ot)*g}const L=Z?{done:!1,value:v[0]}:et.next(H);d&&(L.value=d(L.value));let{done:W}=L;!Z&&p!==null&&(W=this.playbackSpeed>=0?this.currentTime>=c:this.currentTime<=0);const nt=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&W);return nt&&B!==Ko&&(L.value=_c(v,this.options,X,this.speed)),q&&q(L.value),nt&&this.finish(),L}then(l,u){return this.finished.then(l,u)}get duration(){return Ze(this.calculatedDuration)}get time(){return Ze(this.currentTime)}set time(l){l=Xe(l),this.currentTime=l,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=l:this.driver&&(this.startTime=this.driver.now()-l/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(l){this.updateTime(oe.now());const u=this.playbackSpeed!==l;this.playbackSpeed=l,u&&(this.time=Ze(this.currentTime))}play(){if(this.isStopped)return;const{driver:l=I1,startTime:u}=this.options;this.driver||(this.driver=l(c=>this.tick(c))),this.options.onPlay?.();const r=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=r):this.holdTime!==null?this.startTime=r-this.holdTime:this.startTime||(this.startTime=u??r),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(oe.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(l){return this.startTime=0,this.tick(l,!0)}attachTimeline(l){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),l.observe(this)}}function gS(a){for(let l=1;l<a.length;l++)a[l]??(a[l]=a[l-1])}const ra=a=>a*180/Math.PI,ko=a=>{const l=ra(Math.atan2(a[1],a[0]));return Po(l)},vS={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:a=>(Math.abs(a[0])+Math.abs(a[3]))/2,rotate:ko,rotateZ:ko,skewX:a=>ra(Math.atan(a[1])),skewY:a=>ra(Math.atan(a[2])),skew:a=>(Math.abs(a[1])+Math.abs(a[2]))/2},Po=a=>(a=a%360,a<0&&(a+=360),a),tp=ko,ep=a=>Math.sqrt(a[0]*a[0]+a[1]*a[1]),np=a=>Math.sqrt(a[4]*a[4]+a[5]*a[5]),SS={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ep,scaleY:np,scale:a=>(ep(a)+np(a))/2,rotateX:a=>Po(ra(Math.atan2(a[6],a[5]))),rotateY:a=>Po(ra(Math.atan2(-a[2],a[0]))),rotateZ:tp,rotate:tp,skewX:a=>ra(Math.atan(a[4])),skewY:a=>ra(Math.atan(a[1])),skew:a=>(Math.abs(a[1])+Math.abs(a[4]))/2};function Jo(a){return a.includes("scale")?1:0}function Fo(a,l){if(!a||a==="none")return Jo(l);const u=a.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let r,c;if(u)r=SS,c=u;else{const g=a.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=vS,c=g}if(!c)return Jo(l);const d=r[l],h=c[1].split(",").map(TS);return typeof d=="function"?d(h):h[d]}const bS=(a,l)=>{const{transform:u="none"}=getComputedStyle(a);return Fo(u,l)};function TS(a){return parseFloat(a.trim())}const si=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],ui=new Set(si),ap=a=>a===li||a===it,xS=new Set(["x","y","z"]),AS=si.filter(a=>!xS.has(a));function ES(a){const l=[];return AS.forEach(u=>{const r=a.getValue(u);r!==void 0&&(l.push([u,r.get()]),r.set(u.startsWith("scale")?1:0))}),l}const ca={width:({x:a},{paddingLeft:l="0",paddingRight:u="0"})=>a.max-a.min-parseFloat(l)-parseFloat(u),height:({y:a},{paddingTop:l="0",paddingBottom:u="0"})=>a.max-a.min-parseFloat(l)-parseFloat(u),top:(a,{top:l})=>parseFloat(l),left:(a,{left:l})=>parseFloat(l),bottom:({y:a},{top:l})=>parseFloat(l)+(a.max-a.min),right:({x:a},{left:l})=>parseFloat(l)+(a.max-a.min),x:(a,{transform:l})=>Fo(l,"x"),y:(a,{transform:l})=>Fo(l,"y")};ca.translateX=ca.x;ca.translateY=ca.y;const fa=new Set;let Wo=!1,$o=!1,Io=!1;function Vy(){if($o){const a=Array.from(fa).filter(r=>r.needsMeasurement),l=new Set(a.map(r=>r.element)),u=new Map;l.forEach(r=>{const c=ES(r);c.length&&(u.set(r,c),r.render())}),a.forEach(r=>r.measureInitialState()),l.forEach(r=>{r.render();const c=u.get(r);c&&c.forEach(([d,h])=>{r.getValue(d)?.set(h)})}),a.forEach(r=>r.measureEndState()),a.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}$o=!1,Wo=!1,fa.forEach(a=>a.complete(Io)),fa.clear()}function zy(){fa.forEach(a=>{a.readKeyframes(),a.needsMeasurement&&($o=!0)})}function MS(){Io=!0,zy(),Vy(),Io=!1}class Cc{constructor(l,u,r,c,d,h=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...l],this.onComplete=u,this.name=r,this.motionValue=c,this.element=d,this.isAsync=h}scheduleResolve(){this.state="scheduled",this.isAsync?(fa.add(this),Wo||(Wo=!0,Dt.read(zy),Dt.resolveKeyframes(Vy))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:l,name:u,element:r,motionValue:c}=this;if(l[0]===null){const d=c?.get(),h=l[l.length-1];if(d!==void 0)l[0]=d;else if(r&&u){const g=r.readValue(u,h);g!=null&&(l[0]=g)}l[0]===void 0&&(l[0]=h),c&&d===void 0&&c.set(l[0])}gS(l)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(l=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,l),fa.delete(this)}cancel(){this.state==="scheduled"&&(fa.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const DS=a=>a.startsWith("--");function _S(a,l,u){DS(l)?a.style.setProperty(l,u):a.style[l]=u}const RS=gc(()=>window.ScrollTimeline!==void 0),OS={};function CS(a,l){const u=gc(a);return()=>OS[l]??u()}const Uy=CS(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),rl=([a,l,u,r])=>`cubic-bezier(${a}, ${l}, ${u}, ${r})`,ip={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:rl([0,.65,.55,1]),circOut:rl([.55,0,1,.45]),backIn:rl([.31,.01,.66,-.59]),backOut:rl([.33,1.53,.69,.99])};function By(a,l){if(a)return typeof a=="function"?Uy()?Cy(a,l):"ease-out":by(a)?rl(a):Array.isArray(a)?a.map(u=>By(u,l)||ip.easeOut):ip[a]}function NS(a,l,u,{delay:r=0,duration:c=300,repeat:d=0,repeatType:h="loop",ease:g="easeOut",times:p}={},m=void 0){const v={[l]:u};p&&(v.offset=p);const b=By(g,c);Array.isArray(b)&&(v.easing=b);const E={delay:r,duration:c,easing:Array.isArray(b)?"linear":b,fill:"both",iterations:d+1,direction:h==="reverse"?"alternate":"normal"};return m&&(E.pseudoElement=m),a.animate(v,E)}function wy(a){return typeof a=="function"&&"applyToOptions"in a}function jS({type:a,...l}){return wy(a)&&Uy()?a.applyToOptions(l):(l.duration??(l.duration=300),l.ease??(l.ease="easeOut"),l)}class VS extends Rc{constructor(l){if(super(),this.finishedTime=null,this.isStopped=!1,!l)return;const{element:u,name:r,keyframes:c,pseudoElement:d,allowFlatten:h=!1,finalKeyframe:g,onComplete:p}=l;this.isPseudoElement=!!d,this.allowFlatten=h,this.options=l,yc(typeof l.type!="string");const m=jS(l);this.animation=NS(u,r,c,m,d),m.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!d){const v=_c(c,this.options,g,this.speed);this.updateMotionValue?this.updateMotionValue(v):_S(u,r,v),this.animation.cancel()}p?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:l}=this;l==="idle"||l==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){const l=this.animation.effect?.getComputedTiming?.().duration||0;return Ze(Number(l))}get time(){return Ze(Number(this.animation.currentTime)||0)}set time(l){this.finishedTime=null,this.animation.currentTime=Xe(l)}get speed(){return this.animation.playbackRate}set speed(l){l<0&&(this.finishedTime=null),this.animation.playbackRate=l}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(l){this.animation.startTime=l}attachTimeline({timeline:l,observe:u}){return this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,l&&RS()?(this.animation.timeline=l,Ne):u(this)}}const Ly={anticipate:yy,backInOut:py,circInOut:vy};function zS(a){return a in Ly}function US(a){typeof a.ease=="string"&&zS(a.ease)&&(a.ease=Ly[a.ease])}const lp=10;class BS extends VS{constructor(l){US(l),jy(l),super(l),l.startTime&&(this.startTime=l.startTime),this.options=l}updateMotionValue(l){const{motionValue:u,onUpdate:r,onComplete:c,element:d,...h}=this.options;if(!u)return;if(l!==void 0){u.set(l);return}const g=new Oc({...h,autoplay:!1}),p=Xe(this.finishedTime??this.time);u.setWithVelocity(g.sample(p-lp).value,g.sample(p).value,lp),g.stop()}}const sp=(a,l)=>l==="zIndex"?!1:!!(typeof a=="number"||Array.isArray(a)||typeof a=="string"&&(Hn.test(a)||a==="0")&&!a.startsWith("url("));function wS(a){const l=a[0];if(a.length===1)return!0;for(let u=0;u<a.length;u++)if(a[u]!==l)return!0}function LS(a,l,u,r){const c=a[0];if(c===null)return!1;if(l==="display"||l==="visibility")return!0;const d=a[a.length-1],h=sp(c,l),g=sp(d,l);return!h||!g?!1:wS(a)||(u==="spring"||wy(u))&&r}function tc(a){a.duration=0,a.type}const HS=new Set(["opacity","clipPath","filter","transform"]),qS=gc(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function YS(a){const{motionValue:l,name:u,repeatDelay:r,repeatType:c,damping:d,type:h}=a;if(!(l?.owner?.current instanceof HTMLElement))return!1;const{onUpdate:p,transformTemplate:m}=l.owner.getProps();return qS()&&u&&HS.has(u)&&(u!=="transform"||!m)&&!p&&!r&&c!=="mirror"&&d!==0&&h!=="inertia"}const GS=40;class XS extends Rc{constructor({autoplay:l=!0,delay:u=0,type:r="keyframes",repeat:c=0,repeatDelay:d=0,repeatType:h="loop",keyframes:g,name:p,motionValue:m,element:v,...b}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=oe.now();const E={autoplay:l,delay:u,type:r,repeat:c,repeatDelay:d,repeatType:h,name:p,motionValue:m,element:v,...b},j=v?.KeyframeResolver||Cc;this.keyframeResolver=new j(g,(B,q,X)=>this.onKeyframesResolved(B,q,E,!X),p,m,v),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(l,u,r,c){this.keyframeResolver=void 0;const{name:d,type:h,velocity:g,delay:p,isHandoff:m,onUpdate:v}=r;this.resolvedAt=oe.now(),LS(l,d,h,g)||((fn.instantAnimations||!p)&&v?.(_c(l,r,u)),l[0]=l[l.length-1],tc(r),r.repeat=0);const E={startTime:c?this.resolvedAt?this.resolvedAt-this.createdAt>GS?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:u,...r,keyframes:l},j=!m&&YS(E)?new BS({...E,element:E.motionValue.owner.current}):new Oc(E);j.finished.then(()=>this.notifyFinished()).catch(Ne),this.pendingTimeline&&(this.stopTimeline=j.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=j}get finished(){return this._animation?this.animation.finished:this._finished}then(l,u){return this.finished.finally(l).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),MS()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(l){this.animation.time=l}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(l){this.animation.speed=l}get startTime(){return this.animation.startTime}attachTimeline(l){return this._animation?this.stopTimeline=this.animation.attachTimeline(l):this.pendingTimeline=l,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}const ZS=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function QS(a){const l=ZS.exec(a);if(!l)return[,];const[,u,r,c]=l;return[`--${u??r}`,c]}function Hy(a,l,u=1){const[r,c]=QS(a);if(!r)return;const d=window.getComputedStyle(l).getPropertyValue(r);if(d){const h=d.trim();return uy(h)?parseFloat(h):h}return xc(c)?Hy(c,l,u+1):c}function Nc(a,l){return a?.[l]??a?.default??a}const qy=new Set(["width","height","top","left","right","bottom",...si]),KS={test:a=>a==="auto",parse:a=>a},Yy=a=>l=>l.test(a),Gy=[li,it,Qe,wn,w1,B1,KS],up=a=>Gy.find(Yy(a));function kS(a){return typeof a=="number"?a===0:a!==null?a==="none"||a==="0"||oy(a):!0}const PS=new Set(["brightness","contrast","saturate","opacity"]);function JS(a){const[l,u]=a.slice(0,-1).split("(");if(l==="drop-shadow")return a;const[r]=u.match(Ac)||[];if(!r)return a;const c=u.replace(r,"");let d=PS.has(l)?1:0;return r!==u&&(d*=100),l+"("+d+c+")"}const FS=/\b([a-z-]*)\(.*?\)/gu,ec={...Hn,getAnimatableNone:a=>{const l=a.match(FS);return l?l.map(JS).join(" "):a}},rp={...li,transform:Math.round},WS={rotate:wn,rotateX:wn,rotateY:wn,rotateZ:wn,scale:qs,scaleX:qs,scaleY:qs,scaleZ:qs,skew:wn,skewX:wn,skewY:wn,distance:it,translateX:it,translateY:it,translateZ:it,x:it,y:it,z:it,perspective:it,transformPerspective:it,opacity:pl,originX:Jm,originY:Jm,originZ:it},jc={borderWidth:it,borderTopWidth:it,borderRightWidth:it,borderBottomWidth:it,borderLeftWidth:it,borderRadius:it,radius:it,borderTopLeftRadius:it,borderTopRightRadius:it,borderBottomRightRadius:it,borderBottomLeftRadius:it,width:it,maxWidth:it,height:it,maxHeight:it,top:it,right:it,bottom:it,left:it,padding:it,paddingTop:it,paddingRight:it,paddingBottom:it,paddingLeft:it,margin:it,marginTop:it,marginRight:it,marginBottom:it,marginLeft:it,backgroundPositionX:it,backgroundPositionY:it,...WS,zIndex:rp,fillOpacity:pl,strokeOpacity:pl,numOctaves:rp},$S={...jc,color:Lt,backgroundColor:Lt,outlineColor:Lt,fill:Lt,stroke:Lt,borderColor:Lt,borderTopColor:Lt,borderRightColor:Lt,borderBottomColor:Lt,borderLeftColor:Lt,filter:ec,WebkitFilter:ec},Xy=a=>$S[a];function Zy(a,l){let u=Xy(a);return u!==ec&&(u=Hn),u.getAnimatableNone?u.getAnimatableNone(l):void 0}const IS=new Set(["auto","none","0"]);function tb(a,l,u){let r=0,c;for(;r<a.length&&!c;){const d=a[r];typeof d=="string"&&!IS.has(d)&&yl(d).values.length&&(c=a[r]),r++}if(c&&u)for(const d of l)a[d]=Zy(u,c)}class eb extends Cc{constructor(l,u,r,c,d){super(l,u,r,c,d,!0)}readKeyframes(){const{unresolvedKeyframes:l,element:u,name:r}=this;if(!u||!u.current)return;super.readKeyframes();for(let p=0;p<l.length;p++){let m=l[p];if(typeof m=="string"&&(m=m.trim(),xc(m))){const v=Hy(m,u.current);v!==void 0&&(l[p]=v),p===l.length-1&&(this.finalKeyframe=m)}}if(this.resolveNoneKeyframes(),!qy.has(r)||l.length!==2)return;const[c,d]=l,h=up(c),g=up(d);if(h!==g)if(ap(h)&&ap(g))for(let p=0;p<l.length;p++){const m=l[p];typeof m=="string"&&(l[p]=parseFloat(m))}else ca[r]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:l,name:u}=this,r=[];for(let c=0;c<l.length;c++)(l[c]===null||kS(l[c]))&&r.push(c);r.length&&tb(l,r,u)}measureInitialState(){const{element:l,unresolvedKeyframes:u,name:r}=this;if(!l||!l.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ca[r](l.measureViewportBox(),window.getComputedStyle(l.current)),u[0]=this.measuredOrigin;const c=u[u.length-1];c!==void 0&&l.getValue(r,c).jump(c,!1)}measureEndState(){const{element:l,name:u,unresolvedKeyframes:r}=this;if(!l||!l.current)return;const c=l.getValue(u);c&&c.jump(this.measuredOrigin,!1);const d=r.length-1,h=r[d];r[d]=ca[u](l.measureViewportBox(),window.getComputedStyle(l.current)),h!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=h),this.removedTransforms?.length&&this.removedTransforms.forEach(([g,p])=>{l.getValue(g).set(p)}),this.resolveNoneKeyframes()}}function nb(a,l,u){if(a instanceof EventTarget)return[a];if(typeof a=="string"){let r=document;const c=u?.[a]??r.querySelectorAll(a);return c?Array.from(c):[]}return Array.from(a)}const Qy=(a,l)=>l&&typeof a=="number"?l.transform(a):a;function Ky(a){return ry(a)&&"offsetHeight"in a}const op=30,ab=a=>!isNaN(parseFloat(a));class ib{constructor(l,u={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=r=>{const c=oe.now();if(this.updatedAt!==c&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(const d of this.dependents)d.dirty()},this.hasAnimated=!1,this.setCurrent(l),this.owner=u.owner}setCurrent(l){this.current=l,this.updatedAt=oe.now(),this.canTrackVelocity===null&&l!==void 0&&(this.canTrackVelocity=ab(this.current))}setPrevFrameValue(l=this.current){this.prevFrameValue=l,this.prevUpdatedAt=this.updatedAt}onChange(l){return this.on("change",l)}on(l,u){this.events[l]||(this.events[l]=new vc);const r=this.events[l].add(u);return l==="change"?()=>{r(),Dt.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const l in this.events)this.events[l].clear()}attach(l,u){this.passiveEffect=l,this.stopPassiveEffect=u}set(l){this.passiveEffect?this.passiveEffect(l,this.updateAndNotify):this.updateAndNotify(l)}setWithVelocity(l,u,r){this.set(u),this.prev=void 0,this.prevFrameValue=l,this.prevUpdatedAt=this.updatedAt-r}jump(l,u=!0){this.updateAndNotify(l),this.prev=l,this.prevUpdatedAt=this.prevFrameValue=void 0,u&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(l){this.dependents||(this.dependents=new Set),this.dependents.add(l)}removeDependent(l){this.dependents&&this.dependents.delete(l)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const l=oe.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||l-this.updatedAt>op)return 0;const u=Math.min(this.updatedAt-this.prevUpdatedAt,op);return cy(parseFloat(this.current)-parseFloat(this.prevFrameValue),u)}start(l){return this.stop(),new Promise(u=>{this.hasAnimated=!0,this.animation=l(u),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ai(a,l){return new ib(a,l)}const{schedule:Vc}=Ty(queueMicrotask,!1),Ue={x:!1,y:!1};function ky(){return Ue.x||Ue.y}function lb(a){return a==="x"||a==="y"?Ue[a]?null:(Ue[a]=!0,()=>{Ue[a]=!1}):Ue.x||Ue.y?null:(Ue.x=Ue.y=!0,()=>{Ue.x=Ue.y=!1})}function Py(a,l){const u=nb(a),r=new AbortController,c={passive:!0,...l,signal:r.signal};return[u,c,()=>r.abort()]}function cp(a){return!(a.pointerType==="touch"||ky())}function sb(a,l,u={}){const[r,c,d]=Py(a,u),h=g=>{if(!cp(g))return;const{target:p}=g,m=l(p,g);if(typeof m!="function"||!p)return;const v=b=>{cp(b)&&(m(b),p.removeEventListener("pointerleave",v))};p.addEventListener("pointerleave",v,c)};return r.forEach(g=>{g.addEventListener("pointerenter",h,c)}),d}const Jy=(a,l)=>l?a===l?!0:Jy(a,l.parentElement):!1,zc=a=>a.pointerType==="mouse"?typeof a.button!="number"||a.button<=0:a.isPrimary!==!1,ub=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function rb(a){return ub.has(a.tagName)||a.tabIndex!==-1}const Zs=new WeakSet;function fp(a){return l=>{l.key==="Enter"&&a(l)}}function Vo(a,l){a.dispatchEvent(new PointerEvent("pointer"+l,{isPrimary:!0,bubbles:!0}))}const ob=(a,l)=>{const u=a.currentTarget;if(!u)return;const r=fp(()=>{if(Zs.has(u))return;Vo(u,"down");const c=fp(()=>{Vo(u,"up")}),d=()=>Vo(u,"cancel");u.addEventListener("keyup",c,l),u.addEventListener("blur",d,l)});u.addEventListener("keydown",r,l),u.addEventListener("blur",()=>u.removeEventListener("keydown",r),l)};function hp(a){return zc(a)&&!ky()}function cb(a,l,u={}){const[r,c,d]=Py(a,u),h=g=>{const p=g.currentTarget;if(!hp(g))return;Zs.add(p);const m=l(p,g),v=(j,B)=>{window.removeEventListener("pointerup",b),window.removeEventListener("pointercancel",E),Zs.has(p)&&Zs.delete(p),hp(j)&&typeof m=="function"&&m(j,{success:B})},b=j=>{v(j,p===window||p===document||u.useGlobalTarget||Jy(p,j.target))},E=j=>{v(j,!1)};window.addEventListener("pointerup",b,c),window.addEventListener("pointercancel",E,c)};return r.forEach(g=>{(u.useGlobalTarget?window:g).addEventListener("pointerdown",h,c),Ky(g)&&(g.addEventListener("focus",m=>ob(m,c)),!rb(g)&&!g.hasAttribute("tabindex")&&(g.tabIndex=0))}),d}function Fy(a){return ry(a)&&"ownerSVGElement"in a}function fb(a){return Fy(a)&&a.tagName==="svg"}const ee=a=>!!(a&&a.getVelocity),hb=[...Gy,Lt,Hn],db=a=>hb.find(Yy(a)),Uc=K.createContext({transformPagePoint:a=>a,isStatic:!1,reducedMotion:"never"});class mb extends K.Component{getSnapshotBeforeUpdate(l){const u=this.props.childRef.current;if(u&&l.isPresent&&!this.props.isPresent){const r=u.offsetParent,c=Ky(r)&&r.offsetWidth||0,d=this.props.sizeRef.current;d.height=u.offsetHeight||0,d.width=u.offsetWidth||0,d.top=u.offsetTop,d.left=u.offsetLeft,d.right=c-d.width-d.left}return null}componentDidUpdate(){}render(){return this.props.children}}function pb({children:a,isPresent:l,anchorX:u,root:r}){const c=K.useId(),d=K.useRef(null),h=K.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:g}=K.useContext(Uc);return K.useInsertionEffect(()=>{const{width:p,height:m,top:v,left:b,right:E}=h.current;if(l||!d.current||!p||!m)return;const j=u==="left"?`left: ${b}`:`right: ${E}`;d.current.dataset.motionPopId=c;const B=document.createElement("style");g&&(B.nonce=g);const q=r??document.head;return q.appendChild(B),B.sheet&&B.sheet.insertRule(`
          [data-motion-pop-id="${c}"] {
            position: absolute !important;
            width: ${p}px !important;
            height: ${m}px !important;
            ${j}px !important;
            top: ${v}px !important;
          }
        `),()=>{q.contains(B)&&q.removeChild(B)}},[l]),x.jsx(mb,{isPresent:l,childRef:d,sizeRef:h,children:K.cloneElement(a,{ref:d})})}const yb=({children:a,initial:l,isPresent:u,onExitComplete:r,custom:c,presenceAffectsLayout:d,mode:h,anchorX:g,root:p})=>{const m=hc(gb),v=K.useId();let b=!0,E=K.useMemo(()=>(b=!1,{id:v,initial:l,isPresent:u,custom:c,onExitComplete:j=>{m.set(j,!0);for(const B of m.values())if(!B)return;r&&r()},register:j=>(m.set(j,!1),()=>m.delete(j))}),[u,m,r]);return d&&b&&(E={...E}),K.useMemo(()=>{m.forEach((j,B)=>m.set(B,!1))},[u]),K.useEffect(()=>{!u&&!m.size&&r&&r()},[u]),h==="popLayout"&&(a=x.jsx(pb,{isPresent:u,anchorX:g,root:p,children:a})),x.jsx(Is.Provider,{value:E,children:a})};function gb(){return new Map}function Wy(a=!0){const l=K.useContext(Is);if(l===null)return[!0,null];const{isPresent:u,onExitComplete:r,register:c}=l,d=K.useId();K.useEffect(()=>{if(a)return c(d)},[a]);const h=K.useCallback(()=>a&&r&&r(d),[d,r,a]);return!u&&r?[!1,h]:[!0]}const Ys=a=>a.key||"";function dp(a){const l=[];return K.Children.forEach(a,u=>{K.isValidElement(u)&&l.push(u)}),l}const vb=({children:a,custom:l,initial:u=!0,onExitComplete:r,presenceAffectsLayout:c=!0,mode:d="sync",propagate:h=!1,anchorX:g="left",root:p})=>{const[m,v]=Wy(h),b=K.useMemo(()=>dp(a),[a]),E=h&&!m?[]:b.map(Ys),j=K.useRef(!0),B=K.useRef(b),q=hc(()=>new Map),[X,Y]=K.useState(b),[Z,H]=K.useState(b);sy(()=>{j.current=!1,B.current=b;for(let W=0;W<Z.length;W++){const nt=Ys(Z[W]);E.includes(nt)?q.delete(nt):q.get(nt)!==!0&&q.set(nt,!1)}},[Z,E.length,E.join("-")]);const et=[];if(b!==X){let W=[...b];for(let nt=0;nt<Z.length;nt++){const J=Z[nt],yt=Ys(J);E.includes(yt)||(W.splice(nt,0,J),et.push(J))}return d==="wait"&&et.length&&(W=et),H(dp(W)),Y(b),null}const{forceRender:L}=K.useContext(fc);return x.jsx(x.Fragment,{children:Z.map(W=>{const nt=Ys(W),J=h&&!m?!1:b===Z||E.includes(nt),yt=()=>{if(q.has(nt))q.set(nt,!0);else return;let Ot=!0;q.forEach(Xt=>{Xt||(Ot=!1)}),Ot&&(L?.(),H(B.current),h&&v?.(),r&&r())};return x.jsx(yb,{isPresent:J,initial:!j.current||u?void 0:!1,custom:l,presenceAffectsLayout:c,mode:d,root:p,onExitComplete:J?void 0:yt,anchorX:g,children:W},nt)})})},$y=K.createContext({strict:!1}),mp={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ii={};for(const a in mp)ii[a]={isEnabled:l=>mp[a].some(u=>!!l[u])};function Sb(a){for(const l in a)ii[l]={...ii[l],...a[l]}}const bb=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Ws(a){return a.startsWith("while")||a.startsWith("drag")&&a!=="draggable"||a.startsWith("layout")||a.startsWith("onTap")||a.startsWith("onPan")||a.startsWith("onLayout")||bb.has(a)}let Iy=a=>!Ws(a);function Tb(a){typeof a=="function"&&(Iy=l=>l.startsWith("on")?!Ws(l):a(l))}try{Tb(require("@emotion/is-prop-valid").default)}catch{}function xb(a,l,u){const r={};for(const c in a)c==="values"&&typeof a.values=="object"||(Iy(c)||u===!0&&Ws(c)||!l&&!Ws(c)||a.draggable&&c.startsWith("onDrag"))&&(r[c]=a[c]);return r}const tu=K.createContext({});function eu(a){return a!==null&&typeof a=="object"&&typeof a.start=="function"}function gl(a){return typeof a=="string"||Array.isArray(a)}const Bc=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],wc=["initial",...Bc];function nu(a){return eu(a.animate)||wc.some(l=>gl(a[l]))}function tg(a){return!!(nu(a)||a.variants)}function Ab(a,l){if(nu(a)){const{initial:u,animate:r}=a;return{initial:u===!1||gl(u)?u:void 0,animate:gl(r)?r:void 0}}return a.inherit!==!1?l:{}}function Eb(a){const{initial:l,animate:u}=Ab(a,K.useContext(tu));return K.useMemo(()=>({initial:l,animate:u}),[pp(l),pp(u)])}function pp(a){return Array.isArray(a)?a.join(" "):a}const vl={};function Mb(a){for(const l in a)vl[l]=a[l],Tc(l)&&(vl[l].isCSSVariable=!0)}function eg(a,{layout:l,layoutId:u}){return ui.has(a)||a.startsWith("origin")||(l||u!==void 0)&&(!!vl[a]||a==="opacity")}const Db={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},_b=si.length;function Rb(a,l,u){let r="",c=!0;for(let d=0;d<_b;d++){const h=si[d],g=a[h];if(g===void 0)continue;let p=!0;if(typeof g=="number"?p=g===(h.startsWith("scale")?1:0):p=parseFloat(g)===0,!p||u){const m=Qy(g,jc[h]);if(!p){c=!1;const v=Db[h]||h;r+=`${v}(${m}) `}u&&(l[h]=m)}}return r=r.trim(),u?r=u(l,c?"":r):c&&(r="none"),r}function Lc(a,l,u){const{style:r,vars:c,transformOrigin:d}=a;let h=!1,g=!1;for(const p in l){const m=l[p];if(ui.has(p)){h=!0;continue}else if(Tc(p)){c[p]=m;continue}else{const v=Qy(m,jc[p]);p.startsWith("origin")?(g=!0,d[p]=v):r[p]=v}}if(l.transform||(h||u?r.transform=Rb(l,a.transform,u):r.transform&&(r.transform="none")),g){const{originX:p="50%",originY:m="50%",originZ:v=0}=d;r.transformOrigin=`${p} ${m} ${v}`}}const Hc=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ng(a,l,u){for(const r in l)!ee(l[r])&&!eg(r,u)&&(a[r]=l[r])}function Ob({transformTemplate:a},l){return K.useMemo(()=>{const u=Hc();return Lc(u,l,a),Object.assign({},u.vars,u.style)},[l])}function Cb(a,l){const u=a.style||{},r={};return ng(r,u,a),Object.assign(r,Ob(a,l)),r}function Nb(a,l){const u={},r=Cb(a,l);return a.drag&&a.dragListener!==!1&&(u.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=a.drag===!0?"none":`pan-${a.drag==="x"?"y":"x"}`),a.tabIndex===void 0&&(a.onTap||a.onTapStart||a.whileTap)&&(u.tabIndex=0),u.style=r,u}const jb={offset:"stroke-dashoffset",array:"stroke-dasharray"},Vb={offset:"strokeDashoffset",array:"strokeDasharray"};function zb(a,l,u=1,r=0,c=!0){a.pathLength=1;const d=c?jb:Vb;a[d.offset]=it.transform(-r);const h=it.transform(l),g=it.transform(u);a[d.array]=`${h} ${g}`}function ag(a,{attrX:l,attrY:u,attrScale:r,pathLength:c,pathSpacing:d=1,pathOffset:h=0,...g},p,m,v){if(Lc(a,g,m),p){a.style.viewBox&&(a.attrs.viewBox=a.style.viewBox);return}a.attrs=a.style,a.style={};const{attrs:b,style:E}=a;b.transform&&(E.transform=b.transform,delete b.transform),(E.transform||b.transformOrigin)&&(E.transformOrigin=b.transformOrigin??"50% 50%",delete b.transformOrigin),E.transform&&(E.transformBox=v?.transformBox??"fill-box",delete b.transformBox),l!==void 0&&(b.x=l),u!==void 0&&(b.y=u),r!==void 0&&(b.scale=r),c!==void 0&&zb(b,c,d,h,!1)}const ig=()=>({...Hc(),attrs:{}}),lg=a=>typeof a=="string"&&a.toLowerCase()==="svg";function Ub(a,l,u,r){const c=K.useMemo(()=>{const d=ig();return ag(d,l,lg(r),a.transformTemplate,a.style),{...d.attrs,style:{...d.style}}},[l]);if(a.style){const d={};ng(d,a.style,a),c.style={...d,...c.style}}return c}const Bb=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function qc(a){return typeof a!="string"||a.includes("-")?!1:!!(Bb.indexOf(a)>-1||/[A-Z]/u.test(a))}function wb(a,l,u,{latestValues:r},c,d=!1){const g=(qc(a)?Ub:Nb)(l,r,c,a),p=xb(l,typeof a=="string",d),m=a!==K.Fragment?{...p,...g,ref:u}:{},{children:v}=l,b=K.useMemo(()=>ee(v)?v.get():v,[v]);return K.createElement(a,{...m,children:b})}function yp(a){const l=[{},{}];return a?.values.forEach((u,r)=>{l[0][r]=u.get(),l[1][r]=u.getVelocity()}),l}function Yc(a,l,u,r){if(typeof l=="function"){const[c,d]=yp(r);l=l(u!==void 0?u:a.custom,c,d)}if(typeof l=="string"&&(l=a.variants&&a.variants[l]),typeof l=="function"){const[c,d]=yp(r);l=l(u!==void 0?u:a.custom,c,d)}return l}function Qs(a){return ee(a)?a.get():a}function Lb({scrapeMotionValuesFromProps:a,createRenderState:l},u,r,c){return{latestValues:Hb(u,r,c,a),renderState:l()}}function Hb(a,l,u,r){const c={},d=r(a,{});for(const E in d)c[E]=Qs(d[E]);let{initial:h,animate:g}=a;const p=nu(a),m=tg(a);l&&m&&!p&&a.inherit!==!1&&(h===void 0&&(h=l.initial),g===void 0&&(g=l.animate));let v=u?u.initial===!1:!1;v=v||h===!1;const b=v?g:h;if(b&&typeof b!="boolean"&&!eu(b)){const E=Array.isArray(b)?b:[b];for(let j=0;j<E.length;j++){const B=Yc(a,E[j]);if(B){const{transitionEnd:q,transition:X,...Y}=B;for(const Z in Y){let H=Y[Z];if(Array.isArray(H)){const et=v?H.length-1:0;H=H[et]}H!==null&&(c[Z]=H)}for(const Z in q)c[Z]=q[Z]}}}return c}const sg=a=>(l,u)=>{const r=K.useContext(tu),c=K.useContext(Is),d=()=>Lb(a,l,r,c);return u?d():hc(d)};function Gc(a,l,u){const{style:r}=a,c={};for(const d in r)(ee(r[d])||l.style&&ee(l.style[d])||eg(d,a)||u?.getValue(d)?.liveStyle!==void 0)&&(c[d]=r[d]);return c}const qb=sg({scrapeMotionValuesFromProps:Gc,createRenderState:Hc});function ug(a,l,u){const r=Gc(a,l,u);for(const c in a)if(ee(a[c])||ee(l[c])){const d=si.indexOf(c)!==-1?"attr"+c.charAt(0).toUpperCase()+c.substring(1):c;r[d]=a[c]}return r}const Yb=sg({scrapeMotionValuesFromProps:ug,createRenderState:ig}),Gb=Symbol.for("motionComponentSymbol");function $a(a){return a&&typeof a=="object"&&Object.prototype.hasOwnProperty.call(a,"current")}function Xb(a,l,u){return K.useCallback(r=>{r&&a.onMount&&a.onMount(r),l&&(r?l.mount(r):l.unmount()),u&&(typeof u=="function"?u(r):$a(u)&&(u.current=r))},[l])}const Xc=a=>a.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),Zb="framerAppearId",rg="data-"+Xc(Zb),og=K.createContext({});function Qb(a,l,u,r,c){const{visualElement:d}=K.useContext(tu),h=K.useContext($y),g=K.useContext(Is),p=K.useContext(Uc).reducedMotion,m=K.useRef(null);r=r||h.renderer,!m.current&&r&&(m.current=r(a,{visualState:l,parent:d,props:u,presenceContext:g,blockInitialAnimation:g?g.initial===!1:!1,reducedMotionConfig:p}));const v=m.current,b=K.useContext(og);v&&!v.projection&&c&&(v.type==="html"||v.type==="svg")&&Kb(m.current,u,c,b);const E=K.useRef(!1);K.useInsertionEffect(()=>{v&&E.current&&v.update(u,g)});const j=u[rg],B=K.useRef(!!j&&!window.MotionHandoffIsComplete?.(j)&&window.MotionHasOptimisedAnimation?.(j));return sy(()=>{v&&(E.current=!0,window.MotionIsMounted=!0,v.updateFeatures(),v.scheduleRenderMicrotask(),B.current&&v.animationState&&v.animationState.animateChanges())}),K.useEffect(()=>{v&&(!B.current&&v.animationState&&v.animationState.animateChanges(),B.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(j)}),B.current=!1),v.enteringChildren=void 0)}),v}function Kb(a,l,u,r){const{layoutId:c,layout:d,drag:h,dragConstraints:g,layoutScroll:p,layoutRoot:m,layoutCrossfade:v}=l;a.projection=new u(a.latestValues,l["data-framer-portal-id"]?void 0:cg(a.parent)),a.projection.setOptions({layoutId:c,layout:d,alwaysMeasureLayout:!!h||g&&$a(g),visualElement:a,animationType:typeof d=="string"?d:"both",initialPromotionConfig:r,crossfade:v,layoutScroll:p,layoutRoot:m})}function cg(a){if(a)return a.options.allowProjection!==!1?a.projection:cg(a.parent)}function zo(a,{forwardMotionProps:l=!1}={},u,r){u&&Sb(u);const c=qc(a)?Yb:qb;function d(g,p){let m;const v={...K.useContext(Uc),...g,layoutId:kb(g)},{isStatic:b}=v,E=Eb(g),j=c(g,b);if(!b&&dc){Pb();const B=Jb(v);m=B.MeasureLayout,E.visualElement=Qb(a,j,v,r,B.ProjectionNode)}return x.jsxs(tu.Provider,{value:E,children:[m&&E.visualElement?x.jsx(m,{visualElement:E.visualElement,...v}):null,wb(a,g,Xb(j,E.visualElement,p),j,b,l)]})}d.displayName=`motion.${typeof a=="string"?a:`create(${a.displayName??a.name??""})`}`;const h=K.forwardRef(d);return h[Gb]=a,h}function kb({layoutId:a}){const l=K.useContext(fc).id;return l&&a!==void 0?l+"-"+a:a}function Pb(a,l){K.useContext($y).strict}function Jb(a){const{drag:l,layout:u}=ii;if(!l&&!u)return{};const r={...l,...u};return{MeasureLayout:l?.isEnabled(a)||u?.isEnabled(a)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}function Fb(a,l){if(typeof Proxy>"u")return zo;const u=new Map,r=(d,h)=>zo(d,h,a,l),c=(d,h)=>r(d,h);return new Proxy(c,{get:(d,h)=>h==="create"?r:(u.has(h)||u.set(h,zo(h,void 0,a,l)),u.get(h))})}function fg({top:a,left:l,right:u,bottom:r}){return{x:{min:l,max:u},y:{min:a,max:r}}}function Wb({x:a,y:l}){return{top:l.min,right:a.max,bottom:l.max,left:a.min}}function $b(a,l){if(!l)return a;const u=l({x:a.left,y:a.top}),r=l({x:a.right,y:a.bottom});return{top:u.y,left:u.x,bottom:r.y,right:r.x}}function Uo(a){return a===void 0||a===1}function nc({scale:a,scaleX:l,scaleY:u}){return!Uo(a)||!Uo(l)||!Uo(u)}function sa(a){return nc(a)||hg(a)||a.z||a.rotate||a.rotateX||a.rotateY||a.skewX||a.skewY}function hg(a){return gp(a.x)||gp(a.y)}function gp(a){return a&&a!=="0%"}function $s(a,l,u){const r=a-u,c=l*r;return u+c}function vp(a,l,u,r,c){return c!==void 0&&(a=$s(a,c,r)),$s(a,u,r)+l}function ac(a,l=0,u=1,r,c){a.min=vp(a.min,l,u,r,c),a.max=vp(a.max,l,u,r,c)}function dg(a,{x:l,y:u}){ac(a.x,l.translate,l.scale,l.originPoint),ac(a.y,u.translate,u.scale,u.originPoint)}const Sp=.999999999999,bp=1.0000000000001;function Ib(a,l,u,r=!1){const c=u.length;if(!c)return;l.x=l.y=1;let d,h;for(let g=0;g<c;g++){d=u[g],h=d.projectionDelta;const{visualElement:p}=d.options;p&&p.props.style&&p.props.style.display==="contents"||(r&&d.options.layoutScroll&&d.scroll&&d!==d.root&&ti(a,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),h&&(l.x*=h.x.scale,l.y*=h.y.scale,dg(a,h)),r&&sa(d.latestValues)&&ti(a,d.latestValues))}l.x<bp&&l.x>Sp&&(l.x=1),l.y<bp&&l.y>Sp&&(l.y=1)}function Ia(a,l){a.min=a.min+l,a.max=a.max+l}function Tp(a,l,u,r,c=.5){const d=Rt(a.min,a.max,c);ac(a,l,u,d,r)}function ti(a,l){Tp(a.x,l.x,l.scaleX,l.scale,l.originX),Tp(a.y,l.y,l.scaleY,l.scale,l.originY)}function mg(a,l){return fg($b(a.getBoundingClientRect(),l))}function tT(a,l,u){const r=mg(a,u),{scroll:c}=l;return c&&(Ia(r.x,c.offset.x),Ia(r.y,c.offset.y)),r}const xp=()=>({translate:0,scale:1,origin:0,originPoint:0}),ei=()=>({x:xp(),y:xp()}),Ap=()=>({min:0,max:0}),Ut=()=>({x:Ap(),y:Ap()}),ic={current:null},pg={current:!1};function eT(){if(pg.current=!0,!!dc)if(window.matchMedia){const a=window.matchMedia("(prefers-reduced-motion)"),l=()=>ic.current=a.matches;a.addEventListener("change",l),l()}else ic.current=!1}const nT=new WeakMap;function aT(a,l,u){for(const r in l){const c=l[r],d=u[r];if(ee(c))a.addValue(r,c);else if(ee(d))a.addValue(r,ai(c,{owner:a}));else if(d!==c)if(a.hasValue(r)){const h=a.getValue(r);h.liveStyle===!0?h.jump(c):h.hasAnimated||h.set(c)}else{const h=a.getStaticValue(r);a.addValue(r,ai(h!==void 0?h:c,{owner:a}))}}for(const r in u)l[r]===void 0&&a.removeValue(r);return l}const Ep=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class iT{scrapeMotionValuesFromProps(l,u,r){return{}}constructor({parent:l,props:u,presenceContext:r,reducedMotionConfig:c,blockInitialAnimation:d,visualState:h},g={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Cc,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const E=oe.now();this.renderScheduledAt<E&&(this.renderScheduledAt=E,Dt.render(this.render,!1,!0))};const{latestValues:p,renderState:m}=h;this.latestValues=p,this.baseTarget={...p},this.initialValues=u.initial?{...p}:{},this.renderState=m,this.parent=l,this.props=u,this.presenceContext=r,this.depth=l?l.depth+1:0,this.reducedMotionConfig=c,this.options=g,this.blockInitialAnimation=!!d,this.isControllingVariants=nu(u),this.isVariantNode=tg(u),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(l&&l.current);const{willChange:v,...b}=this.scrapeMotionValuesFromProps(u,{},this);for(const E in b){const j=b[E];p[E]!==void 0&&ee(j)&&j.set(p[E])}}mount(l){this.current=l,nT.set(l,this),this.projection&&!this.projection.instance&&this.projection.mount(l),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((u,r)=>this.bindToMotionValue(r,u)),pg.current||eT(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:ic.current,this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),Ln(this.notifyUpdate),Ln(this.render),this.valueSubscriptions.forEach(l=>l()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this);for(const l in this.events)this.events[l].clear();for(const l in this.features){const u=this.features[l];u&&(u.unmount(),u.isMounted=!1)}this.current=null}addChild(l){this.children.add(l),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(l)}removeChild(l){this.children.delete(l),this.enteringChildren&&this.enteringChildren.delete(l)}bindToMotionValue(l,u){this.valueSubscriptions.has(l)&&this.valueSubscriptions.get(l)();const r=ui.has(l);r&&this.onBindTransform&&this.onBindTransform();const c=u.on("change",h=>{this.latestValues[l]=h,this.props.onUpdate&&Dt.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});let d;window.MotionCheckAppearSync&&(d=window.MotionCheckAppearSync(this,l,u)),this.valueSubscriptions.set(l,()=>{c(),d&&d(),u.owner&&u.stop()})}sortNodePosition(l){return!this.current||!this.sortInstanceNodePosition||this.type!==l.type?0:this.sortInstanceNodePosition(this.current,l.current)}updateFeatures(){let l="animation";for(l in ii){const u=ii[l];if(!u)continue;const{isEnabled:r,Feature:c}=u;if(!this.features[l]&&c&&r(this.props)&&(this.features[l]=new c(this)),this.features[l]){const d=this.features[l];d.isMounted?d.update():(d.mount(),d.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):Ut()}getStaticValue(l){return this.latestValues[l]}setStaticValue(l,u){this.latestValues[l]=u}update(l,u){(l.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=l,this.prevPresenceContext=this.presenceContext,this.presenceContext=u;for(let r=0;r<Ep.length;r++){const c=Ep[r];this.propEventSubscriptions[c]&&(this.propEventSubscriptions[c](),delete this.propEventSubscriptions[c]);const d="on"+c,h=l[d];h&&(this.propEventSubscriptions[c]=this.on(c,h))}this.prevMotionValues=aT(this,this.scrapeMotionValuesFromProps(l,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(l){return this.props.variants?this.props.variants[l]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(l){const u=this.getClosestVariantNode();if(u)return u.variantChildren&&u.variantChildren.add(l),()=>u.variantChildren.delete(l)}addValue(l,u){const r=this.values.get(l);u!==r&&(r&&this.removeValue(l),this.bindToMotionValue(l,u),this.values.set(l,u),this.latestValues[l]=u.get())}removeValue(l){this.values.delete(l);const u=this.valueSubscriptions.get(l);u&&(u(),this.valueSubscriptions.delete(l)),delete this.latestValues[l],this.removeValueFromRenderState(l,this.renderState)}hasValue(l){return this.values.has(l)}getValue(l,u){if(this.props.values&&this.props.values[l])return this.props.values[l];let r=this.values.get(l);return r===void 0&&u!==void 0&&(r=ai(u===null?void 0:u,{owner:this}),this.addValue(l,r)),r}readValue(l,u){let r=this.latestValues[l]!==void 0||!this.current?this.latestValues[l]:this.getBaseTargetFromProps(this.props,l)??this.readValueFromInstance(this.current,l,this.options);return r!=null&&(typeof r=="string"&&(uy(r)||oy(r))?r=parseFloat(r):!db(r)&&Hn.test(u)&&(r=Zy(l,u)),this.setBaseTarget(l,ee(r)?r.get():r)),ee(r)?r.get():r}setBaseTarget(l,u){this.baseTarget[l]=u}getBaseTarget(l){const{initial:u}=this.props;let r;if(typeof u=="string"||typeof u=="object"){const d=Yc(this.props,u,this.presenceContext?.custom);d&&(r=d[l])}if(u&&r!==void 0)return r;const c=this.getBaseTargetFromProps(this.props,l);return c!==void 0&&!ee(c)?c:this.initialValues[l]!==void 0&&r===void 0?void 0:this.baseTarget[l]}on(l,u){return this.events[l]||(this.events[l]=new vc),this.events[l].add(u)}notify(l,...u){this.events[l]&&this.events[l].notify(...u)}scheduleRenderMicrotask(){Vc.render(this.render)}}class yg extends iT{constructor(){super(...arguments),this.KeyframeResolver=eb}sortInstanceNodePosition(l,u){return l.compareDocumentPosition(u)&2?1:-1}getBaseTargetFromProps(l,u){return l.style?l.style[u]:void 0}removeValueFromRenderState(l,{vars:u,style:r}){delete u[l],delete r[l]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:l}=this.props;ee(l)&&(this.childSubscription=l.on("change",u=>{this.current&&(this.current.textContent=`${u}`)}))}}function gg(a,{style:l,vars:u},r,c){const d=a.style;let h;for(h in l)d[h]=l[h];c?.applyProjectionStyles(d,r);for(h in u)d.setProperty(h,u[h])}function lT(a){return window.getComputedStyle(a)}class sT extends yg{constructor(){super(...arguments),this.type="html",this.renderInstance=gg}readValueFromInstance(l,u){if(ui.has(u))return this.projection?.isProjecting?Jo(u):bS(l,u);{const r=lT(l),c=(Tc(u)?r.getPropertyValue(u):r[u])||0;return typeof c=="string"?c.trim():c}}measureInstanceViewportBox(l,{transformPagePoint:u}){return mg(l,u)}build(l,u,r){Lc(l,u,r.transformTemplate)}scrapeMotionValuesFromProps(l,u,r){return Gc(l,u,r)}}const vg=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function uT(a,l,u,r){gg(a,l,void 0,r);for(const c in l.attrs)a.setAttribute(vg.has(c)?c:Xc(c),l.attrs[c])}class rT extends yg{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=Ut}getBaseTargetFromProps(l,u){return l[u]}readValueFromInstance(l,u){if(ui.has(u)){const r=Xy(u);return r&&r.default||0}return u=vg.has(u)?u:Xc(u),l.getAttribute(u)}scrapeMotionValuesFromProps(l,u,r){return ug(l,u,r)}build(l,u,r){ag(l,u,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(l,u,r,c){uT(l,u,r,c)}mount(l){this.isSVGTag=lg(l.tagName),super.mount(l)}}const oT=(a,l)=>qc(a)?new rT(l):new sT(l,{allowProjection:a!==K.Fragment});function ni(a,l,u){const r=a.getProps();return Yc(r,l,u!==void 0?u:r.custom,a)}const lc=a=>Array.isArray(a);function cT(a,l,u){a.hasValue(l)?a.getValue(l).set(u):a.addValue(l,ai(u))}function fT(a){return lc(a)?a[a.length-1]||0:a}function hT(a,l){const u=ni(a,l);let{transitionEnd:r={},transition:c={},...d}=u||{};d={...d,...r};for(const h in d){const g=fT(d[h]);cT(a,h,g)}}function dT(a){return!!(ee(a)&&a.add)}function sc(a,l){const u=a.getValue("willChange");if(dT(u))return u.add(l);if(!u&&fn.WillChange){const r=new fn.WillChange("auto");a.addValue("willChange",r),r.add(l)}}function Sg(a){return a.props[rg]}const mT=a=>a!==null;function pT(a,{repeat:l,repeatType:u="loop"},r){const c=a.filter(mT),d=l&&u!=="loop"&&l%2===1?0:c.length-1;return c[d]}const yT={type:"spring",stiffness:500,damping:25,restSpeed:10},gT=a=>({type:"spring",stiffness:550,damping:a===0?2*Math.sqrt(550):30,restSpeed:10}),vT={type:"keyframes",duration:.8},ST={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},bT=(a,{keyframes:l})=>l.length>2?vT:ui.has(a)?a.startsWith("scale")?gT(l[1]):yT:ST;function TT({when:a,delay:l,delayChildren:u,staggerChildren:r,staggerDirection:c,repeat:d,repeatType:h,repeatDelay:g,from:p,elapsed:m,...v}){return!!Object.keys(v).length}const Zc=(a,l,u,r={},c,d)=>h=>{const g=Nc(r,a)||{},p=g.delay||r.delay||0;let{elapsed:m=0}=r;m=m-Xe(p);const v={keyframes:Array.isArray(u)?u:[null,u],ease:"easeOut",velocity:l.getVelocity(),...g,delay:-m,onUpdate:E=>{l.set(E),g.onUpdate&&g.onUpdate(E)},onComplete:()=>{h(),g.onComplete&&g.onComplete()},name:a,motionValue:l,element:d?void 0:c};TT(g)||Object.assign(v,bT(a,v)),v.duration&&(v.duration=Xe(v.duration)),v.repeatDelay&&(v.repeatDelay=Xe(v.repeatDelay)),v.from!==void 0&&(v.keyframes[0]=v.from);let b=!1;if((v.type===!1||v.duration===0&&!v.repeatDelay)&&(tc(v),v.delay===0&&(b=!0)),(fn.instantAnimations||fn.skipAnimations)&&(b=!0,tc(v),v.delay=0),v.allowFlatten=!g.type&&!g.ease,b&&!d&&l.get()!==void 0){const E=pT(v.keyframes,g);if(E!==void 0){Dt.update(()=>{v.onUpdate(E),v.onComplete()});return}}return g.isSync?new Oc(v):new XS(v)};function xT({protectedKeys:a,needsAnimating:l},u){const r=a.hasOwnProperty(u)&&l[u]!==!0;return l[u]=!1,r}function bg(a,l,{delay:u=0,transitionOverride:r,type:c}={}){let{transition:d=a.getDefaultTransition(),transitionEnd:h,...g}=l;r&&(d=r);const p=[],m=c&&a.animationState&&a.animationState.getState()[c];for(const v in g){const b=a.getValue(v,a.latestValues[v]??null),E=g[v];if(E===void 0||m&&xT(m,v))continue;const j={delay:u,...Nc(d||{},v)},B=b.get();if(B!==void 0&&!b.isAnimating&&!Array.isArray(E)&&E===B&&!j.velocity)continue;let q=!1;if(window.MotionHandoffAnimation){const Y=Sg(a);if(Y){const Z=window.MotionHandoffAnimation(Y,v,Dt);Z!==null&&(j.startTime=Z,q=!0)}}sc(a,v),b.start(Zc(v,b,E,a.shouldReduceMotion&&qy.has(v)?{type:!1}:j,a,q));const X=b.animation;X&&p.push(X)}return h&&Promise.all(p).then(()=>{Dt.update(()=>{h&&hT(a,h)})}),p}function Tg(a,l,u,r=0,c=1){const d=Array.from(a).sort((m,v)=>m.sortNodePosition(v)).indexOf(l),h=a.size,g=(h-1)*r;return typeof u=="function"?u(d,h):c===1?d*r:g-d*r}function uc(a,l,u={}){const r=ni(a,l,u.type==="exit"?a.presenceContext?.custom:void 0);let{transition:c=a.getDefaultTransition()||{}}=r||{};u.transitionOverride&&(c=u.transitionOverride);const d=r?()=>Promise.all(bg(a,r,u)):()=>Promise.resolve(),h=a.variantChildren&&a.variantChildren.size?(p=0)=>{const{delayChildren:m=0,staggerChildren:v,staggerDirection:b}=c;return AT(a,l,p,m,v,b,u)}:()=>Promise.resolve(),{when:g}=c;if(g){const[p,m]=g==="beforeChildren"?[d,h]:[h,d];return p().then(()=>m())}else return Promise.all([d(),h(u.delay)])}function AT(a,l,u=0,r=0,c=0,d=1,h){const g=[];for(const p of a.variantChildren)p.notify("AnimationStart",l),g.push(uc(p,l,{...h,delay:u+(typeof r=="function"?0:r)+Tg(a.variantChildren,p,r,c,d)}).then(()=>p.notify("AnimationComplete",l)));return Promise.all(g)}function ET(a,l,u={}){a.notify("AnimationStart",l);let r;if(Array.isArray(l)){const c=l.map(d=>uc(a,d,u));r=Promise.all(c)}else if(typeof l=="string")r=uc(a,l,u);else{const c=typeof l=="function"?ni(a,l,u.custom):l;r=Promise.all(bg(a,c,u))}return r.then(()=>{a.notify("AnimationComplete",l)})}function xg(a,l){if(!Array.isArray(l))return!1;const u=l.length;if(u!==a.length)return!1;for(let r=0;r<u;r++)if(l[r]!==a[r])return!1;return!0}const MT=wc.length;function Ag(a){if(!a)return;if(!a.isControllingVariants){const u=a.parent?Ag(a.parent)||{}:{};return a.props.initial!==void 0&&(u.initial=a.props.initial),u}const l={};for(let u=0;u<MT;u++){const r=wc[u],c=a.props[r];(gl(c)||c===!1)&&(l[r]=c)}return l}const DT=[...Bc].reverse(),_T=Bc.length;function RT(a){return l=>Promise.all(l.map(({animation:u,options:r})=>ET(a,u,r)))}function OT(a){let l=RT(a),u=Mp(),r=!0;const c=p=>(m,v)=>{const b=ni(a,v,p==="exit"?a.presenceContext?.custom:void 0);if(b){const{transition:E,transitionEnd:j,...B}=b;m={...m,...B,...j}}return m};function d(p){l=p(a)}function h(p){const{props:m}=a,v=Ag(a.parent)||{},b=[],E=new Set;let j={},B=1/0;for(let X=0;X<_T;X++){const Y=DT[X],Z=u[Y],H=m[Y]!==void 0?m[Y]:v[Y],et=gl(H),L=Y===p?Z.isActive:null;L===!1&&(B=X);let W=H===v[Y]&&H!==m[Y]&&et;if(W&&r&&a.manuallyAnimateOnMount&&(W=!1),Z.protectedKeys={...j},!Z.isActive&&L===null||!H&&!Z.prevProp||eu(H)||typeof H=="boolean")continue;const nt=CT(Z.prevProp,H);let J=nt||Y===p&&Z.isActive&&!W&&et||X>B&&et,yt=!1;const Ot=Array.isArray(H)?H:[H];let Xt=Ot.reduce(c(Y),{});L===!1&&(Xt={});const{prevResolvedValues:Ht={}}=Z,ke={...Ht,...Xt},Be=w=>{J=!0,E.has(w)&&(yt=!0,E.delete(w)),Z.needsAnimating[w]=!0;const P=a.getValue(w);P&&(P.liveStyle=!1)};for(const w in ke){const P=Xt[w],ft=Ht[w];if(j.hasOwnProperty(w))continue;let T=!1;lc(P)&&lc(ft)?T=!xg(P,ft):T=P!==ft,T?P!=null?Be(w):E.add(w):P!==void 0&&E.has(w)?Be(w):Z.protectedKeys[w]=!0}Z.prevProp=H,Z.prevResolvedValues=Xt,Z.isActive&&(j={...j,...Xt}),r&&a.blockInitialAnimation&&(J=!1);const qt=W&&nt;J&&(!qt||yt)&&b.push(...Ot.map(w=>{const P={type:Y};if(typeof w=="string"&&r&&!qt&&a.manuallyAnimateOnMount&&a.parent){const{parent:ft}=a,T=ni(ft,w);if(ft.enteringChildren&&T){const{delayChildren:U}=T.transition||{};P.delay=Tg(ft.enteringChildren,a,U)}}return{animation:w,options:P}}))}if(E.size){const X={};if(typeof m.initial!="boolean"){const Y=ni(a,Array.isArray(m.initial)?m.initial[0]:m.initial);Y&&Y.transition&&(X.transition=Y.transition)}E.forEach(Y=>{const Z=a.getBaseTarget(Y),H=a.getValue(Y);H&&(H.liveStyle=!0),X[Y]=Z??null}),b.push({animation:X})}let q=!!b.length;return r&&(m.initial===!1||m.initial===m.animate)&&!a.manuallyAnimateOnMount&&(q=!1),r=!1,q?l(b):Promise.resolve()}function g(p,m){if(u[p].isActive===m)return Promise.resolve();a.variantChildren?.forEach(b=>b.animationState?.setActive(p,m)),u[p].isActive=m;const v=h(p);for(const b in u)u[b].protectedKeys={};return v}return{animateChanges:h,setActive:g,setAnimateFunction:d,getState:()=>u,reset:()=>{u=Mp(),r=!0}}}function CT(a,l){return typeof l=="string"?l!==a:Array.isArray(l)?!xg(l,a):!1}function la(a=!1){return{isActive:a,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Mp(){return{animate:la(!0),whileInView:la(),whileHover:la(),whileTap:la(),whileDrag:la(),whileFocus:la(),exit:la()}}class qn{constructor(l){this.isMounted=!1,this.node=l}update(){}}class NT extends qn{constructor(l){super(l),l.animationState||(l.animationState=OT(l))}updateAnimationControlsSubscription(){const{animate:l}=this.node.getProps();eu(l)&&(this.unmountControls=l.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:l}=this.node.getProps(),{animate:u}=this.node.prevProps||{};l!==u&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let jT=0;class VT extends qn{constructor(){super(...arguments),this.id=jT++}update(){if(!this.node.presenceContext)return;const{isPresent:l,onExitComplete:u}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||l===r)return;const c=this.node.animationState.setActive("exit",!l);u&&!l&&c.then(()=>{u(this.id)})}mount(){const{register:l,onExitComplete:u}=this.node.presenceContext||{};u&&u(this.id),l&&(this.unmount=l(this.id))}unmount(){}}const zT={animation:{Feature:NT},exit:{Feature:VT}};function Sl(a,l,u,r={passive:!0}){return a.addEventListener(l,u,r),()=>a.removeEventListener(l,u)}function Al(a){return{point:{x:a.pageX,y:a.pageY}}}const UT=a=>l=>zc(l)&&a(l,Al(l));function fl(a,l,u,r){return Sl(a,l,UT(u),r)}const Eg=1e-4,BT=1-Eg,wT=1+Eg,Mg=.01,LT=0-Mg,HT=0+Mg;function ae(a){return a.max-a.min}function qT(a,l,u){return Math.abs(a-l)<=u}function Dp(a,l,u,r=.5){a.origin=r,a.originPoint=Rt(l.min,l.max,a.origin),a.scale=ae(u)/ae(l),a.translate=Rt(u.min,u.max,a.origin)-a.originPoint,(a.scale>=BT&&a.scale<=wT||isNaN(a.scale))&&(a.scale=1),(a.translate>=LT&&a.translate<=HT||isNaN(a.translate))&&(a.translate=0)}function hl(a,l,u,r){Dp(a.x,l.x,u.x,r?r.originX:void 0),Dp(a.y,l.y,u.y,r?r.originY:void 0)}function _p(a,l,u){a.min=u.min+l.min,a.max=a.min+ae(l)}function YT(a,l,u){_p(a.x,l.x,u.x),_p(a.y,l.y,u.y)}function Rp(a,l,u){a.min=l.min-u.min,a.max=a.min+ae(l)}function dl(a,l,u){Rp(a.x,l.x,u.x),Rp(a.y,l.y,u.y)}function Ce(a){return[a("x"),a("y")]}const Dg=({current:a})=>a?a.ownerDocument.defaultView:null,Op=(a,l)=>Math.abs(a-l);function GT(a,l){const u=Op(a.x,l.x),r=Op(a.y,l.y);return Math.sqrt(u**2+r**2)}class _g{constructor(l,u,{transformPagePoint:r,contextWindow:c=window,dragSnapToOrigin:d=!1,distanceThreshold:h=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const E=wo(this.lastMoveEventInfo,this.history),j=this.startEvent!==null,B=GT(E.offset,{x:0,y:0})>=this.distanceThreshold;if(!j&&!B)return;const{point:q}=E,{timestamp:X}=Wt;this.history.push({...q,timestamp:X});const{onStart:Y,onMove:Z}=this.handlers;j||(Y&&Y(this.lastMoveEvent,E),this.startEvent=this.lastMoveEvent),Z&&Z(this.lastMoveEvent,E)},this.handlePointerMove=(E,j)=>{this.lastMoveEvent=E,this.lastMoveEventInfo=Bo(j,this.transformPagePoint),Dt.update(this.updatePoint,!0)},this.handlePointerUp=(E,j)=>{this.end();const{onEnd:B,onSessionEnd:q,resumeAnimation:X}=this.handlers;if(this.dragSnapToOrigin&&X&&X(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const Y=wo(E.type==="pointercancel"?this.lastMoveEventInfo:Bo(j,this.transformPagePoint),this.history);this.startEvent&&B&&B(E,Y),q&&q(E,Y)},!zc(l))return;this.dragSnapToOrigin=d,this.handlers=u,this.transformPagePoint=r,this.distanceThreshold=h,this.contextWindow=c||window;const g=Al(l),p=Bo(g,this.transformPagePoint),{point:m}=p,{timestamp:v}=Wt;this.history=[{...m,timestamp:v}];const{onSessionStart:b}=u;b&&b(l,wo(p,this.history)),this.removeListeners=bl(fl(this.contextWindow,"pointermove",this.handlePointerMove),fl(this.contextWindow,"pointerup",this.handlePointerUp),fl(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(l){this.handlers=l}end(){this.removeListeners&&this.removeListeners(),Ln(this.updatePoint)}}function Bo(a,l){return l?{point:l(a.point)}:a}function Cp(a,l){return{x:a.x-l.x,y:a.y-l.y}}function wo({point:a},l){return{point:a,delta:Cp(a,Rg(l)),offset:Cp(a,XT(l)),velocity:ZT(l,.1)}}function XT(a){return a[0]}function Rg(a){return a[a.length-1]}function ZT(a,l){if(a.length<2)return{x:0,y:0};let u=a.length-1,r=null;const c=Rg(a);for(;u>=0&&(r=a[u],!(c.timestamp-r.timestamp>Xe(l)));)u--;if(!r)return{x:0,y:0};const d=Ze(c.timestamp-r.timestamp);if(d===0)return{x:0,y:0};const h={x:(c.x-r.x)/d,y:(c.y-r.y)/d};return h.x===1/0&&(h.x=0),h.y===1/0&&(h.y=0),h}function QT(a,{min:l,max:u},r){return l!==void 0&&a<l?a=r?Rt(l,a,r.min):Math.max(a,l):u!==void 0&&a>u&&(a=r?Rt(u,a,r.max):Math.min(a,u)),a}function Np(a,l,u){return{min:l!==void 0?a.min+l:void 0,max:u!==void 0?a.max+u-(a.max-a.min):void 0}}function KT(a,{top:l,left:u,bottom:r,right:c}){return{x:Np(a.x,u,c),y:Np(a.y,l,r)}}function jp(a,l){let u=l.min-a.min,r=l.max-a.max;return l.max-l.min<a.max-a.min&&([u,r]=[r,u]),{min:u,max:r}}function kT(a,l){return{x:jp(a.x,l.x),y:jp(a.y,l.y)}}function PT(a,l){let u=.5;const r=ae(a),c=ae(l);return c>r?u=ml(l.min,l.max-r,a.min):r>c&&(u=ml(a.min,a.max-c,l.min)),cn(0,1,u)}function JT(a,l){const u={};return l.min!==void 0&&(u.min=l.min-a.min),l.max!==void 0&&(u.max=l.max-a.min),u}const rc=.35;function FT(a=rc){return a===!1?a=0:a===!0&&(a=rc),{x:Vp(a,"left","right"),y:Vp(a,"top","bottom")}}function Vp(a,l,u){return{min:zp(a,l),max:zp(a,u)}}function zp(a,l){return typeof a=="number"?a:a[l]||0}const WT=new WeakMap;class $T{constructor(l){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=Ut(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=l}start(l,{snapToCursor:u=!1,distanceThreshold:r}={}){const{presenceContext:c}=this.visualElement;if(c&&c.isPresent===!1)return;const d=b=>{const{dragSnapToOrigin:E}=this.getProps();E?this.pauseAnimation():this.stopAnimation(),u&&this.snapToCursor(Al(b).point)},h=(b,E)=>{const{drag:j,dragPropagation:B,onDragStart:q}=this.getProps();if(j&&!B&&(this.openDragLock&&this.openDragLock(),this.openDragLock=lb(j),!this.openDragLock))return;this.latestPointerEvent=b,this.latestPanInfo=E,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ce(Y=>{let Z=this.getAxisMotionValue(Y).get()||0;if(Qe.test(Z)){const{projection:H}=this.visualElement;if(H&&H.layout){const et=H.layout.layoutBox[Y];et&&(Z=ae(et)*(parseFloat(Z)/100))}}this.originPoint[Y]=Z}),q&&Dt.postRender(()=>q(b,E)),sc(this.visualElement,"transform");const{animationState:X}=this.visualElement;X&&X.setActive("whileDrag",!0)},g=(b,E)=>{this.latestPointerEvent=b,this.latestPanInfo=E;const{dragPropagation:j,dragDirectionLock:B,onDirectionLock:q,onDrag:X}=this.getProps();if(!j&&!this.openDragLock)return;const{offset:Y}=E;if(B&&this.currentDirection===null){this.currentDirection=IT(Y),this.currentDirection!==null&&q&&q(this.currentDirection);return}this.updateAxis("x",E.point,Y),this.updateAxis("y",E.point,Y),this.visualElement.render(),X&&X(b,E)},p=(b,E)=>{this.latestPointerEvent=b,this.latestPanInfo=E,this.stop(b,E),this.latestPointerEvent=null,this.latestPanInfo=null},m=()=>Ce(b=>this.getAnimationState(b)==="paused"&&this.getAxisMotionValue(b).animation?.play()),{dragSnapToOrigin:v}=this.getProps();this.panSession=new _g(l,{onSessionStart:d,onStart:h,onMove:g,onSessionEnd:p,resumeAnimation:m},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:v,distanceThreshold:r,contextWindow:Dg(this.visualElement)})}stop(l,u){const r=l||this.latestPointerEvent,c=u||this.latestPanInfo,d=this.isDragging;if(this.cancel(),!d||!c||!r)return;const{velocity:h}=c;this.startAnimation(h);const{onDragEnd:g}=this.getProps();g&&Dt.postRender(()=>g(r,c))}cancel(){this.isDragging=!1;const{projection:l,animationState:u}=this.visualElement;l&&(l.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),u&&u.setActive("whileDrag",!1)}updateAxis(l,u,r){const{drag:c}=this.getProps();if(!r||!Gs(l,c,this.currentDirection))return;const d=this.getAxisMotionValue(l);let h=this.originPoint[l]+r[l];this.constraints&&this.constraints[l]&&(h=QT(h,this.constraints[l],this.elastic[l])),d.set(h)}resolveConstraints(){const{dragConstraints:l,dragElastic:u}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,c=this.constraints;l&&$a(l)?this.constraints||(this.constraints=this.resolveRefConstraints()):l&&r?this.constraints=KT(r.layoutBox,l):this.constraints=!1,this.elastic=FT(u),c!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&Ce(d=>{this.constraints!==!1&&this.getAxisMotionValue(d)&&(this.constraints[d]=JT(r.layoutBox[d],this.constraints[d]))})}resolveRefConstraints(){const{dragConstraints:l,onMeasureDragConstraints:u}=this.getProps();if(!l||!$a(l))return!1;const r=l.current,{projection:c}=this.visualElement;if(!c||!c.layout)return!1;const d=tT(r,c.root,this.visualElement.getTransformPagePoint());let h=kT(c.layout.layoutBox,d);if(u){const g=u(Wb(h));this.hasMutatedConstraints=!!g,g&&(h=fg(g))}return h}startAnimation(l){const{drag:u,dragMomentum:r,dragElastic:c,dragTransition:d,dragSnapToOrigin:h,onDragTransitionEnd:g}=this.getProps(),p=this.constraints||{},m=Ce(v=>{if(!Gs(v,u,this.currentDirection))return;let b=p&&p[v]||{};h&&(b={min:0,max:0});const E=c?200:1e6,j=c?40:1e7,B={type:"inertia",velocity:r?l[v]:0,bounceStiffness:E,bounceDamping:j,timeConstant:750,restDelta:1,restSpeed:10,...d,...b};return this.startAxisValueAnimation(v,B)});return Promise.all(m).then(g)}startAxisValueAnimation(l,u){const r=this.getAxisMotionValue(l);return sc(this.visualElement,l),r.start(Zc(l,r,0,u,this.visualElement,!1))}stopAnimation(){Ce(l=>this.getAxisMotionValue(l).stop())}pauseAnimation(){Ce(l=>this.getAxisMotionValue(l).animation?.pause())}getAnimationState(l){return this.getAxisMotionValue(l).animation?.state}getAxisMotionValue(l){const u=`_drag${l.toUpperCase()}`,r=this.visualElement.getProps(),c=r[u];return c||this.visualElement.getValue(l,(r.initial?r.initial[l]:void 0)||0)}snapToCursor(l){Ce(u=>{const{drag:r}=this.getProps();if(!Gs(u,r,this.currentDirection))return;const{projection:c}=this.visualElement,d=this.getAxisMotionValue(u);if(c&&c.layout){const{min:h,max:g}=c.layout.layoutBox[u];d.set(l[u]-Rt(h,g,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:l,dragConstraints:u}=this.getProps(),{projection:r}=this.visualElement;if(!$a(u)||!r||!this.constraints)return;this.stopAnimation();const c={x:0,y:0};Ce(h=>{const g=this.getAxisMotionValue(h);if(g&&this.constraints!==!1){const p=g.get();c[h]=PT({min:p,max:p},this.constraints[h])}});const{transformTemplate:d}=this.visualElement.getProps();this.visualElement.current.style.transform=d?d({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Ce(h=>{if(!Gs(h,l,null))return;const g=this.getAxisMotionValue(h),{min:p,max:m}=this.constraints[h];g.set(Rt(p,m,c[h]))})}addListeners(){if(!this.visualElement.current)return;WT.set(this.visualElement,this);const l=this.visualElement.current,u=fl(l,"pointerdown",p=>{const{drag:m,dragListener:v=!0}=this.getProps();m&&v&&this.start(p)}),r=()=>{const{dragConstraints:p}=this.getProps();$a(p)&&p.current&&(this.constraints=this.resolveRefConstraints())},{projection:c}=this.visualElement,d=c.addEventListener("measure",r);c&&!c.layout&&(c.root&&c.root.updateScroll(),c.updateLayout()),Dt.read(r);const h=Sl(window,"resize",()=>this.scalePositionWithinConstraints()),g=c.addEventListener("didUpdate",({delta:p,hasLayoutChanged:m})=>{this.isDragging&&m&&(Ce(v=>{const b=this.getAxisMotionValue(v);b&&(this.originPoint[v]+=p[v].translate,b.set(b.get()+p[v].translate))}),this.visualElement.render())});return()=>{h(),u(),d(),g&&g()}}getProps(){const l=this.visualElement.getProps(),{drag:u=!1,dragDirectionLock:r=!1,dragPropagation:c=!1,dragConstraints:d=!1,dragElastic:h=rc,dragMomentum:g=!0}=l;return{...l,drag:u,dragDirectionLock:r,dragPropagation:c,dragConstraints:d,dragElastic:h,dragMomentum:g}}}function Gs(a,l,u){return(l===!0||l===a)&&(u===null||u===a)}function IT(a,l=10){let u=null;return Math.abs(a.y)>l?u="y":Math.abs(a.x)>l&&(u="x"),u}class tx extends qn{constructor(l){super(l),this.removeGroupControls=Ne,this.removeListeners=Ne,this.controls=new $T(l)}mount(){const{dragControls:l}=this.node.getProps();l&&(this.removeGroupControls=l.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Ne}unmount(){this.removeGroupControls(),this.removeListeners()}}const Up=a=>(l,u)=>{a&&Dt.postRender(()=>a(l,u))};class ex extends qn{constructor(){super(...arguments),this.removePointerDownListener=Ne}onPointerDown(l){this.session=new _g(l,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Dg(this.node)})}createPanHandlers(){const{onPanSessionStart:l,onPanStart:u,onPan:r,onPanEnd:c}=this.node.getProps();return{onSessionStart:Up(l),onStart:Up(u),onMove:r,onEnd:(d,h)=>{delete this.session,c&&Dt.postRender(()=>c(d,h))}}}mount(){this.removePointerDownListener=fl(this.node.current,"pointerdown",l=>this.onPointerDown(l))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Ks={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Bp(a,l){return l.max===l.min?0:a/(l.max-l.min)*100}const ul={correct:(a,l)=>{if(!l.target)return a;if(typeof a=="string")if(it.test(a))a=parseFloat(a);else return a;const u=Bp(a,l.target.x),r=Bp(a,l.target.y);return`${u}% ${r}%`}},nx={correct:(a,{treeScale:l,projectionDelta:u})=>{const r=a,c=Hn.parse(a);if(c.length>5)return r;const d=Hn.createTransformer(a),h=typeof c[0]!="number"?1:0,g=u.x.scale*l.x,p=u.y.scale*l.y;c[0+h]/=g,c[1+h]/=p;const m=Rt(g,p,.5);return typeof c[2+h]=="number"&&(c[2+h]/=m),typeof c[3+h]=="number"&&(c[3+h]/=m),d(c)}};let Lo=!1;class ax extends K.Component{componentDidMount(){const{visualElement:l,layoutGroup:u,switchLayoutGroup:r,layoutId:c}=this.props,{projection:d}=l;Mb(ix),d&&(u.group&&u.group.add(d),r&&r.register&&c&&r.register(d),Lo&&d.root.didUpdate(),d.addEventListener("animationComplete",()=>{this.safeToRemove()}),d.setOptions({...d.options,onExitComplete:()=>this.safeToRemove()})),Ks.hasEverUpdated=!0}getSnapshotBeforeUpdate(l){const{layoutDependency:u,visualElement:r,drag:c,isPresent:d}=this.props,{projection:h}=r;return h&&(h.isPresent=d,Lo=!0,c||l.layoutDependency!==u||u===void 0||l.isPresent!==d?h.willUpdate():this.safeToRemove(),l.isPresent!==d&&(d?h.promote():h.relegate()||Dt.postRender(()=>{const g=h.getStack();(!g||!g.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:l}=this.props.visualElement;l&&(l.root.didUpdate(),Vc.postRender(()=>{!l.currentAnimation&&l.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:l,layoutGroup:u,switchLayoutGroup:r}=this.props,{projection:c}=l;Lo=!0,c&&(c.scheduleCheckAfterUnmount(),u&&u.group&&u.group.remove(c),r&&r.deregister&&r.deregister(c))}safeToRemove(){const{safeToRemove:l}=this.props;l&&l()}render(){return null}}function Og(a){const[l,u]=Wy(),r=K.useContext(fc);return x.jsx(ax,{...a,layoutGroup:r,switchLayoutGroup:K.useContext(og),isPresent:l,safeToRemove:u})}const ix={borderRadius:{...ul,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ul,borderTopRightRadius:ul,borderBottomLeftRadius:ul,borderBottomRightRadius:ul,boxShadow:nx};function lx(a,l,u){const r=ee(a)?a:ai(a);return r.start(Zc("",r,l,u)),r.animation}const sx=(a,l)=>a.depth-l.depth;class ux{constructor(){this.children=[],this.isDirty=!1}add(l){mc(this.children,l),this.isDirty=!0}remove(l){pc(this.children,l),this.isDirty=!0}forEach(l){this.isDirty&&this.children.sort(sx),this.isDirty=!1,this.children.forEach(l)}}function rx(a,l){const u=oe.now(),r=({timestamp:c})=>{const d=c-u;d>=l&&(Ln(r),a(d-l))};return Dt.setup(r,!0),()=>Ln(r)}const Cg=["TopLeft","TopRight","BottomLeft","BottomRight"],ox=Cg.length,wp=a=>typeof a=="string"?parseFloat(a):a,Lp=a=>typeof a=="number"||it.test(a);function cx(a,l,u,r,c,d){c?(a.opacity=Rt(0,u.opacity??1,fx(r)),a.opacityExit=Rt(l.opacity??1,0,hx(r))):d&&(a.opacity=Rt(l.opacity??1,u.opacity??1,r));for(let h=0;h<ox;h++){const g=`border${Cg[h]}Radius`;let p=Hp(l,g),m=Hp(u,g);if(p===void 0&&m===void 0)continue;p||(p=0),m||(m=0),p===0||m===0||Lp(p)===Lp(m)?(a[g]=Math.max(Rt(wp(p),wp(m),r),0),(Qe.test(m)||Qe.test(p))&&(a[g]+="%")):a[g]=m}(l.rotate||u.rotate)&&(a.rotate=Rt(l.rotate||0,u.rotate||0,r))}function Hp(a,l){return a[l]!==void 0?a[l]:a.borderRadius}const fx=Ng(0,.5,gy),hx=Ng(.5,.95,Ne);function Ng(a,l,u){return r=>r<a?0:r>l?1:u(ml(a,l,r))}function qp(a,l){a.min=l.min,a.max=l.max}function Oe(a,l){qp(a.x,l.x),qp(a.y,l.y)}function Yp(a,l){a.translate=l.translate,a.scale=l.scale,a.originPoint=l.originPoint,a.origin=l.origin}function Gp(a,l,u,r,c){return a-=l,a=$s(a,1/u,r),c!==void 0&&(a=$s(a,1/c,r)),a}function dx(a,l=0,u=1,r=.5,c,d=a,h=a){if(Qe.test(l)&&(l=parseFloat(l),l=Rt(h.min,h.max,l/100)-h.min),typeof l!="number")return;let g=Rt(d.min,d.max,r);a===d&&(g-=l),a.min=Gp(a.min,l,u,g,c),a.max=Gp(a.max,l,u,g,c)}function Xp(a,l,[u,r,c],d,h){dx(a,l[u],l[r],l[c],l.scale,d,h)}const mx=["x","scaleX","originX"],px=["y","scaleY","originY"];function Zp(a,l,u,r){Xp(a.x,l,mx,u?u.x:void 0,r?r.x:void 0),Xp(a.y,l,px,u?u.y:void 0,r?r.y:void 0)}function Qp(a){return a.translate===0&&a.scale===1}function jg(a){return Qp(a.x)&&Qp(a.y)}function Kp(a,l){return a.min===l.min&&a.max===l.max}function yx(a,l){return Kp(a.x,l.x)&&Kp(a.y,l.y)}function kp(a,l){return Math.round(a.min)===Math.round(l.min)&&Math.round(a.max)===Math.round(l.max)}function Vg(a,l){return kp(a.x,l.x)&&kp(a.y,l.y)}function Pp(a){return ae(a.x)/ae(a.y)}function Jp(a,l){return a.translate===l.translate&&a.scale===l.scale&&a.originPoint===l.originPoint}class gx{constructor(){this.members=[]}add(l){mc(this.members,l),l.scheduleRender()}remove(l){if(pc(this.members,l),l===this.prevLead&&(this.prevLead=void 0),l===this.lead){const u=this.members[this.members.length-1];u&&this.promote(u)}}relegate(l){const u=this.members.findIndex(c=>l===c);if(u===0)return!1;let r;for(let c=u;c>=0;c--){const d=this.members[c];if(d.isPresent!==!1){r=d;break}}return r?(this.promote(r),!0):!1}promote(l,u){const r=this.lead;if(l!==r&&(this.prevLead=r,this.lead=l,l.show(),r)){r.instance&&r.scheduleRender(),l.scheduleRender(),l.resumeFrom=r,u&&(l.resumeFrom.preserveOpacity=!0),r.snapshot&&(l.snapshot=r.snapshot,l.snapshot.latestValues=r.animationValues||r.latestValues),l.root&&l.root.isUpdating&&(l.isLayoutDirty=!0);const{crossfade:c}=l.options;c===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(l=>{const{options:u,resumingFrom:r}=l;u.onExitComplete&&u.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(l=>{l.instance&&l.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function vx(a,l,u){let r="";const c=a.x.translate/l.x,d=a.y.translate/l.y,h=u?.z||0;if((c||d||h)&&(r=`translate3d(${c}px, ${d}px, ${h}px) `),(l.x!==1||l.y!==1)&&(r+=`scale(${1/l.x}, ${1/l.y}) `),u){const{transformPerspective:m,rotate:v,rotateX:b,rotateY:E,skewX:j,skewY:B}=u;m&&(r=`perspective(${m}px) ${r}`),v&&(r+=`rotate(${v}deg) `),b&&(r+=`rotateX(${b}deg) `),E&&(r+=`rotateY(${E}deg) `),j&&(r+=`skewX(${j}deg) `),B&&(r+=`skewY(${B}deg) `)}const g=a.x.scale*l.x,p=a.y.scale*l.y;return(g!==1||p!==1)&&(r+=`scale(${g}, ${p})`),r||"none"}const Ho=["","X","Y","Z"],Sx=1e3;let bx=0;function qo(a,l,u,r){const{latestValues:c}=l;c[a]&&(u[a]=c[a],l.setStaticValue(a,0),r&&(r[a]=0))}function zg(a){if(a.hasCheckedOptimisedAppear=!0,a.root===a)return;const{visualElement:l}=a.options;if(!l)return;const u=Sg(l);if(window.MotionHasOptimisedAnimation(u,"transform")){const{layout:c,layoutId:d}=a.options;window.MotionCancelOptimisedAnimation(u,"transform",Dt,!(c||d))}const{parent:r}=a;r&&!r.hasCheckedOptimisedAppear&&zg(r)}function Ug({attachResizeListener:a,defaultParent:l,measureScroll:u,checkIsScrollRoot:r,resetTransform:c}){return class{constructor(h={},g=l?.()){this.id=bx++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(Ax),this.nodes.forEach(_x),this.nodes.forEach(Rx),this.nodes.forEach(Ex)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=h,this.root=g?g.root||g:this,this.path=g?[...g.path,g]:[],this.parent=g,this.depth=g?g.depth+1:0;for(let p=0;p<this.path.length;p++)this.path[p].shouldResetTransform=!0;this.root===this&&(this.nodes=new ux)}addEventListener(h,g){return this.eventHandlers.has(h)||this.eventHandlers.set(h,new vc),this.eventHandlers.get(h).add(g)}notifyListeners(h,...g){const p=this.eventHandlers.get(h);p&&p.notify(...g)}hasListeners(h){return this.eventHandlers.has(h)}mount(h){if(this.instance)return;this.isSVG=Fy(h)&&!fb(h),this.instance=h;const{layoutId:g,layout:p,visualElement:m}=this.options;if(m&&!m.current&&m.mount(h),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(p||g)&&(this.isLayoutDirty=!0),a){let v,b=0;const E=()=>this.root.updateBlockedByResize=!1;Dt.read(()=>{b=window.innerWidth}),a(h,()=>{const j=window.innerWidth;j!==b&&(b=j,this.root.updateBlockedByResize=!0,v&&v(),v=rx(E,250),Ks.hasAnimatedSinceResize&&(Ks.hasAnimatedSinceResize=!1,this.nodes.forEach($p)))})}g&&this.root.registerSharedNode(g,this),this.options.animate!==!1&&m&&(g||p)&&this.addEventListener("didUpdate",({delta:v,hasLayoutChanged:b,hasRelativeLayoutChanged:E,layout:j})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const B=this.options.transition||m.getDefaultTransition()||Vx,{onLayoutAnimationStart:q,onLayoutAnimationComplete:X}=m.getProps(),Y=!this.targetLayout||!Vg(this.targetLayout,j),Z=!b&&E;if(this.options.layoutRoot||this.resumeFrom||Z||b&&(Y||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);const H={...Nc(B,"layout"),onPlay:q,onComplete:X};(m.shouldReduceMotion||this.options.layoutRoot)&&(H.delay=0,H.type=!1),this.startAnimation(H),this.setAnimationOrigin(v,Z)}else b||$p(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=j})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const h=this.getStack();h&&h.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),Ln(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Ox),this.animationId++)}getTransformTemplate(){const{visualElement:h}=this.options;return h&&h.getProps().transformTemplate}willUpdate(h=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&zg(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let v=0;v<this.path.length;v++){const b=this.path[v];b.shouldResetTransform=!0,b.updateScroll("snapshot"),b.options.layoutRoot&&b.willUpdate(!1)}const{layoutId:g,layout:p}=this.options;if(g===void 0&&!p)return;const m=this.getTransformTemplate();this.prevTransformTemplateValue=m?m(this.latestValues,""):void 0,this.updateSnapshot(),h&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Fp);return}if(this.animationId<=this.animationCommitId){this.nodes.forEach(Wp);return}this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(Dx),this.nodes.forEach(Tx),this.nodes.forEach(xx)):this.nodes.forEach(Wp),this.clearAllSnapshots();const g=oe.now();Wt.delta=cn(0,1e3/60,g-Wt.timestamp),Wt.timestamp=g,Wt.isProcessing=!0,Ro.update.process(Wt),Ro.preRender.process(Wt),Ro.render.process(Wt),Wt.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Vc.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Mx),this.sharedNodes.forEach(Cx)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Dt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Dt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!ae(this.snapshot.measuredBox.x)&&!ae(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let p=0;p<this.path.length;p++)this.path[p].updateScroll();const h=this.layout;this.layout=this.measure(!1),this.layoutCorrected=Ut(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:g}=this.options;g&&g.notify("LayoutMeasure",this.layout.layoutBox,h?h.layoutBox:void 0)}updateScroll(h="measure"){let g=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===h&&(g=!1),g&&this.instance){const p=r(this.instance);this.scroll={animationId:this.root.animationId,phase:h,isRoot:p,offset:u(this.instance),wasRoot:this.scroll?this.scroll.isRoot:p}}}resetTransform(){if(!c)return;const h=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,g=this.projectionDelta&&!jg(this.projectionDelta),p=this.getTransformTemplate(),m=p?p(this.latestValues,""):void 0,v=m!==this.prevTransformTemplateValue;h&&this.instance&&(g||sa(this.latestValues)||v)&&(c(this.instance,m),this.shouldResetTransform=!1,this.scheduleRender())}measure(h=!0){const g=this.measurePageBox();let p=this.removeElementScroll(g);return h&&(p=this.removeTransform(p)),zx(p),{animationId:this.root.animationId,measuredBox:g,layoutBox:p,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:h}=this.options;if(!h)return Ut();const g=h.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(Ux))){const{scroll:m}=this.root;m&&(Ia(g.x,m.offset.x),Ia(g.y,m.offset.y))}return g}removeElementScroll(h){const g=Ut();if(Oe(g,h),this.scroll?.wasRoot)return g;for(let p=0;p<this.path.length;p++){const m=this.path[p],{scroll:v,options:b}=m;m!==this.root&&v&&b.layoutScroll&&(v.wasRoot&&Oe(g,h),Ia(g.x,v.offset.x),Ia(g.y,v.offset.y))}return g}applyTransform(h,g=!1){const p=Ut();Oe(p,h);for(let m=0;m<this.path.length;m++){const v=this.path[m];!g&&v.options.layoutScroll&&v.scroll&&v!==v.root&&ti(p,{x:-v.scroll.offset.x,y:-v.scroll.offset.y}),sa(v.latestValues)&&ti(p,v.latestValues)}return sa(this.latestValues)&&ti(p,this.latestValues),p}removeTransform(h){const g=Ut();Oe(g,h);for(let p=0;p<this.path.length;p++){const m=this.path[p];if(!m.instance||!sa(m.latestValues))continue;nc(m.latestValues)&&m.updateSnapshot();const v=Ut(),b=m.measurePageBox();Oe(v,b),Zp(g,m.latestValues,m.snapshot?m.snapshot.layoutBox:void 0,v)}return sa(this.latestValues)&&Zp(g,this.latestValues),g}setTargetDelta(h){this.targetDelta=h,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(h){this.options={...this.options,...h,crossfade:h.crossfade!==void 0?h.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Wt.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(h=!1){const g=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=g.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=g.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=g.isSharedProjectionDirty);const p=!!this.resumingFrom||this!==g;if(!(h||p&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:v,layoutId:b}=this.options;if(!(!this.layout||!(v||b))){if(this.resolvedRelativeTargetAt=Wt.timestamp,!this.targetDelta&&!this.relativeTarget){const E=this.getClosestProjectingParent();E&&E.layout&&this.animationProgress!==1?(this.relativeParent=E,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Ut(),this.relativeTargetOrigin=Ut(),dl(this.relativeTargetOrigin,this.layout.layoutBox,E.layout.layoutBox),Oe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=Ut(),this.targetWithTransforms=Ut()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),YT(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Oe(this.target,this.layout.layoutBox),dg(this.target,this.targetDelta)):Oe(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const E=this.getClosestProjectingParent();E&&!!E.resumingFrom==!!this.resumingFrom&&!E.options.layoutScroll&&E.target&&this.animationProgress!==1?(this.relativeParent=E,this.forceRelativeParentToResolveTarget(),this.relativeTarget=Ut(),this.relativeTargetOrigin=Ut(),dl(this.relativeTargetOrigin,this.target,E.target),Oe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||nc(this.parent.latestValues)||hg(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){const h=this.getLead(),g=!!this.resumingFrom||this!==h;let p=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(p=!1),g&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(p=!1),this.resolvedRelativeTargetAt===Wt.timestamp&&(p=!1),p)return;const{layout:m,layoutId:v}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(m||v))return;Oe(this.layoutCorrected,this.layout.layoutBox);const b=this.treeScale.x,E=this.treeScale.y;Ib(this.layoutCorrected,this.treeScale,this.path,g),h.layout&&!h.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(h.target=h.layout.layoutBox,h.targetWithTransforms=Ut());const{target:j}=h;if(!j){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Yp(this.prevProjectionDelta.x,this.projectionDelta.x),Yp(this.prevProjectionDelta.y,this.projectionDelta.y)),hl(this.projectionDelta,this.layoutCorrected,j,this.latestValues),(this.treeScale.x!==b||this.treeScale.y!==E||!Jp(this.projectionDelta.x,this.prevProjectionDelta.x)||!Jp(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",j))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(h=!0){if(this.options.visualElement?.scheduleRender(),h){const g=this.getStack();g&&g.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ei(),this.projectionDelta=ei(),this.projectionDeltaWithTransform=ei()}setAnimationOrigin(h,g=!1){const p=this.snapshot,m=p?p.latestValues:{},v={...this.latestValues},b=ei();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!g;const E=Ut(),j=p?p.source:void 0,B=this.layout?this.layout.source:void 0,q=j!==B,X=this.getStack(),Y=!X||X.members.length<=1,Z=!!(q&&!Y&&this.options.crossfade===!0&&!this.path.some(jx));this.animationProgress=0;let H;this.mixTargetDelta=et=>{const L=et/1e3;Ip(b.x,h.x,L),Ip(b.y,h.y,L),this.setTargetDelta(b),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(dl(E,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Nx(this.relativeTarget,this.relativeTargetOrigin,E,L),H&&yx(this.relativeTarget,H)&&(this.isProjectionDirty=!1),H||(H=Ut()),Oe(H,this.relativeTarget)),q&&(this.animationValues=v,cx(v,m,this.latestValues,L,Z,Y)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=L},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(h){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(Ln(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Dt.update(()=>{Ks.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=ai(0)),this.currentAnimation=lx(this.motionValue,[0,1e3],{...h,velocity:0,isSync:!0,onUpdate:g=>{this.mixTargetDelta(g),h.onUpdate&&h.onUpdate(g)},onStop:()=>{},onComplete:()=>{h.onComplete&&h.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const h=this.getStack();h&&h.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Sx),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const h=this.getLead();let{targetWithTransforms:g,target:p,layout:m,latestValues:v}=h;if(!(!g||!p||!m)){if(this!==h&&this.layout&&m&&Bg(this.options.animationType,this.layout.layoutBox,m.layoutBox)){p=this.target||Ut();const b=ae(this.layout.layoutBox.x);p.x.min=h.target.x.min,p.x.max=p.x.min+b;const E=ae(this.layout.layoutBox.y);p.y.min=h.target.y.min,p.y.max=p.y.min+E}Oe(g,p),ti(g,v),hl(this.projectionDeltaWithTransform,this.layoutCorrected,g,v)}}registerSharedNode(h,g){this.sharedNodes.has(h)||this.sharedNodes.set(h,new gx),this.sharedNodes.get(h).add(g);const m=g.options.initialPromotionConfig;g.promote({transition:m?m.transition:void 0,preserveFollowOpacity:m&&m.shouldPreserveFollowOpacity?m.shouldPreserveFollowOpacity(g):void 0})}isLead(){const h=this.getStack();return h?h.lead===this:!0}getLead(){const{layoutId:h}=this.options;return h?this.getStack()?.lead||this:this}getPrevLead(){const{layoutId:h}=this.options;return h?this.getStack()?.prevLead:void 0}getStack(){const{layoutId:h}=this.options;if(h)return this.root.sharedNodes.get(h)}promote({needsReset:h,transition:g,preserveFollowOpacity:p}={}){const m=this.getStack();m&&m.promote(this,p),h&&(this.projectionDelta=void 0,this.needsReset=!0),g&&this.setOptions({transition:g})}relegate(){const h=this.getStack();return h?h.relegate(this):!1}resetSkewAndRotation(){const{visualElement:h}=this.options;if(!h)return;let g=!1;const{latestValues:p}=h;if((p.z||p.rotate||p.rotateX||p.rotateY||p.rotateZ||p.skewX||p.skewY)&&(g=!0),!g)return;const m={};p.z&&qo("z",h,m,this.animationValues);for(let v=0;v<Ho.length;v++)qo(`rotate${Ho[v]}`,h,m,this.animationValues),qo(`skew${Ho[v]}`,h,m,this.animationValues);h.render();for(const v in m)h.setStaticValue(v,m[v]),this.animationValues&&(this.animationValues[v]=m[v]);h.scheduleRender()}applyProjectionStyles(h,g){if(!this.instance||this.isSVG)return;if(!this.isVisible){h.visibility="hidden";return}const p=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,h.visibility="",h.opacity="",h.pointerEvents=Qs(g?.pointerEvents)||"",h.transform=p?p(this.latestValues,""):"none";return}const m=this.getLead();if(!this.projectionDelta||!this.layout||!m.target){this.options.layoutId&&(h.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,h.pointerEvents=Qs(g?.pointerEvents)||""),this.hasProjected&&!sa(this.latestValues)&&(h.transform=p?p({},""):"none",this.hasProjected=!1);return}h.visibility="";const v=m.animationValues||m.latestValues;this.applyTransformsToTarget();let b=vx(this.projectionDeltaWithTransform,this.treeScale,v);p&&(b=p(v,b)),h.transform=b;const{x:E,y:j}=this.projectionDelta;h.transformOrigin=`${E.origin*100}% ${j.origin*100}% 0`,m.animationValues?h.opacity=m===this?v.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:v.opacityExit:h.opacity=m===this?v.opacity!==void 0?v.opacity:"":v.opacityExit!==void 0?v.opacityExit:0;for(const B in vl){if(v[B]===void 0)continue;const{correct:q,applyTo:X,isCSSVariable:Y}=vl[B],Z=b==="none"?v[B]:q(v[B],m);if(X){const H=X.length;for(let et=0;et<H;et++)h[X[et]]=Z}else Y?this.options.visualElement.renderState.vars[B]=Z:h[B]=Z}this.options.layoutId&&(h.pointerEvents=m===this?Qs(g?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(h=>h.currentAnimation?.stop()),this.root.nodes.forEach(Fp),this.root.sharedNodes.clear()}}}function Tx(a){a.updateLayout()}function xx(a){const l=a.resumeFrom?.snapshot||a.snapshot;if(a.isLead()&&a.layout&&l&&a.hasListeners("didUpdate")){const{layoutBox:u,measuredBox:r}=a.layout,{animationType:c}=a.options,d=l.source!==a.layout.source;c==="size"?Ce(v=>{const b=d?l.measuredBox[v]:l.layoutBox[v],E=ae(b);b.min=u[v].min,b.max=b.min+E}):Bg(c,l.layoutBox,u)&&Ce(v=>{const b=d?l.measuredBox[v]:l.layoutBox[v],E=ae(u[v]);b.max=b.min+E,a.relativeTarget&&!a.currentAnimation&&(a.isProjectionDirty=!0,a.relativeTarget[v].max=a.relativeTarget[v].min+E)});const h=ei();hl(h,u,l.layoutBox);const g=ei();d?hl(g,a.applyTransform(r,!0),l.measuredBox):hl(g,u,l.layoutBox);const p=!jg(h);let m=!1;if(!a.resumeFrom){const v=a.getClosestProjectingParent();if(v&&!v.resumeFrom){const{snapshot:b,layout:E}=v;if(b&&E){const j=Ut();dl(j,l.layoutBox,b.layoutBox);const B=Ut();dl(B,u,E.layoutBox),Vg(j,B)||(m=!0),v.options.layoutRoot&&(a.relativeTarget=B,a.relativeTargetOrigin=j,a.relativeParent=v)}}}a.notifyListeners("didUpdate",{layout:u,snapshot:l,delta:g,layoutDelta:h,hasLayoutChanged:p,hasRelativeLayoutChanged:m})}else if(a.isLead()){const{onExitComplete:u}=a.options;u&&u()}a.options.transition=void 0}function Ax(a){a.parent&&(a.isProjecting()||(a.isProjectionDirty=a.parent.isProjectionDirty),a.isSharedProjectionDirty||(a.isSharedProjectionDirty=!!(a.isProjectionDirty||a.parent.isProjectionDirty||a.parent.isSharedProjectionDirty)),a.isTransformDirty||(a.isTransformDirty=a.parent.isTransformDirty))}function Ex(a){a.isProjectionDirty=a.isSharedProjectionDirty=a.isTransformDirty=!1}function Mx(a){a.clearSnapshot()}function Fp(a){a.clearMeasurements()}function Wp(a){a.isLayoutDirty=!1}function Dx(a){const{visualElement:l}=a.options;l&&l.getProps().onBeforeLayoutMeasure&&l.notify("BeforeLayoutMeasure"),a.resetTransform()}function $p(a){a.finishAnimation(),a.targetDelta=a.relativeTarget=a.target=void 0,a.isProjectionDirty=!0}function _x(a){a.resolveTargetDelta()}function Rx(a){a.calcProjection()}function Ox(a){a.resetSkewAndRotation()}function Cx(a){a.removeLeadSnapshot()}function Ip(a,l,u){a.translate=Rt(l.translate,0,u),a.scale=Rt(l.scale,1,u),a.origin=l.origin,a.originPoint=l.originPoint}function ty(a,l,u,r){a.min=Rt(l.min,u.min,r),a.max=Rt(l.max,u.max,r)}function Nx(a,l,u,r){ty(a.x,l.x,u.x,r),ty(a.y,l.y,u.y,r)}function jx(a){return a.animationValues&&a.animationValues.opacityExit!==void 0}const Vx={duration:.45,ease:[.4,0,.1,1]},ey=a=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(a),ny=ey("applewebkit/")&&!ey("chrome/")?Math.round:Ne;function ay(a){a.min=ny(a.min),a.max=ny(a.max)}function zx(a){ay(a.x),ay(a.y)}function Bg(a,l,u){return a==="position"||a==="preserve-aspect"&&!qT(Pp(l),Pp(u),.2)}function Ux(a){return a!==a.root&&a.scroll?.wasRoot}const Bx=Ug({attachResizeListener:(a,l)=>Sl(a,"resize",l),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Yo={current:void 0},wg=Ug({measureScroll:a=>({x:a.scrollLeft,y:a.scrollTop}),defaultParent:()=>{if(!Yo.current){const a=new Bx({});a.mount(window),a.setOptions({layoutScroll:!0}),Yo.current=a}return Yo.current},resetTransform:(a,l)=>{a.style.transform=l!==void 0?l:"none"},checkIsScrollRoot:a=>window.getComputedStyle(a).position==="fixed"}),wx={pan:{Feature:ex},drag:{Feature:tx,ProjectionNode:wg,MeasureLayout:Og}};function iy(a,l,u){const{props:r}=a;a.animationState&&r.whileHover&&a.animationState.setActive("whileHover",u==="Start");const c="onHover"+u,d=r[c];d&&Dt.postRender(()=>d(l,Al(l)))}class Lx extends qn{mount(){const{current:l}=this.node;l&&(this.unmount=sb(l,(u,r)=>(iy(this.node,r,"Start"),c=>iy(this.node,c,"End"))))}unmount(){}}class Hx extends qn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let l=!1;try{l=this.node.current.matches(":focus-visible")}catch{l=!0}!l||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=bl(Sl(this.node.current,"focus",()=>this.onFocus()),Sl(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function ly(a,l,u){const{props:r}=a;if(a.current instanceof HTMLButtonElement&&a.current.disabled)return;a.animationState&&r.whileTap&&a.animationState.setActive("whileTap",u==="Start");const c="onTap"+(u==="End"?"":u),d=r[c];d&&Dt.postRender(()=>d(l,Al(l)))}class qx extends qn{mount(){const{current:l}=this.node;l&&(this.unmount=cb(l,(u,r)=>(ly(this.node,r,"Start"),(c,{success:d})=>ly(this.node,c,d?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const oc=new WeakMap,Go=new WeakMap,Yx=a=>{const l=oc.get(a.target);l&&l(a)},Gx=a=>{a.forEach(Yx)};function Xx({root:a,...l}){const u=a||document;Go.has(u)||Go.set(u,{});const r=Go.get(u),c=JSON.stringify(l);return r[c]||(r[c]=new IntersectionObserver(Gx,{root:a,...l})),r[c]}function Zx(a,l,u){const r=Xx(l);return oc.set(a,u),r.observe(a),()=>{oc.delete(a),r.unobserve(a)}}const Qx={some:0,all:1};class Kx extends qn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:l={}}=this.node.getProps(),{root:u,margin:r,amount:c="some",once:d}=l,h={root:u?u.current:void 0,rootMargin:r,threshold:typeof c=="number"?c:Qx[c]},g=p=>{const{isIntersecting:m}=p;if(this.isInView===m||(this.isInView=m,d&&!m&&this.hasEnteredView))return;m&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",m);const{onViewportEnter:v,onViewportLeave:b}=this.node.getProps(),E=m?v:b;E&&E(p)};return Zx(this.node.current,h,g)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:l,prevProps:u}=this.node;["amount","margin","root"].some(kx(l,u))&&this.startObserver()}unmount(){}}function kx({viewport:a={}},{viewport:l={}}={}){return u=>a[u]!==l[u]}const Px={inView:{Feature:Kx},tap:{Feature:qx},focus:{Feature:Hx},hover:{Feature:Lx}},Jx={layout:{ProjectionNode:wg,MeasureLayout:Og}},Fx={...zT,...Px,...wx,...Jx},Ke=Fb(Fx,oT),au=()=>{const[a,l]=K.useState(!1),[u,r]=K.useState(!1);K.useEffect(()=>{const p=()=>{const m=window.scrollY>64;l(m)};return window.addEventListener("scroll",p),()=>window.removeEventListener("scroll",p)},[]);const c=()=>{r(!u)},d=()=>{r(!1)},h=[{name:"首页",href:"/"},{name:"文章",href:"/article"},{name:"关于",href:"/about"},{name:"联系",href:"/contact"}],g=(p,m)=>{p.preventDefault(),window.history.pushState({},"",m),window.dispatchEvent(new PopStateEvent("popstate")),d()};return x.jsxs(x.Fragment,{children:[x.jsx(Ke.header,{className:`header ${a?"header--scrolled":""}`,initial:{y:0},animate:{y:a?-64:0},transition:{duration:.3,ease:[.25,.46,.45,.94]},children:x.jsxs("div",{className:"header__container",children:[x.jsx("div",{className:"header__logo",children:x.jsx("a",{href:"/",className:"header__logo-link",onClick:p=>g(p,"/"),children:"墨韵流光"})}),x.jsx("nav",{className:"header__nav header__nav--desktop",children:x.jsx("ul",{className:"header__nav-list",children:h.map(p=>x.jsx("li",{className:"header__nav-item",children:x.jsx("a",{href:p.href,className:"header__nav-link",onClick:m=>g(m,p.href),children:p.name})},p.name))})}),x.jsx("button",{className:"header__mobile-toggle",onClick:c,"aria-label":"切换菜单",children:x.jsxs("span",{className:"header__hamburger",children:[x.jsx("span",{}),x.jsx("span",{}),x.jsx("span",{})]})})]})}),x.jsx(vb,{children:u&&x.jsxs(x.Fragment,{children:[x.jsx(Ke.div,{className:"header__mobile-overlay",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3},onClick:d}),x.jsx(Ke.nav,{className:"header__mobile-menu",initial:{x:"100%"},animate:{x:0},exit:{x:"100%"},transition:{duration:.3,ease:[.25,.46,.45,.94]},children:x.jsx("ul",{className:"header__mobile-nav-list",children:h.map(p=>x.jsx("li",{className:"header__mobile-nav-item",children:x.jsx("a",{href:p.href,className:"header__mobile-nav-link",onClick:m=>g(m,p.href),children:p.name})},p.name))})})]})})]})};function Lg(a){var l,u,r="";if(typeof a=="string"||typeof a=="number")r+=a;else if(typeof a=="object")if(Array.isArray(a)){var c=a.length;for(l=0;l<c;l++)a[l]&&(u=Lg(a[l]))&&(r&&(r+=" "),r+=u)}else for(u in a)a[u]&&(r&&(r+=" "),r+=u);return r}function iu(){for(var a,l,u=0,r="",c=arguments.length;u<c;u++)(a=arguments[u])&&(l=Lg(a))&&(r&&(r+=" "),r+=l);return r}const oa=({children:a,href:l,external:u=!1,underline:r=!0,className:c,...d})=>{const h=iu("link",{"link--no-underline":!r,"link--external":u},c),g={href:l,className:h,...u&&{target:"_blank",rel:"noopener noreferrer"},...d};return x.jsxs(Ke.a,{...g,whileHover:"hover",initial:"initial",variants:{initial:{},hover:{}},children:[x.jsx("span",{className:"link__text",children:a}),r&&x.jsx(Ke.span,{className:"link__underline",variants:{initial:{scaleX:0},hover:{scaleX:1}},transition:{duration:.2,ease:[.25,.46,.45,.94]}}),u&&x.jsx("span",{className:"link__external-icon","aria-hidden":"true",children:"↗"})]})},lu=()=>{const a=new Date().getFullYear(),l=[{name:"微博",href:"#",icon:"微"},{name:"知乎",href:"#",icon:"知"},{name:"GitHub",href:"#",icon:"G"},{name:"邮箱",href:"mailto:<EMAIL>",icon:"@"}];return x.jsx("footer",{className:"footer",children:x.jsxs("div",{className:"footer__container",children:[x.jsxs("div",{className:"footer__content",children:[x.jsx("div",{className:"footer__copyright",children:x.jsxs("p",{children:["© ",a," 墨韵流光. 保留所有权利."]})}),x.jsx("div",{className:"footer__social",children:x.jsx("ul",{className:"footer__social-list",children:l.map(u=>x.jsx("li",{className:"footer__social-item",children:x.jsxs(oa,{href:u.href,external:u.href.startsWith("http")||u.href.startsWith("mailto"),underline:!1,className:"footer__social-link","aria-label":u.name,children:[x.jsx("span",{className:"footer__social-icon",children:u.icon}),x.jsx("span",{className:"footer__social-name",children:u.name})]})},u.name))})}),x.jsx("div",{className:"footer__legal",children:x.jsx("p",{children:x.jsx(oa,{href:"#",underline:!1,className:"footer__legal-link",children:"京ICP备12345678号-1"})})})]}),x.jsx("div",{className:"footer__separator",children:x.jsx("span",{children:"· · ·"})})]})})},Wx=({title:a,excerpt:l,date:u,readTime:r,image:c,href:d,size:h="normal"})=>x.jsx(Ke.article,{className:`article-card article-card--${h}`,whileHover:{y:-4,transition:{duration:.25,ease:[.25,.46,.45,.94]}},children:x.jsxs("a",{href:d,className:"article-card__link",children:[c&&x.jsxs("div",{className:"article-card__image-container",children:[x.jsx(Ke.img,{src:c,alt:a,className:"article-card__image",whileHover:{scale:1.03,transition:{duration:.25,ease:[.25,.46,.45,.94]}}}),x.jsx("div",{className:"article-card__image-overlay"})]}),x.jsxs("div",{className:"article-card__content",children:[x.jsx("h3",{className:"article-card__title",children:a}),l&&x.jsx("p",{className:"article-card__excerpt",children:l}),x.jsxs("div",{className:"article-card__meta",children:[u&&x.jsx("time",{className:"article-card__date",dateTime:u,children:new Date(u).toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"})}),r&&x.jsxs(x.Fragment,{children:[x.jsx("span",{className:"article-card__meta-separator",children:"·"}),x.jsxs("span",{className:"article-card__read-time",children:[r," 分钟阅读"]})]})]})]})]})}),ks=({children:a,variant:l="primary",size:u="medium",disabled:r=!1,onClick:c,href:d,className:h,...g})=>{const m={className:iu("button",`button--${l}`,`button--${u}`,{"button--disabled":r},h),onClick:r?void 0:c,disabled:r,...g},v=x.jsx(Ke.span,{className:"button__content",whileTap:{scale:.98},transition:{duration:.1},children:a});return d&&!r?x.jsx(Ke.a,{href:d,...m,whileHover:{scale:1.02},whileTap:{scale:.98},transition:{duration:.15,ease:[.25,.46,.45,.94]},children:v}):x.jsx(Ke.button,{...m,whileHover:r?{}:{scale:1.02},whileTap:r?{}:{scale:.98},transition:{duration:.15,ease:[.25,.46,.45,.94]},children:v})},Hg=[{id:1,title:"春日里的诗意时光",excerpt:"春天来了，万物复苏，心中涌起无限的诗意。在这个美好的季节里，让我们一起感受生活的美好，体验文字的力量。",date:"2024-03-15",readTime:5,image:"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop",href:"/articles/spring-poetry",size:"large",author:"墨韵",tags:["春天","诗意","生活感悟"],coverImage:"https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1200&h=600&fit=crop",content:`
      <p>春天来了，万物复苏，心中涌起无限的诗意。在这个美好的季节里，让我们一起感受生活的美好，体验文字的力量。</p>
      
      <p>阳光明媚的午后，坐在窗前，看着窗外的花草树木，感受着微风拂面的清新，心情也随之舒畅起来。这样的时刻，最适合捧起一本书，或者提笔写下内心的感受。</p>
      
      <h2>春天的色彩</h2>
      
      <p>春天是一个色彩斑斓的季节。嫩绿的新叶，粉红的桃花，金黄的迎春花，还有蓝天白云，构成了一幅美丽的画卷。这些色彩不仅仅是视觉上的享受，更是心灵的洗礼。</p>
      
      <p>在这样的季节里，我喜欢拿起相机，记录下这些美好的瞬间。每一张照片都是一个故事，一段回忆，一份感动。</p>
      
      <blockquote>
        <p>春天是大地的诗篇，每一个细节都值得我们去品味，去感受。</p>
        <footer>
          <cite>— 林清玄</cite>
        </footer>
      </blockquote>
      
      <h2>春日的声音</h2>
      
      <p>春天不仅有色彩，还有声音。鸟儿的鸣叫，溪水的潺潺，风吹过树叶的沙沙声，这些都是春天的声音。闭上眼睛，静静地聆听，你会发现，这些声音是如此的和谐，如此的美妙。</p>
      
      <p>有时候，我会带上一本书，找一个安静的角落，伴随着这些自然的声音，沉浸在阅读的世界里。这样的时光，是最为宝贵的。</p>
      
      <h2>春天的味道</h2>
      
      <p>春天还有独特的味道。新鲜的空气，刚刚萌发的嫩芽，盛开的花朵，这些都有着春天特有的气息。深呼吸，让这些气息充满肺腑，你会感到一种前所未有的清新和活力。</p>
      
      <p>在春天，我喜欢做的一件事就是泡一杯花茶，坐在阳台上，看书或者写作。茶香与春风交织，创造出一种独特的氛围，让人心旷神怡。</p>
      
      <h2>结语</h2>
      
      <p>春天是一个充满诗意的季节，它给我们带来了无限的灵感和创作的欲望。在这个季节里，让我们放慢脚步，感受生活的美好，用文字记录下这些美好的瞬间。</p>
      
      <p>愿每一个人都能在春天里找到属于自己的诗意时光。</p>
    `},{id:2,title:"夜深人静时的思考",excerpt:"在夜深人静的时候，思绪总是特别清晰。这是一个适合思考人生、回顾过往的时刻。",date:"2024-03-12",readTime:3,image:"https://images.unsplash.com/photo-1519681393784-d120c3e90d74?w=800&h=600&fit=crop",href:"/articles/night-thoughts",author:"墨韵",tags:["夜晚","思考","人生感悟"],coverImage:"https://images.unsplash.com/photo-1519681393784-d120c3e90d74?w=1200&h=600&fit=crop",content:`
      <p>夜深人静的时候，世界仿佛按下了暂停键。没有了白天的喧嚣，没有了繁忙的工作，只有自己和内心的对话。</p>
      
      <p>这样的时刻，思绪总是特别清晰。白天被各种事务占据的大脑，终于有了喘息的机会，可以静下来思考一些更深层的问题。</p>
      
      <h2>夜的宁静</h2>
      
      <p>夜晚有一种独特的魅力。月光透过窗帘洒在桌案上，远处偶尔传来的车声，都成了这个时刻的背景音。在这样的环境中，人很容易进入一种沉思的状态。</p>
      
      <p>我喜欢在这样的夜晚，泡一壶茶，翻开一本书，或者简单地坐在窗前，看着外面的世界，让思绪自由地飞翔。</p>
      
      <h2>思考的价值</h2>
      
      <p>思考是人类最宝贵的能力之一。通过思考，我们可以回顾过去，审视现在，规划未来。夜深人静的时候，正是进行这种深度思考的最佳时机。</p>
      
      <p>在这样的时刻，我们可以问自己一些重要的问题：今天做了什么有意义的事？明天想要成为什么样的人？人生的目标是什么？</p>
      
      <h2>内心的声音</h2>
      
      <p>白天的我们，往往被外界的声音所包围。工作的要求，他人的期待，社会的标准，这些都在影响着我们的判断。但在夜深人静的时候，我们终于可以听到内心真正的声音。</p>
      
      <p>这个声音可能会告诉我们，什么是真正重要的，什么是我们真正想要的。倾听内心的声音，是找到人生方向的重要途径。</p>
      
      <h2>结语</h2>
      
      <p>夜深人静时的思考，是一种珍贵的体验。在这个快节奏的时代，我们更需要这样的时刻，来与自己对话，来思考人生的意义。</p>
      
      <p>愿每个人都能珍惜这样的夜晚，在思考中找到内心的平静和人生的方向。</p>
    `},{id:3,title:"关于写作的一些感悟",excerpt:"写作是一种表达，也是一种思考的方式。通过文字，我们可以记录生活，分享感悟。",date:"2024-03-10",readTime:7,image:"https://images.unsplash.com/photo-1455390582262-044cdead277a?w=800&h=600&fit=crop",href:"/articles/writing-thoughts",author:"墨韵",tags:["写作","文字","创作"],coverImage:"https://images.unsplash.com/photo-1455390582262-044cdead277a?w=1200&h=600&fit=crop",content:`
      <p>写作，对我来说，不仅仅是一种表达方式，更是一种生活态度。通过文字，我记录着生活的点点滴滴，分享着内心的感悟和思考。</p>
      
      <p>每当提起笔，或者坐在电脑前敲击键盘的时候，我都会感到一种特殊的兴奋。这种兴奋来自于即将要创造出一些东西的期待，来自于要与读者分享思想的渴望。</p>
      
      <h2>写作的初心</h2>
      
      <p>我开始写作，最初只是为了记录。记录生活中的美好瞬间，记录内心的感动，记录对世界的观察和思考。慢慢地，我发现写作不仅仅是记录，更是一种思考的过程。</p>
      
      <p>在写作的过程中，原本模糊的想法会变得清晰，零散的思绪会变得有条理。写作帮助我更好地理解自己，理解世界。</p>
      
      <h2>文字的力量</h2>
      
      <p>文字有着神奇的力量。它可以跨越时空，连接不同的心灵。一篇好的文章，可以让读者产生共鸣，可以给人以启发，可以传递温暖和力量。</p>
      
      <p>我常常被一些优美的文字所感动。那些作家用简单的词汇，构建出复杂的情感世界，用平凡的语言，表达出深刻的哲理。这让我深深地敬佩，也激励着我不断地学习和进步。</p>
      
      <h2>写作的挑战</h2>
      
      <p>写作并不总是容易的。有时候，面对空白的页面，会感到无从下笔。有时候，想要表达的内容很多，却不知道如何组织。有时候，写出来的文字，总觉得不能准确地表达内心的想法。</p>
      
      <p>但正是这些挑战，让写作变得有趣。每一次的困难，都是一次成长的机会。每一次的突破，都会带来巨大的满足感。</p>
      
      <h2>写作的习惯</h2>
      
      <p>我发现，写作需要养成习惯。就像锻炼身体一样，只有持续地练习，才能不断地提高。我尽量每天都写一些东西，哪怕只是几百字的随笔。</p>
      
      <p>这种习惯不仅提高了我的写作技巧，更重要的是，它让我保持着对生活的敏感和思考。通过每天的写作，我学会了观察，学会了思考，学会了表达。</p>
      
      <h2>结语</h2>
      
      <p>写作是一条没有终点的路。在这条路上，我们会遇到困难，也会收获喜悦。但无论如何，我都会继续走下去，因为写作已经成为了我生活中不可缺少的一部分。</p>
      
      <p>愿每一个热爱文字的人，都能在写作中找到属于自己的快乐和意义。</p>
    `}],$x=()=>x.jsxs("div",{className:"home-page",children:[x.jsx(au,{}),x.jsxs("main",{className:"home-page__main",children:[x.jsx("section",{className:"home-page__hero",children:x.jsxs("div",{className:"home-page__hero-container",children:[x.jsx("h1",{className:"home-page__hero-title",children:"墨韵流光"}),x.jsx("p",{className:"home-page__hero-subtitle",children:"在数字世界中捕捉思想与文字流动的瞬间美感"}),x.jsxs("div",{className:"home-page__hero-actions",children:[x.jsx(ks,{onClick:()=>{window.history.pushState({},"","/article"),window.dispatchEvent(new PopStateEvent("popstate"))},variant:"primary",children:"开始阅读"}),x.jsx(ks,{onClick:()=>{window.history.pushState({},"","/about"),window.dispatchEvent(new PopStateEvent("popstate"))},variant:"secondary",children:"了解更多"})]})]})}),x.jsx("section",{className:"home-page__articles",children:x.jsxs("div",{className:"home-page__articles-container",children:[x.jsx("div",{className:"home-page__articles-grid",children:Hg.map(a=>x.jsx(Wx,{title:a.title,excerpt:a.excerpt,date:a.date,readTime:a.readTime,image:a.image,href:a.href,size:a.size},a.id))}),x.jsx("div",{className:"home-page__articles-more",children:x.jsx(ks,{href:"/articles",variant:"secondary",size:"large",children:"查看更多文章"})})]})})]}),x.jsx(lu,{})]}),Ix=({children:a,author:l,source:u,className:r,...c})=>{const d=iu("blockquote",r);return x.jsxs("blockquote",{className:d,...c,children:[x.jsx("div",{className:"blockquote__content",children:a}),(l||u)&&x.jsxs("footer",{className:"blockquote__footer",children:[l&&x.jsx("cite",{className:"blockquote__author",children:l}),u&&x.jsxs(x.Fragment,{children:[l&&x.jsx("span",{className:"blockquote__separator",children:"，"}),x.jsx("cite",{className:"blockquote__source",children:u})]})]})]})},tA=()=>{const a=Hg[0];return x.jsxs("div",{className:"article-page",children:[x.jsx(au,{}),x.jsxs("main",{className:"article-page__main",children:[x.jsxs("header",{className:"article-page__header",children:[a.coverImage&&x.jsx("div",{className:"article-page__cover",children:x.jsx("img",{src:a.coverImage,alt:a.title,className:"article-page__cover-image"})}),x.jsxs("div",{className:"article-page__header-content",children:[x.jsx("h1",{className:"article-page__title",children:a.title}),x.jsxs("div",{className:"article-page__meta",children:[a.author&&x.jsx("span",{className:"article-page__author",children:a.author}),a.date&&x.jsxs(x.Fragment,{children:[x.jsx("span",{className:"article-page__meta-separator",children:"·"}),x.jsx("time",{className:"article-page__date",dateTime:a.date,children:new Date(a.date).toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"})})]}),a.readTime&&x.jsxs(x.Fragment,{children:[x.jsx("span",{className:"article-page__meta-separator",children:"·"}),x.jsxs("span",{className:"article-page__read-time",children:[a.readTime," 分钟阅读"]})]})]})]})]}),x.jsx("div",{className:"article-page__container",children:x.jsxs("article",{className:"article-page__content",children:[x.jsx("div",{className:"article-page__body",dangerouslySetInnerHTML:{__html:a.content}}),a.tags&&a.tags.length>0&&x.jsxs("div",{className:"article-page__tags",children:[x.jsx("span",{className:"article-page__tags-label",children:"标签："}),x.jsx("ul",{className:"article-page__tags-list",children:a.tags.map(l=>x.jsx("li",{className:"article-page__tag",children:x.jsx(oa,{href:`/tags/${l}`,underline:!1,children:l})},l))})]})]})})]}),x.jsx(lu,{})]})},eA=({children:a,title:l,author:u,source:r,className:c,...d})=>{const h=iu("poetry",c);return x.jsxs("div",{className:h,...d,children:[l&&x.jsx("h3",{className:"poetry__title",children:l}),x.jsx("div",{className:"poetry__content",children:a}),(u||r)&&x.jsxs("footer",{className:"poetry__footer",children:[u&&x.jsxs("cite",{className:"poetry__author",children:["— ",u]}),r&&x.jsxs(x.Fragment,{children:[u&&x.jsx("span",{className:"poetry__separator",children:"，"}),x.jsxs("cite",{className:"poetry__source",children:["《",r,"》"]})]})]})]})},nA=()=>x.jsxs("div",{className:"about-page",children:[x.jsx(au,{}),x.jsx("main",{className:"about-page__main",children:x.jsxs("div",{className:"about-page__container",children:[x.jsxs("header",{className:"about-page__header",children:[x.jsx("h1",{className:"about-page__title",children:"关于墨韵流光"}),x.jsx("p",{className:"about-page__subtitle",children:"在数字世界中捕捉思想与文字流动的瞬间美感"})]}),x.jsxs("div",{className:"about-page__content",children:[x.jsxs("section",{className:"about-page__section",children:[x.jsx("h2",{children:"设计理念"}),x.jsx("p",{children:'"墨韵流光"致力于在数字世界中捕捉思想与文字流动的瞬间美感。它并非一个静态的内容容器， 而是一个有生命的、会呼吸的有机体。设计的核心任务是摒弃一切不必要的视觉噪音， 让内容本身成为绝对主角。'}),x.jsx(Ix,{author:"设计师",source:"设计规范",children:"内容优先，呼吸感，内隐和谐，情感温度——这是我们追求的设计境界。"})]}),x.jsxs("section",{className:"about-page__section",children:[x.jsx("h2",{children:"技术实现"}),x.jsxs("p",{children:["本站采用现代化的前端技术栈构建，基于React和Vite开发， 使用Framer Motion实现流畅的动画效果。整个设计系统严格遵循",x.jsx(oa,{href:"#",external:!0,children:"《墨韵流光》设计规范"}),"， 确保视觉的一致性和用户体验的优雅。"]}),x.jsxs("ul",{className:"about-page__tech-list",children:[x.jsx("li",{children:"React 18 - 现代化的用户界面库"}),x.jsx("li",{children:"Vite - 快速的构建工具"}),x.jsx("li",{children:"Framer Motion - 流畅的动画库"}),x.jsx("li",{children:"CSS Custom Properties - 设计令牌系统"}),x.jsx("li",{children:"响应式设计 - 适配各种设备"})]})]}),x.jsxs("section",{className:"about-page__section",children:[x.jsx("h2",{children:"设计特色"}),x.jsxs("div",{className:"about-page__features",children:[x.jsxs("div",{className:"about-page__feature",children:[x.jsx("h3",{children:"色彩系统"}),x.jsx("p",{children:'基于传统水墨意境，基调淡雅隽永，仅以一抹"落霞红"画龙点睛。'})]}),x.jsxs("div",{className:"about-page__feature",children:[x.jsx("h3",{children:"版式系统"}),x.jsx("p",{children:"融汇衬线体的古典风骨与无衬线体的现代易读性，构建层次清晰的字体系统。"})]}),x.jsxs("div",{className:"about-page__feature",children:[x.jsx("h3",{children:"动效系统"}),x.jsx("p",{children:'遵循"缓、柔、雅"的原则，所有动效旨在服务内容，强化体验。'})]}),x.jsxs("div",{className:"about-page__feature",children:[x.jsx("h3",{children:"间距系统"}),x.jsx("p",{children:"建立以8px为原子单位的网格体系，通过战略性留白构建从容的视觉节奏。"})]})]})]}),x.jsx("section",{className:"about-page__section",children:x.jsxs(eA,{title:"网站诗韵",author:"墨韵",className:"about-page__poetry",children:["数字世界觅诗韵，",`
`,"文字流光映心境。",`
`,"简约设计承古意，",`
`,"现代技术展新颜。",`
`,`
`,"留白之间藏深意，",`
`,"色彩点缀见匠心。",`
`,"愿君驻足细品味，",`
`,"共赏墨韵与流光。"]})}),x.jsxs("section",{className:"about-page__section",children:[x.jsx("h2",{children:"联系方式"}),x.jsx("p",{children:"如果您对本站的设计或技术实现有任何疑问，或者想要交流关于设计和开发的想法， 欢迎通过以下方式联系我："}),x.jsxs("div",{className:"about-page__contact",children:[x.jsxs("div",{className:"about-page__contact-item",children:[x.jsx("strong",{children:"邮箱："}),x.jsx(oa,{href:"mailto:<EMAIL>",external:!0,children:"<EMAIL>"})]}),x.jsxs("div",{className:"about-page__contact-item",children:[x.jsx("strong",{children:"GitHub："}),x.jsx(oa,{href:"https://github.com",external:!0,children:"@moyunliuguang"})]}),x.jsxs("div",{className:"about-page__contact-item",children:[x.jsx("strong",{children:"微博："}),x.jsx(oa,{href:"https://weibo.com",external:!0,children:"@墨韵流光"})]})]})]})]})]})}),x.jsx(lu,{})]}),aA=()=>x.jsxs("div",{className:"not-found-page",children:[x.jsx(au,{}),x.jsx("main",{className:"not-found-page__main",children:x.jsxs("div",{className:"not-found-page__container",children:[x.jsxs("div",{className:"not-found-page__content",children:[x.jsx("h1",{className:"not-found-page__title",children:"信步至此，已是无径"}),x.jsx("p",{className:"not-found-page__subtitle",children:"或可返回首页，重觅墨韵流光"}),x.jsx("div",{className:"not-found-page__actions",children:x.jsx(ks,{href:"/",variant:"primary",size:"large",children:"返回首页"})})]}),x.jsx("div",{className:"not-found-page__decoration",children:x.jsx("span",{className:"not-found-page__dots",children:"· · ·"})})]})}),x.jsx(lu,{})]});function iA(){const[a,l]=K.useState(window.location.pathname);return K.useEffect(()=>{const r=()=>{l(window.location.pathname)};return window.addEventListener("popstate",r),()=>window.removeEventListener("popstate",r)},[]),(()=>{switch(a){case"/":return x.jsx($x,{});case"/article":return x.jsx(tA,{});case"/about":return x.jsx(nA,{});default:return x.jsx(aA,{})}})()}g1.createRoot(document.getElementById("root")).render(x.jsx(K.StrictMode,{children:x.jsx(iA,{})}));
