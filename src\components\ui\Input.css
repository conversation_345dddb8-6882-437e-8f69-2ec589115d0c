/**
 * Input 组件样式
 * 实现表单元素的设计规范
 */

.input-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.input__label {
  font-family: var(--font-sans-serif);
  font-size: 14px;
  line-height: 24px;
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.input__required {
  color: var(--color-accent-romantic);
  margin-left: 2px;
}

.input {
  width: 100%;
  padding: 12px var(--space-sm);
  font-family: var(--font-sans-serif);
  font-size: 16px;
  line-height: 24px;
  color: var(--color-text-secondary);
  background-color: var(--color-background-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: 4px;
  transition: all var(--duration-fast) var(--easing-default);
}

.input::placeholder {
  color: var(--color-text-tertiary);
}

.input:focus {
  outline: none;
  border-color: var(--color-accent-romantic);
  box-shadow: 0 0 0 2px var(--color-alpha-romantic-20);
}

.input:hover:not(:focus):not(:disabled) {
  border-color: var(--color-text-tertiary);
}

/* 错误状态 */
.input--error {
  border-color: var(--color-accent-romantic);
}

.input--error:focus {
  box-shadow: 0 0 0 2px var(--color-alpha-romantic-20);
}

/* 禁用状态 */
.input--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--color-background-secondary);
}

.input--disabled::placeholder {
  color: var(--color-text-tertiary);
}

/* 帮助文本 */
.input__helper {
  font-family: var(--font-sans-serif);
  font-size: 12px;
  line-height: 20px;
  color: var(--color-text-tertiary);
}

.input__helper--error {
  color: var(--color-accent-romantic);
}

/* 尺寸变体 */
.input--small {
  padding: var(--space-xs) 12px;
  font-size: 14px;
  line-height: 20px;
}

.input--large {
  padding: var(--space-sm) var(--space-md);
  font-size: 18px;
  line-height: 28px;
}

/* 响应式设计 */
@media (max-width: 767px) {
  .input {
    font-size: 16px; /* 防止iOS缩放 */
  }
}
