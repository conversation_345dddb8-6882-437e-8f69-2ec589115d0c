/**
 * Poetry 组件样式
 * 实现诗歌模块的设计规范
 */

.poetry {
  margin: var(--space-xl) 0;
  padding: var(--space-lg);
  background-color: var(--color-background-secondary);
  border-radius: 8px;
  text-align: center;
  position: relative;
}

.poetry::before {
  content: '"';
  position: absolute;
  top: var(--space-sm);
  left: var(--space-md);
  font-family: var(--font-serif);
  font-size: 48px;
  line-height: 1;
  color: var(--color-border-primary);
  opacity: 0.5;
}

.poetry::after {
  content: '"';
  position: absolute;
  bottom: var(--space-sm);
  right: var(--space-md);
  font-family: var(--font-serif);
  font-size: 48px;
  line-height: 1;
  color: var(--color-border-primary);
  opacity: 0.5;
}

.poetry__title {
  font-family: var(--font-serif);
  font-size: 20px;
  line-height: 32px;
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
  font-weight: var(--font-weight-regular);
}

.poetry__content {
  font-family: var(--font-serif);
  font-size: 18px;
  line-height: 36px;
  color: var(--color-text-secondary);
  white-space: pre-wrap;
  margin-bottom: var(--space-md);
  position: relative;
  z-index: 1;
}

.poetry__footer {
  font-family: var(--font-sans-serif);
  font-size: 14px;
  line-height: 24px;
  color: var(--color-text-tertiary);
  font-style: normal;
  margin-top: var(--space-md);
  position: relative;
  z-index: 1;
}

.poetry__author {
  font-style: normal;
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.poetry__source {
  font-style: normal;
}

.poetry__separator {
  margin: 0 4px;
}

/* 简洁变体 */
.poetry--simple {
  background: none;
  padding: var(--space-md) 0;
  border-left: 3px solid var(--color-accent-romantic);
  padding-left: var(--space-lg);
  text-align: left;
}

.poetry--simple::before,
.poetry--simple::after {
  display: none;
}

/* 居中变体 */
.poetry--centered {
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

/* 大尺寸变体 */
.poetry--large .poetry__content {
  font-size: 22px;
  line-height: 44px;
}

.poetry--large .poetry__title {
  font-size: 24px;
  line-height: 36px;
}

/* 响应式设计 */
@media (max-width: 767px) {
  .poetry {
    padding: var(--space-md);
    margin: var(--space-lg) 0;
  }
  
  .poetry::before,
  .poetry::after {
    font-size: 32px;
    top: var(--space-xs);
    bottom: var(--space-xs);
    left: var(--space-sm);
    right: var(--space-sm);
  }
  
  .poetry__title {
    font-size: 18px;
    line-height: 28px;
  }
  
  .poetry__content {
    font-size: 16px;
    line-height: 32px;
  }
  
  .poetry--large .poetry__content {
    font-size: 18px;
    line-height: 36px;
  }
  
  .poetry--large .poetry__title {
    font-size: 20px;
    line-height: 32px;
  }
  
  .poetry--simple {
    padding-left: var(--space-md);
  }
}
