import React from 'react';
import { motion } from 'framer-motion';
import './ArticleCard.css';

const ArticleCard = ({ 
  title, 
  excerpt, 
  date, 
  readTime, 
  image, 
  href,
  size = 'normal' // 'normal' | 'large' 用于瀑布流布局
}) => {
  return (
    <motion.article
      className={`article-card article-card--${size}`}
      whileHover={{
        y: -4,
        transition: {
          duration: 0.25,
          ease: [0.25, 0.46, 0.45, 0.94]
        }
      }}
    >
      <a href={href} className="article-card__link">
        {/* 特色图片 */}
        {image && (
          <div className="article-card__image-container">
            <motion.img
              src={image}
              alt={title}
              className="article-card__image"
              whileHover={{
                scale: 1.03,
                transition: {
                  duration: 0.25,
                  ease: [0.25, 0.46, 0.45, 0.94]
                }
              }}
            />
            <div className="article-card__image-overlay" />
          </div>
        )}
        
        {/* 内容区域 */}
        <div className="article-card__content">
          <h3 className="article-card__title">{title}</h3>
          
          {excerpt && (
            <p className="article-card__excerpt">{excerpt}</p>
          )}
          
          {/* 元信息 */}
          <div className="article-card__meta">
            {date && (
              <time className="article-card__date" dateTime={date}>
                {new Date(date).toLocaleDateString('zh-CN', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </time>
            )}
            {readTime && (
              <>
                <span className="article-card__meta-separator">·</span>
                <span className="article-card__read-time">{readTime} 分钟阅读</span>
              </>
            )}
          </div>
        </div>
      </a>
    </motion.article>
  );
};

export default ArticleCard;
