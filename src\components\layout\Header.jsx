import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import './Header.css';

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrolled = window.scrollY > 64;
      setIsScrolled(scrolled);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const navItems = [
    { name: '首页', href: '/' },
    { name: '文章', href: '/article' },
    { name: '关于', href: '/about' },
    { name: '联系', href: '/contact' },
  ];

  const handleNavClick = (e, href) => {
    e.preventDefault();
    window.history.pushState({}, '', href);
    window.dispatchEvent(new PopStateEvent('popstate'));
    closeMobileMenu();
  };

  return (
    <>
      <motion.header
        className={`header ${isScrolled ? 'header--scrolled' : ''}`}
        initial={{ y: 0 }}
        animate={{ y: isScrolled ? -64 : 0 }}
        transition={{
          duration: 0.3,
          ease: [0.25, 0.46, 0.45, 0.94]
        }}
      >
        <div className="header__container">
          {/* Logo */}
          <div className="header__logo">
            <a
              href="/"
              className="header__logo-link"
              onClick={(e) => handleNavClick(e, '/')}
            >
              墨韵流光
            </a>
          </div>

          {/* 桌面端导航 */}
          <nav className="header__nav header__nav--desktop">
            <ul className="header__nav-list">
              {navItems.map((item) => (
                <li key={item.name} className="header__nav-item">
                  <a
                    href={item.href}
                    className="header__nav-link"
                    onClick={(e) => handleNavClick(e, item.href)}
                  >
                    {item.name}
                  </a>
                </li>
              ))}
            </ul>
          </nav>

          {/* 移动端菜单按钮 */}
          <button
            className="header__mobile-toggle"
            onClick={toggleMobileMenu}
            aria-label="切换菜单"
          >
            <span className="header__hamburger">
              <span></span>
              <span></span>
              <span></span>
            </span>
          </button>
        </div>
      </motion.header>

      {/* 移动端菜单 */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <>
            {/* 背景蒙层 */}
            <motion.div
              className="header__mobile-overlay"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              onClick={closeMobileMenu}
            />

            {/* 菜单面板 */}
            <motion.nav
              className="header__mobile-menu"
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{
                duration: 0.3,
                ease: [0.25, 0.46, 0.45, 0.94]
              }}
            >
              <ul className="header__mobile-nav-list">
                {navItems.map((item) => (
                  <li key={item.name} className="header__mobile-nav-item">
                    <a
                      href={item.href}
                      className="header__mobile-nav-link"
                      onClick={(e) => handleNavClick(e, item.href)}
                    >
                      {item.name}
                    </a>
                  </li>
                ))}
              </ul>
            </motion.nav>
          </>
        )}
      </AnimatePresence>
    </>
  );
};

export default Header;
