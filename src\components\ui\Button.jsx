import React from 'react';
import { motion } from 'framer-motion';
import clsx from 'clsx';
import './Button.css';

const Button = ({
  children,
  variant = 'primary', // 'primary' | 'secondary'
  size = 'medium', // 'small' | 'medium' | 'large'
  disabled = false,
  onClick,
  href,
  className,
  ...props
}) => {
  const buttonClasses = clsx(
    'button',
    `button--${variant}`,
    `button--${size}`,
    {
      'button--disabled': disabled,
    },
    className
  );

  const buttonProps = {
    className: buttonClasses,
    onClick: disabled ? undefined : onClick,
    disabled,
    ...props,
  };

  const content = (
    <motion.span
      className="button__content"
      whileTap={{ scale: 0.98 }}
      transition={{ duration: 0.1 }}
    >
      {children}
    </motion.span>
  );

  if (href && !disabled) {
    return (
      <motion.a
        href={href}
        {...buttonProps}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        transition={{
          duration: 0.15,
          ease: [0.25, 0.46, 0.45, 0.94]
        }}
      >
        {content}
      </motion.a>
    );
  }

  return (
    <motion.button
      {...buttonProps}
      whileHover={disabled ? {} : { scale: 1.02 }}
      whileTap={disabled ? {} : { scale: 0.98 }}
      transition={{
        duration: 0.15,
        ease: [0.25, 0.46, 0.45, 0.94]
      }}
    >
      {content}
    </motion.button>
  );
};

export default Button;
