/**
 * Blockquote 组件样式
 * 实现引用的设计规范
 */

.blockquote {
  position: relative;
  margin: var(--space-lg) 0;
  padding-left: var(--space-md);
  font-family: var(--font-sans-serif);
  font-style: italic;
  font-size: 18px;
  line-height: 32px;
  color: var(--color-text-secondary);
}

/* 左侧装饰线 */
.blockquote::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: var(--color-accent-romantic);
}

.blockquote__content {
  margin-bottom: var(--space-sm);
}

.blockquote__content p {
  margin-bottom: var(--space-sm);
}

.blockquote__content p:last-child {
  margin-bottom: 0;
}

/* 引用来源 */
.blockquote__footer {
  font-style: normal;
  font-size: 14px;
  line-height: 24px;
  color: var(--color-text-tertiary);
  margin-top: var(--space-sm);
}

.blockquote__author {
  font-style: normal;
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
}

.blockquote__source {
  font-style: normal;
}

.blockquote__separator {
  margin: 0 2px;
}

/* 大尺寸引用 */
.blockquote--large {
  font-size: 20px;
  line-height: 36px;
  padding-left: var(--space-lg);
  margin: var(--space-xl) 0;
}

.blockquote--large::before {
  width: 3px;
}

.blockquote--large .blockquote__footer {
  font-size: 15px;
  margin-top: var(--space-md);
}

/* 居中引用 */
.blockquote--centered {
  text-align: center;
  padding-left: 0;
  padding: 0 var(--space-lg);
}

.blockquote--centered::before {
  display: none;
}

.blockquote--centered::after {
  content: '';
  display: block;
  width: 40px;
  height: 2px;
  background-color: var(--color-accent-romantic);
  margin: var(--space-md) auto 0;
}

/* 无装饰引用 */
.blockquote--plain {
  padding-left: 0;
}

.blockquote--plain::before {
  display: none;
}

/* 响应式设计 */
@media (max-width: 767px) {
  .blockquote {
    font-size: 16px;
    line-height: 28px;
    margin: var(--space-md) 0;
    padding-left: var(--space-sm);
  }
  
  .blockquote--large {
    font-size: 18px;
    line-height: 32px;
    padding-left: var(--space-md);
    margin: var(--space-lg) 0;
  }
  
  .blockquote--centered {
    padding: 0 var(--space-md);
  }
}
