/**
 * Textarea 组件样式
 * 实现文本域的设计规范
 */

.textarea-wrapper {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.textarea__label {
  font-family: var(--font-sans-serif);
  font-size: 14px;
  line-height: 24px;
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.textarea__required {
  color: var(--color-accent-romantic);
  margin-left: 2px;
}

.textarea {
  width: 100%;
  padding: 12px var(--space-sm);
  font-family: var(--font-sans-serif);
  font-size: 16px;
  line-height: 24px;
  color: var(--color-text-secondary);
  background-color: var(--color-background-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: 4px;
  transition: all var(--duration-fast) var(--easing-default);
  min-height: 96px;
}

.textarea::placeholder {
  color: var(--color-text-tertiary);
}

.textarea:focus {
  outline: none;
  border-color: var(--color-accent-romantic);
  box-shadow: 0 0 0 2px var(--color-alpha-romantic-20);
}

.textarea:hover:not(:focus):not(:disabled) {
  border-color: var(--color-text-tertiary);
}

/* 调整大小选项 */
.textarea--resize-none {
  resize: none;
}

.textarea--resize-vertical {
  resize: vertical;
}

.textarea--resize-horizontal {
  resize: horizontal;
}

.textarea--resize-both {
  resize: both;
}

/* 错误状态 */
.textarea--error {
  border-color: var(--color-accent-romantic);
}

.textarea--error:focus {
  box-shadow: 0 0 0 2px var(--color-alpha-romantic-20);
}

/* 禁用状态 */
.textarea--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--color-background-secondary);
  resize: none;
}

.textarea--disabled::placeholder {
  color: var(--color-text-tertiary);
}

/* 帮助文本 */
.textarea__helper {
  font-family: var(--font-sans-serif);
  font-size: 12px;
  line-height: 20px;
  color: var(--color-text-tertiary);
}

.textarea__helper--error {
  color: var(--color-accent-romantic);
}

/* 响应式设计 */
@media (max-width: 767px) {
  .textarea {
    font-size: 16px; /* 防止iOS缩放 */
  }
}
