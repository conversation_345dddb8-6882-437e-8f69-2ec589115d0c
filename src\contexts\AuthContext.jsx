import React, { createContext, useContext, useState, useEffect } from 'react';

// 默认管理员账号
const DEFAULT_ADMIN = {
  username: 'admin',
  password: 'Wyh123wyh',
  role: 'admin',
  displayName: '管理员'
};

// 创建认证上下文
const AuthContext = createContext();

// 自定义Hook用于使用认证上下文
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// 认证提供者组件
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // 从localStorage恢复登录状态
  useEffect(() => {
    const savedUser = localStorage.getItem('auth_user');
    if (savedUser) {
      try {
        const userData = JSON.parse(savedUser);
        setUser(userData);
      } catch (error) {
        console.error('Failed to parse saved user data:', error);
        localStorage.removeItem('auth_user');
      }
    }
    setIsLoading(false);
  }, []);

  // 登录函数
  const login = async (username, password) => {
    try {
      // 简单的前端验证逻辑
      if (username === DEFAULT_ADMIN.username && password === DEFAULT_ADMIN.password) {
        const userData = {
          username: DEFAULT_ADMIN.username,
          role: DEFAULT_ADMIN.role,
          displayName: DEFAULT_ADMIN.displayName,
          loginTime: new Date().toISOString()
        };
        
        setUser(userData);
        localStorage.setItem('auth_user', JSON.stringify(userData));
        
        return { success: true, user: userData };
      } else {
        return { 
          success: false, 
          error: '用户名或密码错误' 
        };
      }
    } catch (error) {
      return { 
        success: false, 
        error: '登录过程中发生错误' 
      };
    }
  };

  // 登出函数
  const logout = () => {
    setUser(null);
    localStorage.removeItem('auth_user');
  };

  // 检查是否为管理员
  const isAdmin = () => {
    return user && user.role === 'admin';
  };

  // 检查是否已登录
  const isAuthenticated = () => {
    return !!user;
  };

  const value = {
    user,
    isLoading,
    login,
    logout,
    isAdmin,
    isAuthenticated
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
