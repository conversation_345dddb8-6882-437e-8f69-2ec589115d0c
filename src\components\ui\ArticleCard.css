/**
 * ArticleCard 组件样式
 * 实现文章卡片的设计规范
 */

.article-card {
  background-color: var(--color-background-secondary);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.04);
  transition: box-shadow var(--duration-medium) var(--easing-default);
  height: fit-content;
}

.article-card:hover {
  box-shadow: 0px 8px 24px 0px rgba(0, 0, 0, 0.08);
}

.article-card__link {
  display: block;
  color: inherit;
  text-decoration: none;
}

/* 图片容器 */
.article-card__image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.article-card__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.article-card__image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40%;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0) 100%
  );
  pointer-events: none;
}

/* 内容区域 */
.article-card__content {
  padding: var(--space-md);
}

.article-card__title {
  font-family: var(--font-serif);
  font-size: 24px;
  line-height: 32px;
  font-weight: var(--font-weight-regular);
  color: var(--color-text-primary);
  margin-bottom: var(--space-sm);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-card__excerpt {
  font-family: var(--font-sans-serif);
  font-size: 15px;
  line-height: 24px;
  color: var(--color-text-secondary);
  margin-bottom: var(--space-md);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 元信息 */
.article-card__meta {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-family: var(--font-sans-serif);
  font-size: 14px;
  line-height: 24px;
  color: var(--color-text-tertiary);
}

.article-card__date {
  color: inherit;
}

.article-card__meta-separator {
  color: var(--color-text-tertiary);
}

.article-card__read-time {
  color: inherit;
}

/* 尺寸变体 */
.article-card--large .article-card__image-container {
  height: 280px;
}

.article-card--large .article-card__title {
  font-size: 28px;
  line-height: 40px;
  -webkit-line-clamp: 3;
}

.article-card--large .article-card__excerpt {
  font-size: 16px;
  line-height: 28px;
  -webkit-line-clamp: 4;
}

.article-card--large .article-card__content {
  padding: var(--space-lg);
}

/* 无图片样式 */
.article-card:not(:has(.article-card__image-container)) .article-card__content {
  padding: var(--space-lg);
}

.article-card:not(:has(.article-card__image-container)) .article-card__title {
  font-size: 28px;
  line-height: 40px;
  margin-bottom: var(--space-md);
}

/* 响应式设计 */
@media (max-width: 767px) {
  .article-card__image-container {
    height: 160px;
  }
  
  .article-card__title {
    font-size: 20px;
    line-height: 28px;
  }
  
  .article-card__excerpt {
    font-size: 14px;
    line-height: 22px;
  }
  
  .article-card__content {
    padding: var(--space-sm);
  }
  
  .article-card--large .article-card__image-container {
    height: 200px;
  }
  
  .article-card--large .article-card__title {
    font-size: 22px;
    line-height: 32px;
  }
  
  .article-card--large .article-card__excerpt {
    font-size: 15px;
    line-height: 24px;
  }
  
  .article-card--large .article-card__content {
    padding: var(--space-md);
  }
}
