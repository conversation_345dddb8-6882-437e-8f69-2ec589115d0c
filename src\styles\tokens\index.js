/**
 * 《墨韵流光》设计令牌统一导出
 */

import { colors, cssVariables as colorVariables } from './colors.js';
import { spacing, spacingValues, specialSpacing, cssVariables as spacingVariables } from './spacing.js';
import { fontFamilies, fontWeights, typography, baselineGrid, cssVariables as typographyVariables } from './typography.js';
import { easings, durations, animations, cssVariables as motionVariables } from './motion.js';
import { breakpoints, mediaQueries, cssVariables as breakpointVariables } from './breakpoints.js';

// 统一导出所有设计令牌
export {
  colors,
  spacing,
  spacingValues,
  specialSpacing,
  fontFamilies,
  fontWeights,
  typography,
  baselineGrid,
  easings,
  durations,
  animations,
  breakpoints,
  mediaQueries,
};

// 合并所有 CSS 自定义属性
export const cssVariables = {
  ...colorVariables,
  ...spacingVariables,
  ...typographyVariables,
  ...motionVariables,
  ...breakpointVariables,
};

// 生成 CSS 自定义属性字符串
export const generateCSSVariables = () => {
  return Object.entries(cssVariables)
    .map(([key, value]) => `${key}: ${value};`)
    .join('\n  ');
};
