import React from 'react';
import { motion } from 'framer-motion';
import clsx from 'clsx';
import './Link.css';

const Link = ({
  children,
  href,
  external = false,
  underline = true,
  className,
  ...props
}) => {
  const linkClasses = clsx(
    'link',
    {
      'link--no-underline': !underline,
      'link--external': external,
    },
    className
  );

  const linkProps = {
    href,
    className: linkClasses,
    ...(external && {
      target: '_blank',
      rel: 'noopener noreferrer',
    }),
    ...props,
  };

  return (
    <motion.a
      {...linkProps}
      whileHover="hover"
      initial="initial"
      variants={{
        initial: {},
        hover: {},
      }}
    >
      <span className="link__text">{children}</span>
      {underline && (
        <motion.span
          className="link__underline"
          variants={{
            initial: { scaleX: 0 },
            hover: { scaleX: 1 },
          }}
          transition={{
            duration: 0.2,
            ease: [0.25, 0.46, 0.45, 0.94],
          }}
        />
      )}
      {external && (
        <span className="link__external-icon" aria-hidden="true">
          ↗
        </span>
      )}
    </motion.a>
  );
};

export default Link;
