/**
 * 《墨韵流光》间距系统
 * 建立以 8px 为原子单位的网格体系
 * 所有元素的 margin 与 padding 必须是此单位的整数倍
 */

export const spacing = {
  xs: '8px',    // 1 unit
  sm: '16px',   // 2 units
  md: '24px',   // 3 units
  lg: '48px',   // 6 units
  xl: '64px',   // 8 units
  xxl: '96px',  // 12 units
};

// 数值版本（用于计算）
export const spacingValues = {
  xs: 8,
  sm: 16,
  md: 24,
  lg: 48,
  xl: 64,
  xxl: 96,
};

// 特殊间距规则
export const specialSpacing = {
  // 文章页主内容区两侧留白（桌面端/平板端）
  contentSidePadding: '120px',
  
  // 移动端内容区左右padding
  mobileContentPadding: spacing.md, // 24px
  
  // 顶部导航高度
  headerHeight: '64px',
  
  // 侧边栏最大宽度
  sidebarMaxWidth: '280px',
  
  // 文章核心内容区最大宽度
  articleMaxWidth: '720px',
};

// CSS 自定义属性导出
export const cssVariables = {
  '--space-xs': spacing.xs,
  '--space-sm': spacing.sm,
  '--space-md': spacing.md,
  '--space-lg': spacing.lg,
  '--space-xl': spacing.xl,
  '--space-xxl': spacing.xxl,
  '--content-side-padding': specialSpacing.contentSidePadding,
  '--mobile-content-padding': specialSpacing.mobileContentPadding,
  '--header-height': specialSpacing.headerHeight,
  '--sidebar-max-width': specialSpacing.sidebarMaxWidth,
  '--article-max-width': specialSpacing.articleMaxWidth,
};
