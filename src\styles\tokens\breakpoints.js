/**
 * 《墨韵流光》响应式断点系统
 */

export const breakpoints = {
  mobile: '768px',    // < 768px
  tablet: '1200px',   // 768px - 1199px
  desktop: '1200px',  // >= 1200px
};

// 媒体查询辅助函数
export const mediaQueries = {
  mobile: `(max-width: ${breakpoints.mobile})`,
  tablet: `(min-width: ${breakpoints.mobile}) and (max-width: calc(${breakpoints.tablet} - 1px))`,
  desktop: `(min-width: ${breakpoints.tablet})`,
  
  // 简化版本
  mobileOnly: `(max-width: calc(${breakpoints.mobile} - 1px))`,
  tabletUp: `(min-width: ${breakpoints.mobile})`,
  desktopUp: `(min-width: ${breakpoints.tablet})`,
};

// CSS 自定义属性导出
export const cssVariables = {
  '--breakpoint-mobile': breakpoints.mobile,
  '--breakpoint-tablet': breakpoints.tablet,
  '--breakpoint-desktop': breakpoints.desktop,
};
