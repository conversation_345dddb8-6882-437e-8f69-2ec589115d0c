/**
 * 《墨韵流光》动效系统
 * 核心信条: 缓、柔、雅
 * 所有动效旨在服务内容，强化体验，避免无意义的炫技
 */

// 动画曲线
export const easings = {
  // 全局默认缓动曲线 (类 ease-out)
  default: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  
  // 其他常用曲线
  easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
  easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
  easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
};

// 持续时间
export const durations = {
  // 交互反馈动效 (hover, active)
  fast: '150ms',
  medium: '250ms',
  
  // 页面元素进入/退出动效
  slow: '300ms',
  slower: '500ms',
  
  // 特殊动效
  breathe: '2000ms', // 呼吸式动画
};

// 常用动画组合
export const animations = {
  // 卡片悬停
  cardHover: {
    duration: durations.medium,
    easing: easings.default,
    transform: 'translateY(-4px)',
  },
  
  // 链接下划线绘制
  linkUnderline: {
    duration: '200ms',
    easing: easings.default,
  },
  
  // 导航栏滚动显隐
  headerSlide: {
    duration: durations.slow,
    easing: easings.default,
  },
  
  // 移动端菜单滑出
  mobileMenu: {
    duration: durations.slow,
    easing: easings.default,
  },
  
  // 画廊模式淡入淡出
  lightbox: {
    duration: durations.slow,
    easing: easings.default,
  },
  
  // 呼吸式加载动画
  breathingDots: {
    duration: durations.breathe,
    easing: easings.easeInOut,
  },
};

// CSS 自定义属性导出
export const cssVariables = {
  '--easing-default': easings.default,
  '--easing-ease-in': easings.easeIn,
  '--easing-ease-out': easings.easeOut,
  '--easing-ease-in-out': easings.easeInOut,
  
  '--duration-fast': durations.fast,
  '--duration-medium': durations.medium,
  '--duration-slow': durations.slow,
  '--duration-slower': durations.slower,
  '--duration-breathe': durations.breathe,
  
  // 组合动画
  '--animation-card-hover-duration': animations.cardHover.duration,
  '--animation-card-hover-easing': animations.cardHover.easing,
  '--animation-card-hover-transform': animations.cardHover.transform,
  
  '--animation-link-underline-duration': animations.linkUnderline.duration,
  '--animation-link-underline-easing': animations.linkUnderline.easing,
  
  '--animation-header-slide-duration': animations.headerSlide.duration,
  '--animation-header-slide-easing': animations.headerSlide.easing,
  
  '--animation-mobile-menu-duration': animations.mobileMenu.duration,
  '--animation-mobile-menu-easing': animations.mobileMenu.easing,
  
  '--animation-lightbox-duration': animations.lightbox.duration,
  '--animation-lightbox-easing': animations.lightbox.easing,
  
  '--animation-breathing-dots-duration': animations.breathingDots.duration,
  '--animation-breathing-dots-easing': animations.breathingDots.easing,
};
