import React from 'react';
import Link from '../ui/Link';
import './Footer.css';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  
  const socialLinks = [
    { name: '微博', href: '#', icon: '微' },
    { name: '知乎', href: '#', icon: '知' },
    { name: 'GitHub', href: '#', icon: 'G' },
    { name: '邮箱', href: 'mailto:<EMAIL>', icon: '@' },
  ];

  return (
    <footer className="footer">
      <div className="footer__container">
        <div className="footer__content">
          {/* 版权信息 */}
          <div className="footer__copyright">
            <p>© {currentYear} 墨韵流光. 保留所有权利.</p>
          </div>

          {/* 社交媒体链接 */}
          <div className="footer__social">
            <ul className="footer__social-list">
              {socialLinks.map((link) => (
                <li key={link.name} className="footer__social-item">
                  <Link
                    href={link.href}
                    external={link.href.startsWith('http') || link.href.startsWith('mailto')}
                    underline={false}
                    className="footer__social-link"
                    aria-label={link.name}
                  >
                    <span className="footer__social-icon">{link.icon}</span>
                    <span className="footer__social-name">{link.name}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* 备案信息 */}
          <div className="footer__legal">
            <p>
              <Link href="#" underline={false} className="footer__legal-link">
                京ICP备12345678号-1
              </Link>
            </p>
          </div>
        </div>

        {/* 装饰性分隔符 */}
        <div className="footer__separator">
          <span>· · ·</span>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
