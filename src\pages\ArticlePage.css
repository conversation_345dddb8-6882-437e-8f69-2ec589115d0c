/**
 * ArticlePage 样式
 * 实现文章页"静谧之地"布局
 */

.article-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.article-page__main {
  flex: 1;
  padding-top: var(--header-height);
}

/* 文章头部 */
.article-page__header {
  background-color: var(--color-background-primary);
  padding-bottom: var(--space-xl);
}

.article-page__cover {
  width: 100%;
  height: 400px;
  overflow: hidden;
  margin-bottom: var(--space-xl);
}

.article-page__cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.article-page__header-content {
  max-width: var(--article-max-width);
  margin: 0 auto;
  padding: 0 var(--space-md);
  text-align: center;
}

.article-page__title {
  font-family: var(--font-serif);
  font-size: 48px;
  line-height: 64px;
  color: var(--color-text-primary);
  margin-bottom: var(--space-lg);
  font-weight: var(--font-weight-regular);
}

.article-page__meta {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-xs);
  font-family: var(--font-sans-serif);
  font-size: 14px;
  line-height: 24px;
  color: var(--color-text-tertiary);
  flex-wrap: wrap;
}

.article-page__author {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.article-page__meta-separator {
  color: var(--color-text-tertiary);
}

.article-page__date,
.article-page__read-time {
  color: inherit;
}

/* 文章内容容器 */
.article-page__container {
  background-color: var(--color-background-primary);
  padding: var(--space-xl) 0 var(--space-xxl);
}

.article-page__content {
  max-width: var(--article-max-width);
  margin: 0 auto;
  padding: 0 var(--space-md);
}

/* 文章正文样式 */
.article-page__body {
  font-family: var(--font-sans-serif);
  font-size: 17px;
  line-height: 32px;
  color: var(--color-text-secondary);
}

.article-page__body p {
  margin-bottom: var(--space-lg);
}

.article-page__body p:last-child {
  margin-bottom: 0;
}

.article-page__body h2 {
  font-family: var(--font-serif);
  font-size: 32px;
  line-height: 48px;
  color: var(--color-text-primary);
  margin: var(--space-xl) 0 var(--space-lg);
  font-weight: var(--font-weight-regular);
}

.article-page__body h3 {
  font-family: var(--font-serif);
  font-size: 24px;
  line-height: 32px;
  color: var(--color-text-primary);
  margin: var(--space-lg) 0 var(--space-md);
  font-weight: var(--font-weight-regular);
}

.article-page__body blockquote {
  margin: var(--space-xl) 0;
}

.article-page__body img {
  margin: var(--space-lg) 0;
  border-radius: 8px;
}

.article-page__body code {
  background-color: var(--color-background-secondary);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: var(--font-mono);
  font-size: 15px;
}

.article-page__body pre {
  background-color: var(--color-background-secondary);
  padding: var(--space-md);
  border-radius: 8px;
  overflow-x: auto;
  margin: var(--space-lg) 0;
}

.article-page__body pre code {
  background: none;
  padding: 0;
}

/* 文章标签 */
.article-page__tags {
  margin-top: var(--space-xl);
  padding-top: var(--space-lg);
  border-top: 1px solid var(--color-border-primary);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  flex-wrap: wrap;
}

.article-page__tags-label {
  font-family: var(--font-sans-serif);
  font-size: 14px;
  line-height: 24px;
  color: var(--color-text-tertiary);
  font-weight: var(--font-weight-medium);
}

.article-page__tags-list {
  display: flex;
  gap: var(--space-sm);
  flex-wrap: wrap;
}

.article-page__tag {
  background-color: var(--color-background-secondary);
  border-radius: 16px;
  padding: 4px 12px;
  font-size: 12px;
  line-height: 20px;
}

.article-page__tag a {
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-regular);
}

.article-page__tag a:hover {
  color: var(--color-accent-romantic);
}

/* 响应式设计 */
@media (max-width: 767px) {
  .article-page__cover {
    height: 250px;
    margin-bottom: var(--space-lg);
  }

  .article-page__title {
    font-size: 32px;
    line-height: 44px;
  }

  .article-page__meta {
    font-size: 13px;
    justify-content: center;
  }

  .article-page__body {
    font-size: 16px;
    line-height: 28px;
  }

  .article-page__body h2 {
    font-size: 28px;
    line-height: 40px;
    margin: var(--space-lg) 0 var(--space-md);
  }

  .article-page__body h3 {
    font-size: 22px;
    line-height: 32px;
    margin: var(--space-md) 0 var(--space-sm);
  }

  .article-page__tags {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (min-width: 768px) and (max-width: 1199px) {
  .article-page__header-content {
    padding: 0 var(--space-lg);
  }

  .article-page__content {
    padding: 0 var(--space-lg);
  }
}

@media (min-width: 1200px) {
  .article-page__header-content {
    padding: 0 var(--content-side-padding);
  }

  .article-page__content {
    padding: 0 var(--content-side-padding);
  }
}
