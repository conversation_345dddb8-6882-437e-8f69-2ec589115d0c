/**
 * 《墨韵流光》色彩系统
 * 基于传统水墨意境，基调淡雅隽永，仅以一抹"浪漫色"画龙点睛
 */

export const colors = {
  // 背景色系
  background: {
    primary: '#F9F7F3',    // 暖宣白 - 主背景
    secondary: '#F4F1ED',  // 淡米 - 卡片、代码块等与主背景需轻微区分的元素背景
  },

  // 文字色系
  text: {
    primary: '#332E2B',    // 墨黑 - H1, H2, H3 标题
    secondary: '#45403D',  // 深棕灰 - 正文、列表等主体内容
    tertiary: '#9A938E',   // 浅灰褐 - 图注、日期、元信息等辅助文本
  },

  // 点缀色
  accent: {
    romantic: '#B5655D',   // 落霞红 - 唯一的点缀色，用于链接悬停下划线、引用装饰线、激活状态的UI元素
  },

  // 边框色
  border: {
    primary: '#EAE6E1',    // 浅云灰 - 用于需要显式边框的场景，如输入框、分隔符等
  },

  // 特殊状态色
  overlay: {
    dark: 'rgba(29, 26, 25, 0.9)',      // 画廊模式背景
    mobileMenu: 'rgba(0, 0, 0, 0.4)',   // 移动端菜单蒙层
  },

  // 透明度变体
  alpha: {
    romantic10: 'rgba(181, 101, 93, 0.1)',  // 次按钮悬停背景
    romantic20: 'rgba(181, 101, 93, 0.2)',  // 输入框焦点阴影
    background85: 'rgba(249, 247, 243, 0.85)', // 导航栏滚动时背景
  }
};

// CSS 自定义属性导出
export const cssVariables = {
  '--color-background-primary': colors.background.primary,
  '--color-background-secondary': colors.background.secondary,
  '--color-text-primary': colors.text.primary,
  '--color-text-secondary': colors.text.secondary,
  '--color-text-tertiary': colors.text.tertiary,
  '--color-accent-romantic': colors.accent.romantic,
  '--color-border-primary': colors.border.primary,
  '--color-overlay-dark': colors.overlay.dark,
  '--color-overlay-mobile-menu': colors.overlay.mobileMenu,
  '--color-alpha-romantic-10': colors.alpha.romantic10,
  '--color-alpha-romantic-20': colors.alpha.romantic20,
  '--color-alpha-background-85': colors.alpha.background85,
};
