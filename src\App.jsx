import React, { useState, useEffect } from 'react';
import HomePage from './pages/HomePage';
import ArticlePage from './pages/ArticlePage';
import NotFoundPage from './pages/NotFoundPage';
import './App.css';

function App() {
  const [currentPath, setCurrentPath] = useState(window.location.pathname);

  useEffect(() => {
    const handlePopState = () => {
      setCurrentPath(window.location.pathname);
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  // 简单的路由逻辑
  const renderPage = () => {
    switch (currentPath) {
      case '/':
        return <HomePage />;
      case '/article':
        return <ArticlePage />;
      default:
        return <NotFoundPage />;
    }
  };

  return renderPage();
}

export default App;
