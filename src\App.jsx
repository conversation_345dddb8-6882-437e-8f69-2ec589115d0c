import React, { useState, useEffect } from 'react';
import { AuthProvider } from './contexts/AuthContext';
import HomePage from './pages/HomePage';
import ArticlePage from './pages/ArticlePage';
import AboutPage from './pages/AboutPage';
import LoginPage from './pages/LoginPage';
import AdminPage from './pages/AdminPage';
import NotFoundPage from './pages/NotFoundPage';
import './App.css';

function App() {
  const [currentPath, setCurrentPath] = useState(window.location.pathname);

  useEffect(() => {
    const handlePopState = () => {
      setCurrentPath(window.location.pathname);
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  // 简单的路由逻辑
  const renderPage = () => {
    switch (currentPath) {
      case '/':
        return <HomePage />;
      case '/article':
        return <ArticlePage />;
      case '/about':
        return <AboutPage />;
      case '/login':
        return <LoginPage />;
      case '/admin':
        return <AdminPage />;
      default:
        return <NotFoundPage />;
    }
  };

  return (
    <AuthProvider>
      {renderPage()}
    </AuthProvider>
  );
}

export default App;
