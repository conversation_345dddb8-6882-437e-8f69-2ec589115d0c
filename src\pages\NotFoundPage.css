/**
 * NotFoundPage 样式
 * 实现404错误页面的设计规范
 */

.not-found-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.not-found-page__main {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: var(--header-height);
  padding-bottom: var(--space-xxl);
  background-color: var(--color-background-primary);
}

.not-found-page__container {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 var(--space-md);
  text-align: center;
}

.not-found-page__content {
  margin-bottom: var(--space-xl);
}

.not-found-page__title {
  font-family: var(--font-serif);
  font-size: 32px;
  line-height: 48px;
  color: var(--color-text-primary);
  margin-bottom: var(--space-md);
  font-weight: var(--font-weight-regular);
}

.not-found-page__subtitle {
  font-family: var(--font-sans-serif);
  font-size: 17px;
  line-height: 32px;
  color: var(--color-text-secondary);
  margin-bottom: var(--space-xl);
}

.not-found-page__actions {
  display: flex;
  justify-content: center;
}

/* 装饰性元素 */
.not-found-page__decoration {
  margin-top: var(--space-xl);
}

.not-found-page__dots {
  font-family: var(--font-serif);
  font-size: 20px;
  color: var(--color-border-primary);
  letter-spacing: 12px;
}

/* 响应式设计 */
@media (max-width: 767px) {
  .not-found-page__title {
    font-size: 28px;
    line-height: 40px;
  }
  
  .not-found-page__subtitle {
    font-size: 16px;
    line-height: 28px;
  }
  
  .not-found-page__dots {
    font-size: 18px;
    letter-spacing: 8px;
  }
}

@media (min-width: 768px) {
  .not-found-page__container {
    padding: 0 var(--space-lg);
  }
}

@media (min-width: 1200px) {
  .not-found-page__container {
    padding: 0 var(--content-side-padding);
  }
}
