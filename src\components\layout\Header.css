/**
 * Header 组件样式
 * 实现顶部导航的设计规范
 */

.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--header-height);
  background-color: transparent;
  z-index: 1000;
  transition: background-color var(--duration-slow) var(--easing-default);
}

.header--scrolled {
  background-color: var(--color-alpha-background-85);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.header__container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-md);
}

/* Logo */
.header__logo {
  flex-shrink: 0;
}

.header__logo-link {
  font-family: var(--font-serif);
  font-size: 20px;
  line-height: 28px;
  color: var(--color-text-primary);
  font-weight: var(--font-weight-regular);
  transition: color var(--duration-fast) var(--easing-default);
}

.header__logo-link:hover {
  color: var(--color-accent-romantic);
}

/* 桌面端导航 */
.header__nav--desktop {
  display: none;
}

.header__nav-list {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.header__nav-link {
  font-family: var(--font-sans-serif);
  font-size: 16px;
  line-height: 24px;
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-regular);
  transition: color var(--duration-fast) var(--easing-default);
  position: relative;
}

.header__nav-link:hover {
  color: var(--color-text-primary);
}

.header__nav-link--active {
  color: var(--color-text-primary);
  font-weight: var(--font-weight-bold);
}

.header__nav-link--login {
  color: var(--color-accent-romantic);
  font-weight: var(--font-weight-medium);
}

/* 用户菜单 */
.header__user-menu {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.header__user-name {
  font-family: var(--font-sans-serif);
  font-size: 14px;
  line-height: 20px;
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.header__logout-btn {
  font-family: var(--font-sans-serif);
  font-size: 14px;
  line-height: 20px;
  color: var(--color-accent-romantic);
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all var(--duration-fast) var(--easing-default);
}

.header__logout-btn:hover {
  background-color: var(--color-alpha-romantic-10);
}

/* 移动端菜单按钮 */
.header__mobile-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  padding: 0;
  background: none;
  border: none;
  cursor: pointer;
}

.header__hamburger {
  display: flex;
  flex-direction: column;
  width: 20px;
  height: 14px;
  position: relative;
}

.header__hamburger span {
  display: block;
  height: 2px;
  width: 100%;
  background-color: var(--color-text-primary);
  margin-bottom: 4px;
  transition: all var(--duration-fast) var(--easing-default);
}

.header__hamburger span:last-child {
  margin-bottom: 0;
}

/* 移动端菜单 */
.header__mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-overlay-mobile-menu);
  z-index: 1001;
}

.header__mobile-menu {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: var(--sidebar-max-width);
  background-color: var(--color-background-primary);
  z-index: 1002;
  padding: var(--space-xl) var(--space-md);
  box-shadow: -4px 0 24px rgba(0, 0, 0, 0.1);
}

.header__mobile-nav-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-lg);
  margin-top: var(--space-xl);
}

.header__mobile-nav-item {
  position: relative;
}

.header__mobile-nav-link {
  font-family: var(--font-sans-serif);
  font-size: 18px;
  line-height: 28px;
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-regular);
  transition: color var(--duration-fast) var(--easing-default);
  display: block;
  padding: var(--space-sm) 0;
  position: relative;
}

.header__mobile-nav-link:hover {
  color: var(--color-text-primary);
}

.header__mobile-nav-link--active {
  color: var(--color-text-primary);
  font-weight: var(--font-weight-bold);
}

.header__mobile-nav-link--active::before {
  content: '';
  position: absolute;
  left: -var(--space-md);
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: var(--color-accent-romantic);
}

.header__mobile-nav-link--login {
  color: var(--color-accent-romantic);
  font-weight: var(--font-weight-medium);
}

/* 移动端用户状态 */
.header__mobile-user-info {
  border-top: 1px solid var(--color-border-primary);
  margin-top: var(--space-md);
  padding-top: var(--space-md);
}

.header__mobile-user-name {
  font-family: var(--font-sans-serif);
  font-size: 16px;
  line-height: 24px;
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
  display: block;
  padding: var(--space-sm) 0;
}

.header__mobile-logout-btn {
  font-family: var(--font-sans-serif);
  font-size: 16px;
  line-height: 24px;
  color: var(--color-accent-romantic);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-sm) 0;
  width: 100%;
  text-align: left;
  transition: all var(--duration-fast) var(--easing-default);
}

.header__mobile-logout-btn:hover {
  color: var(--color-text-primary);
}

/* 响应式设计 */
@media (min-width: 768px) {
  .header__nav--desktop {
    display: block;
  }

  .header__mobile-toggle {
    display: none;
  }

  .header__container {
    padding: 0 var(--space-lg);
  }
}

@media (min-width: 1200px) {
  .header__container {
    padding: 0 var(--content-side-padding);
  }
}
