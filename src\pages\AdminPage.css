/**
 * AdminPage 样式
 * 实现管理后台页面的设计规范
 */

.admin-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.admin-page__main {
  flex: 1;
  padding-top: var(--header-height);
  background-color: var(--color-background-primary);
}

.admin-page__container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-xl) var(--space-md) var(--space-xxl);
}

/* 页面头部 */
.admin-page__header {
  text-align: center;
  margin-bottom: var(--space-xxl);
}

.admin-page__title {
  font-family: var(--font-serif);
  font-size: 48px;
  line-height: 64px;
  color: var(--color-text-primary);
  margin-bottom: var(--space-sm);
  font-weight: var(--font-weight-regular);
}

.admin-page__subtitle {
  font-family: var(--font-sans-serif);
  font-size: 18px;
  line-height: 28px;
  color: var(--color-text-secondary);
  margin: 0;
}

/* 章节标题 */
.admin-page__section-title {
  font-family: var(--font-serif);
  font-size: 24px;
  line-height: 32px;
  color: var(--color-text-primary);
  margin-bottom: var(--space-lg);
  font-weight: var(--font-weight-regular);
}

/* 统计数据区域 */
.admin-page__stats {
  margin-bottom: var(--space-xxl);
}

.admin-page__stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-lg);
}

.admin-page__stat-card {
  background-color: var(--color-background-secondary);
  border-radius: 8px;
  padding: var(--space-lg);
  border: 1px solid var(--color-border-primary);
  display: flex;
  align-items: center;
  gap: var(--space-md);
  transition: all var(--duration-medium) var(--easing-default);
}

.admin-page__stat-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.admin-page__stat-icon {
  font-size: 32px;
  line-height: 1;
}

.admin-page__stat-content {
  flex: 1;
}

.admin-page__stat-value {
  font-family: var(--font-serif);
  font-size: 28px;
  line-height: 36px;
  color: var(--color-text-primary);
  font-weight: var(--font-weight-regular);
  margin-bottom: 2px;
}

.admin-page__stat-label {
  font-family: var(--font-sans-serif);
  font-size: 14px;
  line-height: 20px;
  color: var(--color-text-tertiary);
}

/* 快速操作区域 */
.admin-page__actions {
  margin-bottom: var(--space-xxl);
}

.admin-page__actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-lg);
}

.admin-page__action-card {
  background-color: var(--color-background-secondary);
  border-radius: 8px;
  padding: var(--space-lg);
  border: 1px solid var(--color-border-primary);
  transition: all var(--duration-medium) var(--easing-default);
}

.admin-page__action-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.admin-page__action-title {
  font-family: var(--font-serif);
  font-size: 20px;
  line-height: 28px;
  color: var(--color-text-primary);
  margin-bottom: var(--space-sm);
  font-weight: var(--font-weight-regular);
}

.admin-page__action-description {
  font-family: var(--font-sans-serif);
  font-size: 15px;
  line-height: 24px;
  color: var(--color-text-secondary);
  margin-bottom: var(--space-md);
}

.admin-page__action-button {
  width: 100%;
}

/* 最近活动区域 */
.admin-page__recent {
  margin-bottom: var(--space-xl);
}

.admin-page__recent-list {
  background-color: var(--color-background-secondary);
  border-radius: 8px;
  border: 1px solid var(--color-border-primary);
  overflow: hidden;
}

.admin-page__recent-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-md);
  padding: var(--space-md) var(--space-lg);
  border-bottom: 1px solid var(--color-border-primary);
}

.admin-page__recent-item:last-child {
  border-bottom: none;
}

.admin-page__recent-time {
  font-family: var(--font-sans-serif);
  font-size: 12px;
  line-height: 20px;
  color: var(--color-text-tertiary);
  white-space: nowrap;
  min-width: 60px;
}

.admin-page__recent-content {
  font-family: var(--font-sans-serif);
  font-size: 14px;
  line-height: 22px;
  color: var(--color-text-secondary);
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 767px) {
  .admin-page__container {
    padding: var(--space-lg) var(--space-md) var(--space-xl);
  }
  
  .admin-page__title {
    font-size: 32px;
    line-height: 44px;
  }
  
  .admin-page__subtitle {
    font-size: 16px;
    line-height: 24px;
  }
  
  .admin-page__stats-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }
  
  .admin-page__stat-card {
    padding: var(--space-md);
  }
  
  .admin-page__stat-icon {
    font-size: 24px;
  }
  
  .admin-page__stat-value {
    font-size: 24px;
    line-height: 32px;
  }
  
  .admin-page__actions-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }
  
  .admin-page__action-card {
    padding: var(--space-md);
  }
  
  .admin-page__recent-item {
    flex-direction: column;
    gap: var(--space-xs);
    padding: var(--space-md);
  }
  
  .admin-page__recent-time {
    min-width: auto;
  }
}

@media (min-width: 768px) and (max-width: 1199px) {
  .admin-page__container {
    padding-left: var(--space-lg);
    padding-right: var(--space-lg);
  }
  
  .admin-page__stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .admin-page__actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1200px) {
  .admin-page__container {
    padding-left: var(--content-side-padding);
    padding-right: var(--content-side-padding);
  }
  
  .admin-page__stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
