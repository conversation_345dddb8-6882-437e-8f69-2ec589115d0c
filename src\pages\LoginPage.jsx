import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Header from '../components/layout/Header';
import Footer from '../components/layout/Footer';
import Input from '../components/ui/Input';
import Button from '../components/ui/Button';
import { useAuth } from '../contexts/AuthContext';
import './LoginPage.css';

const LoginPage = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { login, isAuthenticated } = useAuth();

  // 如果已经登录，重定向到首页
  useEffect(() => {
    if (isAuthenticated()) {
      window.history.pushState({}, '', '/');
      window.dispatchEvent(new PopStateEvent('popstate'));
    }
  }, [isAuthenticated]);

  // 处理输入变化
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // 清除对应字段的错误
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // 表单验证
  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.username.trim()) {
      newErrors.username = '请输入用户名';
    }
    
    if (!formData.password.trim()) {
      newErrors.password = '请输入密码';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理表单提交
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const result = await login(formData.username, formData.password);
      
      if (result.success) {
        // 登录成功，重定向到首页
        window.history.pushState({}, '', '/');
        window.dispatchEvent(new PopStateEvent('popstate'));
      } else {
        // 登录失败，显示错误信息
        setErrors({
          general: result.error || '登录失败，请重试'
        });
      }
    } catch (error) {
      setErrors({
        general: '登录过程中发生错误，请重试'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="login-page">
      <Header />
      
      <main className="login-page__main">
        <div className="login-page__container">
          <motion.div
            className="login-page__form-container"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <header className="login-page__header">
              <h1 className="login-page__title">管理员登录</h1>
              <p className="login-page__subtitle">
                请输入您的管理员凭据以访问管理功能
              </p>
            </header>
            
            <form className="login-page__form" onSubmit={handleSubmit}>
              {errors.general && (
                <motion.div
                  className="login-page__error"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  {errors.general}
                </motion.div>
              )}
              
              <div className="login-page__field">
                <Input
                  name="username"
                  type="text"
                  label="用户名"
                  placeholder="请输入用户名"
                  value={formData.username}
                  onChange={handleInputChange}
                  error={!!errors.username}
                  helperText={errors.username}
                  required
                  disabled={isSubmitting}
                />
              </div>
              
              <div className="login-page__field">
                <Input
                  name="password"
                  type="password"
                  label="密码"
                  placeholder="请输入密码"
                  value={formData.password}
                  onChange={handleInputChange}
                  error={!!errors.password}
                  helperText={errors.password}
                  required
                  disabled={isSubmitting}
                />
              </div>
              
              <div className="login-page__actions">
                <Button
                  type="submit"
                  variant="primary"
                  size="large"
                  disabled={isSubmitting}
                  className="login-page__submit-button"
                >
                  {isSubmitting ? '登录中...' : '登录'}
                </Button>
              </div>
            </form>
            
            <div className="login-page__help">
              <p className="login-page__help-text">
                默认管理员账号：admin / Wyh123wyh
              </p>
            </div>
          </motion.div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default LoginPage;
