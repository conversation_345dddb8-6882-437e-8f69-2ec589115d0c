/**
 * 《墨韵流光》版式系统
 * 融汇衬线体的古典风骨与无衬线体的现代易读性
 * 构建层次清晰、富有书写美感的字体系统
 */

// 字体栈
export const fontFamilies = {
  serif: '"Source Han Serif SC", "思源宋体", Georgia, serif',
  sansSerif: '"Source Han Sans SC", "思源黑体", "PingFang SC", -apple-system, BlinkMacSystemFont, sans-serif',
  mono: '"JetBrains Mono", monospace',
};

// 字重
export const fontWeights = {
  regular: 400,
  medium: 500,
  bold: 700,
};

// 字号和行高系统
export const typography = {
  h1: {
    fontFamily: fontFamilies.serif,
    fontWeight: fontWeights.regular,
    fontSize: '48px',
    lineHeight: '64px', // 1.33
  },
  h2: {
    fontFamily: fontFamilies.serif,
    fontWeight: fontWeights.regular,
    fontSize: '32px',
    lineHeight: '48px', // 1.5
  },
  h3: {
    fontFamily: fontFamilies.serif,
    fontWeight: fontWeights.regular,
    fontSize: '24px',
    lineHeight: '32px', // 1.33
  },
  body: {
    fontFamily: fontFamilies.sansSerif,
    fontWeight: fontWeights.regular,
    fontSize: '17px',
    lineHeight: '32px', // 1.88
  },
  blockquote: {
    fontFamily: fontFamilies.sansSerif,
    fontWeight: fontWeights.regular,
    fontStyle: 'italic',
    fontSize: '18px',
    lineHeight: '32px', // 1.78
  },
  code: {
    fontFamily: fontFamilies.mono,
    fontWeight: fontWeights.regular,
    fontSize: '15px',
    lineHeight: '24px', // 1.6
  },
  meta: {
    fontFamily: fontFamilies.sansSerif,
    fontWeight: fontWeights.regular,
    fontSize: '14px',
    lineHeight: '24px', // 1.71
  },
  // 导航和UI元素
  nav: {
    fontFamily: fontFamilies.sansSerif,
    fontWeight: fontWeights.regular,
    fontSize: '16px',
    lineHeight: '24px',
  },
  logo: {
    fontFamily: fontFamilies.serif,
    fontWeight: fontWeights.regular,
    fontSize: '20px',
    lineHeight: '28px',
  },
  button: {
    fontFamily: fontFamilies.sansSerif,
    fontWeight: fontWeights.medium,
    fontSize: '16px',
    lineHeight: '24px',
  },
};

// 基线网格
export const baselineGrid = '8px';

// CSS 自定义属性导出
export const cssVariables = {
  '--font-serif': fontFamilies.serif,
  '--font-sans-serif': fontFamilies.sansSerif,
  '--font-mono': fontFamilies.mono,
  '--font-weight-regular': fontWeights.regular,
  '--font-weight-medium': fontWeights.medium,
  '--font-weight-bold': fontWeights.bold,
  '--baseline-grid': baselineGrid,
  
  // 标题样式
  '--h1-font-family': typography.h1.fontFamily,
  '--h1-font-weight': typography.h1.fontWeight,
  '--h1-font-size': typography.h1.fontSize,
  '--h1-line-height': typography.h1.lineHeight,
  
  '--h2-font-family': typography.h2.fontFamily,
  '--h2-font-weight': typography.h2.fontWeight,
  '--h2-font-size': typography.h2.fontSize,
  '--h2-line-height': typography.h2.lineHeight,
  
  '--h3-font-family': typography.h3.fontFamily,
  '--h3-font-weight': typography.h3.fontWeight,
  '--h3-font-size': typography.h3.fontSize,
  '--h3-line-height': typography.h3.lineHeight,
  
  // 正文样式
  '--body-font-family': typography.body.fontFamily,
  '--body-font-weight': typography.body.fontWeight,
  '--body-font-size': typography.body.fontSize,
  '--body-line-height': typography.body.lineHeight,
  
  // 其他样式
  '--blockquote-font-family': typography.blockquote.fontFamily,
  '--blockquote-font-weight': typography.blockquote.fontWeight,
  '--blockquote-font-style': typography.blockquote.fontStyle,
  '--blockquote-font-size': typography.blockquote.fontSize,
  '--blockquote-line-height': typography.blockquote.lineHeight,
  
  '--code-font-family': typography.code.fontFamily,
  '--code-font-weight': typography.code.fontWeight,
  '--code-font-size': typography.code.fontSize,
  '--code-line-height': typography.code.lineHeight,
  
  '--meta-font-family': typography.meta.fontFamily,
  '--meta-font-weight': typography.meta.fontWeight,
  '--meta-font-size': typography.meta.fontSize,
  '--meta-line-height': typography.meta.lineHeight,
};
