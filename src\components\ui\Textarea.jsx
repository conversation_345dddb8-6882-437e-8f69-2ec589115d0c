import React, { forwardRef } from 'react';
import clsx from 'clsx';
import './Textarea.css';

const Textarea = forwardRef(({
  placeholder,
  value,
  onChange,
  onFocus,
  onBlur,
  disabled = false,
  error = false,
  helperText,
  label,
  required = false,
  rows = 4,
  resize = 'vertical', // 'none' | 'vertical' | 'horizontal' | 'both'
  className,
  ...props
}, ref) => {
  const textareaClasses = clsx(
    'textarea',
    `textarea--resize-${resize}`,
    {
      'textarea--error': error,
      'textarea--disabled': disabled,
    },
    className
  );

  const textareaId = props.id || `textarea-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div className="textarea-wrapper">
      {label && (
        <label htmlFor={textareaId} className="textarea__label">
          {label}
          {required && <span className="textarea__required">*</span>}
        </label>
      )}
      
      <textarea
        ref={ref}
        id={textareaId}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onFocus={onFocus}
        onBlur={onBlur}
        disabled={disabled}
        required={required}
        rows={rows}
        className={textareaClasses}
        {...props}
      />
      
      {helperText && (
        <div className={`textarea__helper ${error ? 'textarea__helper--error' : ''}`}>
          {helperText}
        </div>
      )}
    </div>
  );
});

Textarea.displayName = 'Textarea';

export default Textarea;
