import React, { forwardRef } from 'react';
import clsx from 'clsx';
import './Input.css';

const Input = forwardRef(({
  type = 'text',
  placeholder,
  value,
  onChange,
  onFocus,
  onBlur,
  disabled = false,
  error = false,
  helperText,
  label,
  required = false,
  className,
  ...props
}, ref) => {
  const inputClasses = clsx(
    'input',
    {
      'input--error': error,
      'input--disabled': disabled,
    },
    className
  );

  const inputId = props.id || `input-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div className="input-wrapper">
      {label && (
        <label htmlFor={inputId} className="input__label">
          {label}
          {required && <span className="input__required">*</span>}
        </label>
      )}
      
      <input
        ref={ref}
        id={inputId}
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        onFocus={onFocus}
        onBlur={onBlur}
        disabled={disabled}
        required={required}
        className={inputClasses}
        {...props}
      />
      
      {helperText && (
        <div className={`input__helper ${error ? 'input__helper--error' : ''}`}>
          {helperText}
        </div>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export default Input;
